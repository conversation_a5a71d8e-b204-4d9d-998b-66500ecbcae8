const fs = require('fs');
const https = require('https');
const path = require('path');

const coreUrl = 'https://cubism.live2d.com/sdk-web/cubismcore/live2dcubismcore.min.js';
const outputDir = path.join(__dirname, '..', 'public', 'lib', 'live2d');
const outputFile = path.join(outputDir, 'live2dcubismcore.js');

// 确保目录存在
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

console.log('Downloading Live2D Cubism Core...');

const file = fs.createWriteStream(outputFile);

https.get(coreUrl, (response) => {
  if (response.statusCode === 200) {
    response.pipe(file);
    
    file.on('finish', () => {
      file.close();
      console.log('Live2D Cubism Core downloaded successfully to:', outputFile);
      
      // 验证文件大小
      const stats = fs.statSync(outputFile);
      console.log('File size:', Math.round(stats.size / 1024), 'KB');
      
      if (stats.size < 1000) {
        console.error('Warning: Downloaded file seems too small, may be corrupted');
        process.exit(1);
      }
    });
    
    file.on('error', (err) => {
      fs.unlink(outputFile, () => {}); // 删除错误文件
      console.error('File write error:', err.message);
      process.exit(1);
    });
  } else {
    console.error('HTTP Error:', response.statusCode, response.statusMessage);
    process.exit(1);
  }
}).on('error', (err) => {
  console.error('Download error:', err.message);
  process.exit(1);
}); 