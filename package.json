{"name": "prototype", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ant-design/icons": "^5.3.7", "@types/three": "^0.177.0", "ahooks": "^3.8.1", "ai": "^4.3.16", "antd": "^5.18.3", "canvas-confetti": "^1.9.3", "dayjs": "^1.11.11", "next": "15.3.4", "pixi-live2d-display": "^0.4.0", "pixi.js": "^8.9.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "three": "^0.177.0", "zustand": "^4.5.4"}, "devDependencies": {"@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar": "^0.4.9", "@stagewise/toolbar-next": "^0.4.9", "@types/node": "^20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}