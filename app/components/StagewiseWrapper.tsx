'use client'

import { useEffect } from 'react'

export default function StagewiseWrapper() {
  useEffect(() => {
    // 只在开发环境下初始化
    if (process.env.NODE_ENV === 'development') {
      // 动态导入并初始化 stagewise toolbar
      Promise.all([
        import('@stagewise/toolbar'),
        import('@stagewise-plugins/react')
      ]).then(([{ initToolbar }, { ReactPlugin }]) => {
        initToolbar({
          plugins: [ReactPlugin],
        })
        console.log('✅ Stagewise toolbar initialized with React plugin')
      }).catch((error) => {
        console.warn('Failed to load stagewise toolbar:', error)
      })
    }
  }, [])

  // 这个组件不渲染任何UI
  return null
} 