"use client";

import { useRouter, usePathname } from "next/navigation";
import {
  Layout,
  <PERSON>u,
  Button,
  <PERSON>po<PERSON>,
  Avatar,
  Dropdown,
  Popover,
  Space,
  Select,
  message,
  Menu as Antd<PERSON>enu,
  Badge,
  Grid,
  Drawer,
} from "antd";
import {
  CheckCircleOutlined,
  MessageOutlined,
  DatabaseOutlined,
  RobotOutlined,
  DownOutlined,
  SwapOutlined,
  UserOutlined,
  LogoutOutlined,
  CustomerServiceOutlined,
  AppstoreOutlined,
  GlobalOutlined,
  ToolOutlined,
  CodeOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { ReactNode } from "react";
import React from "react";

const { Sider } = Layout;
const { Text } = Typography;
const { useBreakpoint } = Grid;

const NAV_ITEMS = [
  {
    key: "evaluations",
    label: "Evaluations",
    path: "/cs-system",
  },
  {
    key: "expert-consultations",
    label: "Expert Consults",
    path: "/cs-system/expert-consults",
  },
];

const SYSTEM_ITEMS = [
  { key: 'cs', label: 'CS', icon: <CustomerServiceOutlined />, path: '/cs-system', description: 'Customer Service System' },
  { key: 'd', label: 'D', icon: <DatabaseOutlined />, path: '/d-system', description: 'Doraemon System' },
  { key: 'a', label: 'A', icon: <AppstoreOutlined />, path: '/a-system', description: 'Assignment Management' },
  { key: 'w', label: 'W', icon: <GlobalOutlined />, path: '/w-system', description: 'Workspace Collaboration' },
  { key: 'sdk', label: 'SDK', icon: <CodeOutlined />, path: '/sdk', description: 'SDK Integration Demo' },
];

const SUB_FEATURE_ITEMS = [
  { key: 'ai-agents', label: 'AI Agents', icon: <RobotOutlined /> },
  { key: 'manual', label: 'Manual', icon: <UserOutlined /> },
  { key: 'tools', label: 'Tools', icon: <ToolOutlined /> },
];

const preferredLanguageOptions = [
  { value: 'en', label: 'English', icon: <span>🇺🇸</span> },
  { value: 'zh-CN', label: '中文', icon: <span>🇨🇳</span> },
  { value: 'ja', label: '日本語', icon: <span>🇯🇵</span> },
];

interface SideNavProps {
  activeMenu: string;
  systemTitle?: string;
  userName?: string;
  userEmail?: string;
  preferredLanguage?: string;
  onLanguageChange?: (lang: string) => void;
  hasWaitingConsults?: boolean;
  mobileDrawerVisible?: boolean;
  onMobileDrawerClose?: () => void;
}

export default function SideNav({
  activeMenu,
  systemTitle = "G123 CS",
  userName = "Support Agent",
  userEmail = "<EMAIL>",
  preferredLanguage,
  onLanguageChange,
  activeSubFeature,
  onSubFeatureChange,
  hasWaitingConsults = false,
  mobileDrawerVisible = false,
  onMobileDrawerClose,
}: SideNavProps & { activeSubFeature?: string; onSubFeatureChange?: (key: string) => void }) {
  const router = useRouter();
  const pathname = usePathname();
  const screens = useBreakpoint();
  const isMobile = !screens.md; // screens.md is false on mobile (< 768px)
  const [lang, setLang] = React.useState(preferredLanguage || 'en');

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('preferredLanguage');
      if (saved) setLang(saved);
    }
  }, []);

  const handleLangChange = (value: string) => {
    setLang(value);
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferredLanguage', value);
      message.success('Language preference saved');
    }
    if (onLanguageChange) onLanguageChange(value);
  };

  // 跳转逻辑
  const handleMenuClick = (e: any) => {
    const item = NAV_ITEMS.find((i) => i.key === e.key);
    if (item && pathname !== item.path) {
      router.push(item.path);
    }
  };

  // 子功能切换
  const handleSubFeatureClick = (key: string) => {
    if (onSubFeatureChange) onSubFeatureChange(key);
  };

  // 系统切换内容
  const systemSwitchContent = (
    <div style={{ width: 250 }}>
      {SYSTEM_ITEMS.map(item => (
        <div
          key={item.key}
          onClick={() => {
            if (pathname !== item.path) router.push(item.path);
          }}
          style={{
            padding: '12px 16px',
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            borderRadius: 6,
            marginBottom: 8,
            background: pathname === item.path ? '#f0f7ff' : 'transparent',
            border: pathname === item.path ? '1px solid #d6e4ff' : '1px solid transparent',
          }}
        >
          <div style={{
            width: 40,
            height: 40,
            borderRadius: '50%',
            background: pathname === item.path ? '#e6f4ff' : '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
            color: pathname === item.path ? '#1677ff' : '#666',
          }}>
            {item.icon}
          </div>
          <div>
            <div style={{ fontWeight: 500, fontSize: 16 }}>{item.label}</div>
            <div style={{ fontSize: 12, color: '#666' }}>{item.description}</div>
          </div>
        </div>
      ))}
    </div>
  );

  // 登出菜单
  const logoutMenu = (
    <AntdMenu>
      {/* 语言选择选项 */}
      <AntdMenu.ItemGroup title="Preferred Language">
        {preferredLanguageOptions.map(option => (
          <AntdMenu.Item 
            key={`lang-${option.value}`} 
            icon={option.icon}
            onClick={() => handleLangChange(option.value)}
            style={{
              backgroundColor: lang === option.value ? '#e6f7ff' : 'transparent',
              color: lang === option.value ? '#1677ff' : 'inherit',
              fontWeight: lang === option.value ? 'bold' : 'normal',
            }}
          >
            {option.label}
          </AntdMenu.Item>
        ))}
      </AntdMenu.ItemGroup>
      <AntdMenu.Divider />
      <AntdMenu.Item key="logout" danger icon={<LogoutOutlined />} onClick={() => router.push("/")}>
        Sign Out
      </AntdMenu.Item>
    </AntdMenu>
  );

  // 为导航项添加徽章
  const getNavItemsWithBadges = () => {
    return NAV_ITEMS.map(item => {
      // 移除所有徽章显示，直接返回原始项目
      return item;
    });
  };

  // 完全定制菜单项，自己控制渲染，不使用前缀图标
  const renderMenuItems = () => {
    const items = getNavItemsWithBadges().map(item => {
      return {
        key: item.key,
        label: item.label,
        onClick: () => {
          const navItem = NAV_ITEMS.find(i => i.key === item.key);
          if (navItem && pathname !== navItem.path) {
            router.push(navItem.path);
          }
        }
      };
    });
    
    return items;
  };

  // 侧边栏内容组件
  const sidebarContent = (
    <>
      <div
        style={{
          height: 64,
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-start",
          padding: "0 16px",
          borderBottom: "1px solid #f0f0f0",
          background: "#f8f9fa",
        }}
      >
        <Text strong style={{ fontSize: 18, color: "#6366f1" }}>
          {systemTitle}
        </Text>
        <Popover
          content={systemSwitchContent}
          trigger="click"
          placement="bottomRight"
          overlayStyle={{ width: 280 }}
        >
          <Button
            type="text"
            icon={<SwapOutlined />}
            style={{ marginLeft: 8, color: "#6366f1" }}
          />
        </Popover>
      </div>

      <div style={{ padding: '16px 8px', display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px)' }}>
        {/* CS系统子功能切换，下拉按钮样式，统一与D系统 */}
        {pathname.startsWith('/cs-system') && (
          <>
            <Dropdown
              menu={{
                items: SUB_FEATURE_ITEMS.map(item => ({
                  key: item.key,
                  icon: item.icon,
                  label: item.label,
                  onClick: () => onSubFeatureChange && onSubFeatureChange(item.key)
                })),
                selectable: true,
                selectedKeys: [activeSubFeature || 'ai-agents']
              }}
              trigger={['click']}
            >
              <Button
                style={{
                  width: '100%',
                  textAlign: 'left',
                  height: 40,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  borderRadius: 8,
                  border: '1px solid #e2e8f0',
                  background: '#fff'
                }}
              >
                <Space>
                  {SUB_FEATURE_ITEMS.find(i => (activeSubFeature || 'ai-agents') === i.key)?.icon}
                  {SUB_FEATURE_ITEMS.find(i => (activeSubFeature || 'ai-agents') === i.key)?.label}
                </Space>
                <DownOutlined style={{ fontSize: 12 }} />
              </Button>
            </Dropdown>
            
            <div style={{ margin: '16px 0', borderBottom: '1px solid #e8e8e8' }}></div>
          </>
        )}

        {/* 菜单导航 - 自定义渲染，不使用图标前缀 */}
        <Menu
          mode="inline"
          selectedKeys={[activeMenu]}
          style={{ 
            borderRight: 0, 
            background: '#f5f5f5',
            width: '100%'
          }}
          items={renderMenuItems()}
          className="side-nav-menu"
        />

        {/* 添加全局样式 */}
        <style jsx global>{`
          .side-nav-menu .ant-menu-item {
            padding: 0 8px !important;
            display: flex !important;
            align-items: center !important;
            height: 40px !important;
            white-space: nowrap !important;
          }
          .side-nav-menu .ant-menu-title-content {
            width: 100% !important;
            overflow: visible !important;
            white-space: nowrap !important;
          }
          .side-nav-menu .ant-menu-title-content > span {
            display: flex !important;
            align-items: center !important;
            width: 100% !important;
          }
        `}</style>

        {/* 个人信息与登出 */}
        <div style={{ marginTop: 'auto', padding: '16px 8px', borderTop: '1px solid #e8e8e8' }}>
          <Dropdown menu={{ items: logoutMenu.props.children.map((item: any) => ({ key: item.key, label: item.props.children, onClick: item.props.onClick, danger: item.props.danger, icon: item.props.icon })) }} trigger={["hover"]} placement="topRight" arrow>
            <div style={{ 
              display: "flex", 
              alignItems: "center",
              padding: '8px',
              borderRadius: '6px',
              cursor: 'pointer',
              backgroundColor: 'transparent'
            }}>
              <Avatar icon={<UserOutlined />} />
              <div style={{ marginLeft: 8, flex: 1 }}>
                <Text strong style={{ display: "block", lineHeight: "1.2" }}>{userName}</Text>
                <Text type="secondary" style={{ fontSize: 12 }}>{userEmail}</Text>
              </div>
            </div>
          </Dropdown>
        </div>
      </div>
    </>
  );

  // 移动端返回 Drawer，桌面端返回 Sider
  if (isMobile) {
    return (
      <Drawer
        title=""
        placement="left"
        width={220}
        onClose={onMobileDrawerClose}
        open={mobileDrawerVisible}
        styles={{ 
          body: { padding: 0, background: "#f5f5f5" },
          header: { display: 'none' }
        }}
      >
        {sidebarContent}
      </Drawer>
    );
  }

  return (
    <Sider width={220} style={{ background: "#f5f5f5" }}>
      {sidebarContent}
    </Sider>
  );
} 