'use client';

import React from 'react';
import Image from 'next/image';

interface AttachmentType {
  type: 'image' | 'file';
  url: string;
  name?: string;
  size?: string;
}

interface UserMessageProps {
  content: string;
  timestamp?: string;
  avatar?: string;
  attachments?: AttachmentType[];
  theme: 'light' | 'dark';
  colors: {
    bgMain: string;
    bgCard: string;
    bgElevated: string;
    bgSubtle: string;
    textPrimary: string;
    textSecondary: string;
    textTertiary: string;
    borderSubtle: string;
    borderStrong: string;
    primary: string;
    primaryLight: string;
    accent: string;
    success: string;
    error: string;
  };
}

export default function UserMessage({ 
  content, 
  timestamp, 
  avatar,
  attachments = [],
  theme,
  colors
}: UserMessageProps) {
  return (
    <div style={{ 
      display: 'flex',
      flexDirection: 'row-reverse',
      marginBottom: '24px',
    }}>
      {/* 头像 */}
      <div style={{ 
        width: '36px',
        height: '36px',
        borderRadius: '50%',
        overflow: 'hidden',
        marginLeft: '12px',
        flexShrink: 0,
        backgroundColor: colors.primary,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontWeight: 'bold',
        fontSize: '16px'
      }}>
        {avatar ? (
          <Image 
            src={avatar} 
            alt="User Avatar" 
            width={36} 
            height={36}
          />
        ) : 'U'}
      </div>
      
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'flex-end'
      }}>
        {/* 消息内容 */}
        <div style={{ 
          backgroundColor: colors.primary,
          color: 'white',
          borderRadius: '12px 12px 0 12px',
          padding: '16px',
          fontSize: '14px',
          lineHeight: 1.5,
          maxWidth: '80%',
          overflowWrap: 'break-word',
          marginBottom: '4px'
        }}>
          {content}
        </div>
        
        {/* 附件 */}
        {attachments.length > 0 && (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            gap: '8px',
            maxWidth: '80%',
            marginBottom: '8px'
          }}>
            {attachments.map((attachment, index) => (
              attachment.type === 'image' ? (
                <div key={index} style={{ 
                  borderRadius: '8px', 
                  overflow: 'hidden',
                  border: `1px solid ${colors.borderSubtle}`,
                  width: '200px',
                  height: '150px',
                  position: 'relative'
                }}>
                  <Image 
                    src={attachment.url} 
                    alt={attachment.name || 'Attachment'} 
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              ) : (
                <div key={index} style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  backgroundColor: colors.bgElevated, 
                  padding: '8px 12px', 
                  borderRadius: '8px',
                  alignSelf: 'flex-end'
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke={colors.textSecondary} strokeWidth="2" style={{ marginRight: '8px' }}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div>
                    <div style={{ fontWeight: 500, color: colors.textPrimary }}>
                      {attachment.name || 'File'}
                    </div>
                    {attachment.size && (
                      <div style={{ fontSize: '12px', color: colors.textTertiary }}>
                        {attachment.size}
                      </div>
                    )}
                  </div>
                </div>
              )
            ))}
          </div>
        )}
        
        {/* 时间戳 */}
        {timestamp && (
          <div style={{ 
            fontSize: '12px', 
            color: colors.textTertiary, 
            marginRight: '8px'
          }}>
            {timestamp}
          </div>
        )}
      </div>
    </div>
  );
} 