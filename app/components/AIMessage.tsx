'use client';

import React from 'react';
import Image from 'next/image';

interface MessageContent {
  type: 'text' | 'image' | 'file' | 'code' | 'tool-call' | 'dynamic-card';
  content: string | any;
  language?: string; // 代码语言，用于代码高亮
  filename?: string; // 文件名称
  result?: string; // 工具调用结果
  title?: string; // 动态卡片标题
}

interface AIMessageProps {
  content: MessageContent[];
  avatar?: string;
  timestamp?: string;
  isLoading?: boolean;
  theme: 'light' | 'dark';
  colors: {
    bgMain: string;
    bgCard: string;
    bgElevated: string;
    bgSubtle: string;
    textPrimary: string;
    textSecondary: string;
    textTertiary: string;
    borderSubtle: string;
    borderStrong: string;
    primary: string;
    primaryLight: string;
    accent: string;
    success: string;
    error: string;
  };
}

export default function AIMessage({ 
  content, 
  avatar = '/ai-avatar.png', 
  timestamp, 
  isLoading = false,
  theme,
  colors
}: AIMessageProps) {
  return (
    <div style={{ 
      display: 'flex',
      marginBottom: '32px',
    }}>
      {/* 头像 */}
      <div style={{ 
        width: '40px',
        height: '40px',
        borderRadius: '50%',
        overflow: 'hidden',
        marginRight: '16px',
        flexShrink: 0,
        backgroundColor: colors.primary,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: '16px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
      }}>
        {avatar ? (
          <Image 
            src={avatar} 
            alt="AI Avatar" 
            width={40} 
            height={40}
          />
        ) : 'AI'}
      </div>
      
      <div style={{ flex: 1 }}>
        {/* 消息内容 */}
        <div style={{ 
          backgroundColor: colors.bgElevated,
          borderRadius: '14px',
          padding: '18px',
          color: colors.textPrimary,
          fontSize: '15px',
          lineHeight: 1.6,
          maxWidth: '100%',
          overflowWrap: 'break-word',
          marginBottom: '6px',
          boxShadow: theme === 'light' 
            ? '0 2px 8px rgba(0, 0, 0, 0.05)' 
            : '0 2px 8px rgba(0, 0, 0, 0.2)'
        }}>
          {isLoading ? (
            // 加载中动画
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '6px'
              }}>
                {[1, 2, 3].map((_, i) => (
                  <div 
                    key={i}
                    style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: colors.primary,
                      animation: 'pulse 1.5s infinite',
                      animationDelay: `${i * 0.2}s`,
                    }}
                  />
                ))}
              </div>
              <style jsx>{`
                @keyframes pulse {
                  0%, 100% { opacity: 0.4; transform: scale(0.8); }
                  50% { opacity: 1; transform: scale(1.2); }
                }
              `}</style>
            </div>
          ) : (
            // 渲染各种内容类型
            content.map((item, index) => {
              switch (item.type) {
                case 'text':
                  // 处理Markdown风格的文本格式化
                  return (
                    <div 
                      key={index} 
                      style={{ 
                        marginBottom: index < content.length - 1 ? '16px' : '0'
                      }}
                    >
                      {formatText(item.content, colors)}
                    </div>
                  );
                
                case 'image':
                  return (
                    <div key={index} style={{ marginBottom: index < content.length - 1 ? '16px' : '0' }}>
                      <Image 
                        src={item.content} 
                        alt="Image in message" 
                        width={300} 
                        height={200} 
                        style={{ 
                          maxWidth: '100%', 
                          height: 'auto', 
                          borderRadius: '10px',
                          marginTop: '10px',
                          marginBottom: '10px',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                        }} 
                      />
                    </div>
                  );
                
                case 'file':
                  return (
                    <div key={index} 
                      style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        backgroundColor: colors.bgSubtle, 
                        padding: '10px 14px', 
                        borderRadius: '10px',
                        marginBottom: index < content.length - 1 ? '16px' : '0',
                        border: `1px solid ${colors.borderSubtle}`
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" fill="none" stroke={colors.primary} strokeWidth="2" style={{ marginRight: '10px' }}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <div style={{ flex: 1, overflow: 'hidden' }}>
                        <div style={{ fontWeight: 500, color: colors.textPrimary, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {item.filename || 'File'}
                        </div>
                        <div style={{ fontSize: '12px', color: colors.textTertiary }}>
                          {item.content}
                        </div>
                      </div>
                      <a 
                        href={item.content} 
                        download={item.filename}
                        style={{
                          marginLeft: '8px',
                          color: colors.primary,
                          cursor: 'pointer',
                          padding: '6px',
                          borderRadius: '6px',
                          backgroundColor: `${colors.primary}15`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                      </a>
                    </div>
                  );
                
                case 'code':
                  return (
                    <div key={index} style={{ marginBottom: index < content.length - 1 ? '16px' : '0' }}>
                      <div style={{ 
                        backgroundColor: theme === 'dark' ? '#1E1E1E' : '#F5F5F5',
                        color: theme === 'dark' ? '#D4D4D4' : '#333333',
                        padding: '16px',
                        borderRadius: '10px',
                        fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
                        fontSize: '14px',
                        overflowX: 'auto',
                        position: 'relative',
                        border: theme === 'dark' ? '1px solid #333' : '1px solid #E0E0E0'
                      }}>
                        {item.language && (
                          <div style={{ 
                            position: 'absolute',
                            top: '8px',
                            right: '12px',
                            fontSize: '12px',
                            color: colors.textTertiary,
                            backgroundColor: theme === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(255,255,255,0.8)',
                            padding: '3px 8px',
                            borderRadius: '6px',
                            fontWeight: '500'
                          }}>
                            {item.language}
                          </div>
                        )}
                        <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                          {item.content}
                        </pre>
                      </div>
                    </div>
                  );
                
                case 'tool-call':
                  return (
                    <div key={index} style={{ marginBottom: index < content.length - 1 ? '16px' : '0' }}>
                      <div style={{ 
                        backgroundColor: theme === 'dark' ? '#1C2833' : '#F0F7FA',
                        color: theme === 'dark' ? '#A3B1C6' : '#2C3E50',
                        padding: '12px 16px',
                        borderRadius: '10px',
                        fontSize: '14px',
                        border: theme === 'dark' ? '1px solid #2C3E50' : '1px solid #D6EAF8',
                        position: 'relative'
                      }}>
                        <div style={{ 
                          display: 'flex',
                          alignItems: 'center',
                          marginBottom: '8px',
                          color: theme === 'dark' ? '#3498DB' : '#2980B9',
                          fontWeight: '500'
                        }}>
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" style={{ marginRight: '8px' }}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 006 3.75v16.5a2.25 2.25 0 002.25 2.25h7.5A2.25 2.25 0 0018 20.25V3.75a2.25 2.25 0 00-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
                          </svg>
                          Tool Call
                        </div>
                        <div style={{ 
                          fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
                          fontSize: '13px',
                          marginBottom: '8px',
                          wordBreak: 'break-word'
                        }}>
                          {item.content}
                        </div>
                        {item.result && (
                          <div style={{ 
                            marginTop: '8px',
                            borderTop: theme === 'dark' ? '1px dashed #2C3E50' : '1px dashed #BDC3C7',
                            paddingTop: '8px'
                          }}>
                            <div style={{ 
                              fontSize: '12px',
                              color: theme === 'dark' ? '#7F8C8D' : '#7F8C8D',
                              marginBottom: '4px'
                            }}>
                              Result:
                            </div>
                            <div style={{ 
                              color: theme === 'dark' ? '#2ECC71' : '#27AE60',
                              fontSize: '13px'
                            }}>
                              {item.result}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                
                case 'dynamic-card':
                  return (
                    <div key={index} style={{ marginBottom: index < content.length - 1 ? '16px' : '0' }}>
                      <div style={{ 
                        backgroundColor: colors.bgCard,
                        borderRadius: '12px',
                        padding: '16px',
                        border: `1px solid ${colors.borderSubtle}`,
                        boxShadow: theme === 'light' ? '0 2px 8px rgba(0, 0, 0, 0.05)' : '0 2px 8px rgba(0, 0, 0, 0.2)'
                      }}>
                        {/* 卡片标题 */}
                        {item.title && (
                          <div style={{ 
                            fontWeight: '600',
                            fontSize: '16px',
                            marginBottom: '12px',
                            color: colors.textPrimary,
                            borderBottom: `1px solid ${colors.borderSubtle}`,
                            paddingBottom: '8px',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                          }}>
                            <span>{item.title}</span>
                            {item.content?.type === 'data-table' && (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke={colors.primary} strokeWidth="2">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            )}
                            {item.content?.type === 'timeline' && (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke={colors.primary} strokeWidth="2">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                            )}
                          </div>
                        )}
                        
                        {/* 渲染数据表格 */}
                        {item.content?.type === 'data-table' && (
                          <div>
                            <div style={{ 
                              display: 'table',
                              width: '100%',
                              borderCollapse: 'collapse' as 'collapse',
                              fontSize: '14px',
                              marginBottom: '12px'
                            }}>
                              {/* 表头 */}
                              <div style={{ display: 'table-header-group' }}>
                                <div style={{ display: 'table-row' }}>
                                  {item.content.columns.map((column: string, colIndex: number) => (
                                    <div 
                                      key={colIndex} 
                                      style={{ 
                                        display: 'table-cell',
                                        padding: '8px 12px',
                                        fontWeight: '600',
                                        borderBottom: `2px solid ${colors.borderStrong}`,
                                        color: colors.textPrimary,
                                        textAlign: colIndex === 0 ? 'left' : 'right'
                                      }}
                                    >
                                      {column}
                                    </div>
                                  ))}
                                </div>
                              </div>
                              {/* 表体 */}
                              <div style={{ display: 'table-row-group' }}>
                                {item.content.rows.map((row: string[], rowIndex: number) => (
                                  <div 
                                    key={rowIndex} 
                                    style={{ 
                                      display: 'table-row',
                                      backgroundColor: rowIndex % 2 === 1 ? (theme === 'light' ? '#F9FAFB' : '#1A202C') : 'transparent'
                                    }}
                                  >
                                    {row.map((cell, cellIndex) => (
                                      <div 
                                        key={cellIndex} 
                                        style={{ 
                                          display: 'table-cell',
                                          padding: '8px 12px',
                                          borderBottom: `1px solid ${colors.borderSubtle}`,
                                          color: cellIndex === 0 ? colors.textPrimary : 
                                                cell.startsWith('+') ? colors.success :
                                                cell.startsWith('-') ? colors.error :
                                                colors.textSecondary,
                                          fontWeight: cellIndex === 0 ? '500' : 'normal',
                                          textAlign: cellIndex === 0 ? 'left' : 'right'
                                        }}
                                      >
                                        {cell}
                                      </div>
                                    ))}
                                  </div>
                                ))}
                              </div>
                            </div>
                            
                            {/* 操作按钮 */}
                            {item.content.actions && item.content.actions.length > 0 && (
                              <div style={{ 
                                display: 'flex',
                                justifyContent: 'flex-end',
                                gap: '8px',
                                marginTop: '12px'
                              }}>
                                {item.content.actions.map((action: any, actionIndex: number) => (
                                  <button
                                    key={actionIndex}
                                    style={{
                                      padding: '6px 12px',
                                      backgroundColor: actionIndex === 0 ? colors.primary : 'transparent',
                                      color: actionIndex === 0 ? 'white' : colors.textSecondary,
                                      border: actionIndex === 0 ? 'none' : `1px solid ${colors.borderStrong}`,
                                      borderRadius: '6px',
                                      fontSize: '13px',
                                      fontWeight: '500',
                                      cursor: 'pointer'
                                    }}
                                  >
                                    {action.label}
                                  </button>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                        
                        {/* 渲染时间线 */}
                        {item.content?.type === 'timeline' && (
                          <div>
                            <div style={{ 
                              display: 'flex',
                              flexDirection: 'column' as 'column',
                              gap: '4px',
                              marginBottom: '16px'
                            }}>
                              {item.content.phases.map((phase: any, phaseIndex: number) => (
                                <div 
                                  key={phaseIndex} 
                                  style={{ 
                                    display: 'flex',
                                    alignItems: 'flex-start',
                                    padding: '10px',
                                    borderRadius: '8px',
                                    backgroundColor: theme === 'light' ? '#F9FAFB' : '#1A202C',
                                    borderLeft: `3px solid ${
                                      phase.status === 'completed' ? colors.success :
                                      phase.status === 'active' ? colors.primary :
                                      colors.borderStrong
                                    }`
                                  }}
                                >
                                  <div style={{ 
                                    width: '24px',
                                    height: '24px',
                                    borderRadius: '50%',
                                    backgroundColor: 
                                      phase.status === 'completed' ? `${colors.success}20` :
                                      phase.status === 'active' ? `${colors.primary}20` :
                                      `${colors.textTertiary}20`,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginRight: '12px',
                                    flexShrink: 0
                                  }}>
                                    <div style={{ 
                                      width: '10px',
                                      height: '10px',
                                      borderRadius: '50%',
                                      backgroundColor: 
                                        phase.status === 'completed' ? colors.success :
                                        phase.status === 'active' ? colors.primary :
                                        colors.textTertiary
                                    }}></div>
                                  </div>
                                  <div style={{ flex: 1 }}>
                                    <div style={{ 
                                      fontWeight: '500',
                                      color: colors.textPrimary,
                                      marginBottom: '4px'
                                    }}>
                                      {phase.name}
                                    </div>
                                    <div style={{ 
                                      display: 'flex',
                                      alignItems: 'center',
                                      fontSize: '12px',
                                      color: colors.textTertiary,
                                      gap: '12px'
                                    }}>
                                      <span>{phase.duration}</span>
                                      <span style={{ 
                                        fontSize: '10px',
                                        padding: '2px 8px',
                                        borderRadius: '12px',
                                        backgroundColor: 
                                          phase.status === 'completed' ? `${colors.success}20` :
                                          phase.status === 'active' ? `${colors.primary}20` :
                                          `${colors.textTertiary}20`,
                                        color: 
                                          phase.status === 'completed' ? colors.success :
                                          phase.status === 'active' ? colors.primary :
                                          colors.textTertiary
                                      }}>
                                        {phase.status.charAt(0).toUpperCase() + phase.status.slice(1)}
                                      </span>
                                    </div>
                                  </div>
                                  <div style={{ 
                                    fontSize: '12px',
                                    color: colors.textSecondary,
                                    textAlign: 'right'
                                  }}>
                                    {new Date(phase.startDate).toLocaleDateString('en-US', {month: 'short', day: 'numeric'})} - 
                                    {new Date(phase.endDate).toLocaleDateString('en-US', {month: 'short', day: 'numeric'})}
                                  </div>
                                </div>
                              ))}
                            </div>
                            
                            {/* 操作按钮 */}
                            {item.content.actions && item.content.actions.length > 0 && (
                              <div style={{ 
                                display: 'flex',
                                justifyContent: 'flex-end',
                                gap: '8px'
                              }}>
                                {item.content.actions.map((action: any, actionIndex: number) => (
                                  <button
                                    key={actionIndex}
                                    style={{
                                      padding: '6px 12px',
                                      backgroundColor: actionIndex === 0 ? colors.primary : 'transparent',
                                      color: actionIndex === 0 ? 'white' : colors.textSecondary,
                                      border: actionIndex === 0 ? 'none' : `1px solid ${colors.borderStrong}`,
                                      borderRadius: '6px',
                                      fontSize: '13px',
                                      fontWeight: '500',
                                      cursor: 'pointer'
                                    }}
                                  >
                                    {action.label}
                                  </button>
                                ))}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                
                default:
                  return null;
              }
            })
          )}
        </div>
        
        {/* 时间戳 */}
        {timestamp && (
          <div style={{ 
            fontSize: '12px', 
            color: colors.textTertiary,
            marginLeft: '10px',
            fontWeight: '500'
          }}>
            {timestamp}
          </div>
        )}
      </div>
    </div>
  );
}

// 格式化文本函数：处理Markdown风格标记
function formatText(text: string, colors: any) {
  // 处理多行文本，分割成段落
  const paragraphs = text.split('\n\n');
  
  return (
    <div>
      {paragraphs.map((paragraph, paragraphIndex) => {
        // 处理标题（**标题**）
        if (paragraph.match(/^\*\*.*\*\*$/)) {
          const title = paragraph.replace(/^\*\*|\*\*$/g, '');
          return (
            <div key={paragraphIndex} style={{ 
              fontWeight: 'bold', 
              fontSize: '16px', 
              borderBottom: `1px solid ${colors.borderSubtle}`,
              paddingBottom: '8px',
              marginBottom: '12px',
              color: colors.primary
            }}>
              {title}
            </div>
          );
        }
        
        // 处理代码块（```...```）
        if (paragraph.match(/^```[\s\S]*```$/)) {
          const codeContent = paragraph.replace(/^```|```$/g, '');
          return (
            <div key={paragraphIndex} style={{ 
              backgroundColor: colors.bgSubtle,
              border: `1px solid ${colors.borderSubtle}`,
              borderRadius: '8px',
              padding: '12px',
              fontFamily: 'monospace',
              fontSize: '13px',
              marginBottom: '12px',
              overflow: 'auto'
            }}>
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>{codeContent}</pre>
            </div>
          );
        }
        
        // 处理引用块（> ...）
        if (paragraph.match(/^>/)) {
          const quoteContent = paragraph.replace(/^>\s?/gm, '');
          return (
            <div key={paragraphIndex} style={{
              borderLeft: `4px solid ${colors.primary}`,
              paddingLeft: '12px',
              marginLeft: '8px',
              marginBottom: '12px',
              fontStyle: 'italic',
              color: colors.textSecondary
            }}>
              {parseInlineMarkdown(quoteContent, colors)}
            </div>
          );
        }
        
        // 处理列表项
        if (paragraph.match(/^[\d+\.\s|•\*\-\s]/m)) {
          const lines = paragraph.split('\n');
          return (
            <div key={paragraphIndex} style={{ marginBottom: '12px' }}>
              {lines.map((line, lineIndex) => {
                // 数字列表
                const numberMatch = line.match(/^(\d+)\.\s+(.*)/);
                // 项目符号列表
                const bulletMatch = line.match(/^([•*-])\s+(.*)/);
                
                if (numberMatch) {
                  const [, number, content] = numberMatch;
                  return (
                    <div key={lineIndex} style={{ 
                      display: 'flex', 
                      marginBottom: '6px',
                      alignItems: 'flex-start'
                    }}>
                      <div style={{ 
                        minWidth: '24px',
                        height: '20px',
                        borderRadius: '10px',
                        backgroundColor: `${colors.primary}20`,
                        color: colors.primary,
                        fontWeight: 'bold',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '8px',
                        fontSize: '12px'
                      }}>
                        {number}
                      </div>
                      <div style={{ flex: 1, lineHeight: '1.4' }}>
                        {parseInlineMarkdown(content, colors)}
                      </div>
                    </div>
                  );
                } else if (bulletMatch) {
                  const [, bullet, content] = bulletMatch;
                  return (
                    <div key={lineIndex} style={{ 
                      display: 'flex', 
                      marginBottom: '6px',
                      alignItems: 'flex-start'
                    }}>
                      <div style={{ 
                        width: '6px',
                        height: '6px',
                        borderRadius: '50%',
                        backgroundColor: colors.primary,
                        marginRight: '8px',
                        marginTop: '7px',
                        flexShrink: 0
                      }} />
                      <div style={{ flex: 1, lineHeight: '1.4' }}>
                        {parseInlineMarkdown(content, colors)}
                      </div>
                    </div>
                  );
                } else if (line.trim()) {
                  return <div key={lineIndex} style={{ marginBottom: '6px' }}>{parseInlineMarkdown(line, colors)}</div>;
                }
                return null;
              })}
            </div>
          );
        }
        
        // 普通段落
        if (paragraph.trim()) {
          return (
            <div key={paragraphIndex} style={{ marginBottom: '12px', lineHeight: '1.5' }}>
              {parseInlineMarkdown(paragraph, colors)}
            </div>
          );
        }
        
        return null;
      })}
    </div>
  );
}

// 解析行内markdown（粗体、斜体、链接、内联代码等）
function parseInlineMarkdown(text: string, colors: any) {
  // 处理链接 [text](url)
  text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, linkText, url) => {
    return `<a href="${url}" style="color: ${colors.primary}; text-decoration: underline;" target="_blank" rel="noopener noreferrer">${linkText}</a>`;
  });
  
  // 处理内联代码 `code`
  text = text.replace(/`([^`]+)`/g, (match, code) => {
    return `<code style="background-color: ${colors.bgSubtle}; padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 0.9em;">${code}</code>`;
  });
  
  // 处理粗体 **text**
  text = text.replace(/\*\*([^*]+)\*\*/g, (match, boldText) => {
    return `<strong style="font-weight: 600;">${boldText}</strong>`;
  });
  
  // 处理斜体 *text*
  text = text.replace(/\*([^*]+)\*/g, (match, italicText) => {
    return `<em style="font-style: italic;">${italicText}</em>`;
  });
  
  // 使用dangerouslySetInnerHTML渲染HTML
  return <span dangerouslySetInnerHTML={{ __html: text }} />;
} 