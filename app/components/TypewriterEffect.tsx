'use client';

import React, { useState, useEffect } from 'react';

interface TypewriterEffectProps {
  useCases: string[];
  textColor: string;
  cursorColor?: string;
  typingSpeed?: number;
  pauseDuration?: number;
}

export default function TypewriterEffect({
  useCases,
  textColor,
  cursorColor = textColor,
  typingSpeed = 80,
  pauseDuration = 1500
}: TypewriterEffectProps) {
  const [displayText, setDisplayText] = useState('');
  const [caseIndex, setCaseIndex] = useState(0);
  const [charIndex, setCharIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [cursorVisible, setCursorVisible] = useState(true);

  // 处理打字机效果逻辑
  useEffect(() => {
    // 当前展示的用例
    const currentCase = useCases[caseIndex];

    // 光标闪烁效果
    const cursorInterval = setInterval(() => {
      setCursorVisible(prev => !prev);
    }, 530);

    // 打字效果定时器
    const typeInterval = setTimeout(() => {
      // 如果正在删除文本
      if (isDeleting) {
        setDisplayText(currentCase.substring(0, charIndex - 1));
        setCharIndex(prev => prev - 1);
        
        // 当删除完成时，切换到下一个用例
        if (charIndex <= 1) {
          setIsDeleting(false);
          setCaseIndex((caseIndex + 1) % useCases.length);
          setCharIndex(0);
          setDisplayText('');
        }
      } 
      // 如果正在输入文本
      else {
        setDisplayText(currentCase.substring(0, charIndex + 1));
        setCharIndex(prev => prev + 1);
        
        // 当输入完成时，等待一段时间后开始删除
        if (charIndex >= currentCase.length) {
          setTimeout(() => {
            setIsDeleting(true);
          }, pauseDuration);
        }
      }
    }, isDeleting ? typingSpeed / 2 : typingSpeed);

    // 清理定时器
    return () => {
      clearTimeout(typeInterval);
      clearInterval(cursorInterval);
    };
  }, [caseIndex, charIndex, isDeleting, useCases, typingSpeed, pauseDuration]);

  return (
    <div style={{ 
      fontFamily: 'monospace', 
      fontSize: '1.1rem', 
      color: textColor,
      display: 'flex',
      minHeight: '2rem',
      alignItems: 'center'
    }}>
      <span>&quot;I want to </span>
      <span>{displayText}</span>
      <span 
        style={{ 
          opacity: cursorVisible ? 1 : 0,
          color: cursorColor,
          fontWeight: 'bold',
          marginLeft: displayText.length === 0 ? '0' : '2px'
        }}
      >|</span>
      <span>&quot;</span>
    </div>
  );
} 