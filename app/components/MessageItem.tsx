'use client';

import React from 'react';
import { Button } from 'antd';
import { CheckOutlined } from '@ant-design/icons';
import MediaMessage from './MediaMessage';

interface MessageItemProps {
  role: string;
  content: string;
  time: string;
  type?: 'text' | 'image' | 'video' | 'file';
  preview?: string;
  filename?: string;
  filesize?: string;
  width?: number;
  height?: number;
  translatedContent?: string;
  userName?: string;
  needsConfirmation?: boolean;
  isConfirmation?: boolean;
  onConfirm?: () => void;
}

const MessageItem: React.FC<MessageItemProps> = ({
  role,
  content,
  time,
  type = 'text',
  preview,
  filename,
  filesize,
  width,
  height,
  translatedContent,
  userName,
  needsConfirmation = false,
  isConfirmation = false,
  onConfirm
}) => {
  return (
    <div 
      style={{ 
        marginBottom: 12, 
        display: 'flex',
        flexDirection: 'column',
        alignItems: role === 'user' ? 'flex-start' : 'flex-end'
      }}
    >
      {/* 用户昵称显示 */}
      <div style={{ 
        fontSize: '10px', 
        color: '#999', 
        marginBottom: '2px',
        paddingLeft: role === 'user' ? '4px' : '0',
        paddingRight: role === 'user' ? '0' : '4px',
        opacity: 0.7
      }}>
        {role === 'user' ? (userName || 'User') : 'AI'}
      </div>
      
      <div style={{ 
        background: role === 'user' ? '#e6f7ff' : '#f6f6f6',
        padding: '8px 12px',
        borderRadius: 6,
        maxWidth: '90%',
        position: 'relative'
      }}>
        <div style={{ 
          fontWeight: 'bold', 
          marginBottom: 2,
          fontSize: '12px',
          color: role === 'user' ? '#1677ff' : '#333'
        }}>
          {role === 'user' ? 'User' : 'AI Assistant'}
        </div>
        
        {/* 根据消息类型渲染不同内容 */}
        {type === 'text' ? (
          <div style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
            {translatedContent || content}
          </div>
        ) : (
          <MediaMessage 
            type={type}
            url={content}
            preview={preview}
            filename={filename}
            filesize={filesize}
            width={width}
            height={height}
          />
        )}

        {/* 确认按钮 */}
        {needsConfirmation && role === 'ai' && (
          <div style={{ marginTop: '8px', textAlign: 'center' }}>
            <Button 
              type="primary" 
              size="small" 
              icon={<CheckOutlined />}
              onClick={onConfirm}
              style={{ 
                backgroundColor: '#52c41a',
                borderColor: '#52c41a',
                fontSize: '11px'
              }}
            >
              Confirm & Send to Player
            </Button>
          </div>
        )}

        {/* 确认标识 */}
        {isConfirmation && role === 'user' && (
          <div style={{ 
            marginTop: '4px', 
            textAlign: 'center',
            color: '#52c41a',
            fontSize: '11px',
            fontWeight: 'bold'
          }}>
            ✓ Confirmed
          </div>
        )}
        
        <div style={{ 
          fontSize: 11, 
          color: '#999', 
          marginTop: 4,
          textAlign: 'right'
        }}>
          {time}
        </div>
      </div>
    </div>
  );
};

export default MessageItem; 