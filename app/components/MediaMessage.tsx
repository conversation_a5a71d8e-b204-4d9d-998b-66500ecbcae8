'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Modal, Slider, Button, Tooltip, Space } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  ExpandOutlined, 
  DownloadOutlined,
  SoundOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined
} from '@ant-design/icons';

interface MediaMessageProps {
  type: 'image' | 'video' | 'file';
  url?: string;
  preview?: string;
  filename?: string;
  filesize?: string;
  width?: number;
  height?: number;
}

const MediaMessage: React.FC<MediaMessageProps> = ({
  type,
  url,
  preview,
  filename,
  filesize,
  width,
  height
}) => {
  // 预览模态框状态
  const [previewVisible, setPreviewVisible] = useState(false);
  
  // 视频控制相关状态
  const [controlsVisible, setControlsVisible] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [volume, setVolume] = useState(100);
  const [isMuted, setIsMuted] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // 图片缩放状态
  const [zoomLevel, setZoomLevel] = useState(100);
  const [zoomMode, setZoomMode] = useState<'contain' | 'cover' | 'fill'>('contain');
  
  // 默认图片
  const defaultImage = '/next.svg';
  // 使用一些实际的演示图片
  const demoImages = {
    image: 'https://images.unsplash.com/photo-1575936123452-b67c3203c357?q=80&w=2070',
    video: 'https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=1974',
    file: '/file.svg'
  };
  
  // 根据媒体类型选择合适的预览图
  const getPreviewSrc = () => {
    if (preview) return preview;
    if (type === 'image') return demoImages.image;
    if (type === 'video') return demoImages.video;
    return demoImages.file;
  };
  
  // 生成真实或模拟的下载链接
  const getDownloadUrl = () => {
    if (url) return url;
    // 如果没有真实URL，使用预览图作为下载替代
    return preview || getPreviewSrc();
  };
  
  // 获取文件名
  const getFileName = () => {
    if (filename) return filename;
    if (url) {
      // 尝试从URL获取文件名
      const urlParts = url.split('/');
      return urlParts[urlParts.length - 1];
    }
    // 默认文件名
    if (type === 'image') return 'image.jpg';
    if (type === 'video') return 'video.mp4';
    return 'document.txt';
  };
  
  // 处理视频播放/暂停
  const togglePlay = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };
  
  // 处理进度条变化
  const handleProgressChange = (value: number) => {
    if (videoRef.current) {
      const time = (videoRef.current.duration / 100) * value;
      videoRef.current.currentTime = time;
      setProgress(value);
    }
  };
  
  // 处理音量变化
  const handleVolumeChange = (value: number) => {
    if (videoRef.current) {
      videoRef.current.volume = value / 100;
      videoRef.current.muted = value === 0;
      setVolume(value);
      setIsMuted(value === 0);
    }
  };
  
  // 切换静音状态
  const toggleMute = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (videoRef.current) {
      const newMutedState = !isMuted;
      videoRef.current.muted = newMutedState;
      setIsMuted(newMutedState);
    }
  };
  
  // 图片缩放控制
  const handleZoomChange = (newZoom: number) => {
    setZoomLevel(Math.max(50, Math.min(200, newZoom)));
  };
  
  // 更改图片缩放模式
  const changeZoomMode = (mode: 'contain' | 'cover' | 'fill') => {
    setZoomMode(mode);
  };
  
  // 更新视频进度
  useEffect(() => {
    const updateProgress = () => {
      if (videoRef.current) {
        const percentage = (videoRef.current.currentTime / videoRef.current.duration) * 100;
        setProgress(percentage);
      }
    };
    
    const videoElement = videoRef.current;
    if (videoElement) {
      videoElement.addEventListener('timeupdate', updateProgress);
      
      // 视频结束时重置状态
      videoElement.addEventListener('ended', () => {
        setIsPlaying(false);
        setProgress(0);
      });
      
      // 初始化音量设置
      videoElement.volume = volume / 100;
      videoElement.muted = isMuted;
    }
    
    return () => {
      if (videoElement) {
        videoElement.removeEventListener('timeupdate', updateProgress);
        videoElement.removeEventListener('ended', () => {});
      }
    };
  }, [videoRef.current, volume, isMuted]);
  
  // 下载文件
  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    
    const downloadUrl = getDownloadUrl();
    const fileName = getFileName();
    
    // 创建一个临时链接并触发下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // 图片预览组件
  if (type === 'image') {
    return (
      <div>
        <div style={{ 
          marginBottom: 8,
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 4
        }}>
          <img 
            src={getPreviewSrc()} 
            alt="User shared image"
            style={{ 
              maxWidth: '100%', 
              maxHeight: 300, 
              objectFit: 'contain',
              border: '1px solid #f0f0f0',
              borderRadius: 4,
              cursor: 'pointer'
            }}
            onClick={() => setPreviewVisible(true)}
          />
          <div style={{
            position: 'absolute',
            top: 8,
            right: 8,
            display: 'flex',
            gap: 8
          }}>
            <Tooltip title="View large image">
              <Button 
                type="text"
                icon={<ExpandOutlined />}
                size="small"
                style={{
                  background: 'rgba(255,255,255,0.7)',
                  borderRadius: '50%'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setPreviewVisible(true);
                }}
              />
            </Tooltip>
            <Tooltip title="Download image">
              <Button 
                type="text"
                icon={<DownloadOutlined />}
                size="small"
                style={{
                  background: 'rgba(255,255,255,0.7)',
                  borderRadius: '50%'
                }}
                onClick={handleDownload}
              />
            </Tooltip>
          </div>
        </div>
        {width && height && (
          <div style={{ fontSize: 12, color: '#666' }}>
            Image {width}x{height}
          </div>
        )}
        
        <Modal
          open={previewVisible}
          footer={null}
          onCancel={() => setPreviewVisible(false)}
          width="80%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0 }}
          closeIcon={<Button type="text" icon={<ExpandOutlined />} style={{ position: 'absolute', top: 10, right: 10, background: 'rgba(255,255,255,0.7)', zIndex: 10 }} />}
        >
          <div style={{ 
            position: 'relative',
            height: 'calc(80vh - 40px)',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div style={{
              position: 'absolute',
              top: 10,
              left: 10,
              zIndex: 10,
              background: 'rgba(255,255,255,0.7)',
              padding: '5px 10px',
              borderRadius: 4,
              display: 'flex',
              alignItems: 'center',
              gap: 8
            }}>
              <Tooltip title="Zoom out">
                <Button 
                  icon={<ZoomOutOutlined />} 
                  size="small"
                  onClick={() => handleZoomChange(zoomLevel - 10)}
                  disabled={zoomLevel <= 50}
                />
              </Tooltip>
              <span style={{ width: 50, textAlign: 'center' }}>{zoomLevel}%</span>
              <Tooltip title="Zoom in">
                <Button 
                  icon={<ZoomInOutlined />} 
                  size="small"
                  onClick={() => handleZoomChange(zoomLevel + 10)}
                  disabled={zoomLevel >= 200}
                />
              </Tooltip>
              <Tooltip title="Reset view">
                <Button 
                  icon={<FullscreenOutlined />} 
                  size="small"
                  onClick={() => {
                    setZoomLevel(100);
                    setZoomMode('contain');
                  }}
                />
              </Tooltip>
              <Tooltip title="Download">
                <Button 
                  icon={<DownloadOutlined />} 
                  size="small"
                  onClick={handleDownload}
                />
              </Tooltip>
            </div>
            <div style={{ flex: 1, overflow: 'auto', textAlign: 'center' }}>
              <img 
                src={getPreviewSrc()} 
                alt="Image preview" 
                style={{ 
                  objectFit: zoomMode,
                  width: `${zoomLevel}%`,
                  maxHeight: 'calc(80vh - 40px)',
                  transition: 'transform 0.2s ease'
                }} 
              />
            </div>
          </div>
        </Modal>
      </div>
    );
  }
  
  // 视频预览组件
  if (type === 'video') {
    return (
      <div>
        <div 
          style={{ 
            marginBottom: 8, 
            position: 'relative',
            borderRadius: 4,
            overflow: 'hidden'
          }}
          onMouseEnter={() => setControlsVisible(true)}
          onMouseLeave={() => !isPlaying && setControlsVisible(false)}
          onClick={() => setPreviewVisible(true)}
        >
          <img 
            src={getPreviewSrc()} 
            alt="Video preview"
            style={{ 
              maxWidth: '100%', 
              maxHeight: 300, 
              objectFit: 'contain',
              border: '1px solid #f0f0f0',
              borderRadius: 4,
              cursor: 'pointer'
            }}
          />
          
          {/* 播放按钮 */}
          <div style={{ 
            position: 'absolute', 
            top: '50%', 
            left: '50%', 
            transform: 'translate(-50%, -50%)',
            background: 'rgba(0,0,0,0.6)',
            borderRadius: '50%',
            width: 48,
            height: 48,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer'
          }}>
            <div style={{ 
              width: 0, 
              height: 0, 
              borderTop: '8px solid transparent',
              borderBottom: '8px solid transparent',
              borderLeft: '16px solid white',
              marginLeft: 4
            }} />
          </div>
          
          {/* 右上角下载按钮 */}
          <div style={{
            position: 'absolute',
            top: 8,
            right: 8
          }}>
            <Tooltip title="Download video">
              <Button 
                type="text"
                icon={<DownloadOutlined />}
                size="small"
                style={{
                  background: 'rgba(255,255,255,0.7)',
                  borderRadius: '50%'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDownload(e);
                }}
              />
            </Tooltip>
          </div>
          
          {/* 视频控制栏 - 悬停时显示 */}
          {controlsVisible && (
            <div 
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                background: 'rgba(0,0,0,0.5)',
                padding: '8px 12px',
                display: 'flex',
                alignItems: 'center',
                gap: 8
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                type="text"
                size="small"
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                style={{ color: 'white' }}
                onClick={togglePlay}
              />
              <Slider 
                value={progress} 
                onChange={handleProgressChange}
                style={{ flex: 1, marginLeft: 4, marginRight: 4 }}
              />
              <div 
                style={{ position: 'relative' }}
                onMouseEnter={() => setShowVolumeSlider(true)}
                onMouseLeave={() => setShowVolumeSlider(false)}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<SoundOutlined style={{ color: isMuted ? 'rgba(255,255,255,0.5)' : 'white' }} />}
                  style={{ color: 'white' }}
                  onClick={toggleMute}
                />
                {showVolumeSlider && (
                  <div style={{
                    position: 'absolute',
                    bottom: '100%',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    background: 'rgba(0,0,0,0.7)',
                    padding: '10px 5px',
                    borderRadius: 4,
                    marginBottom: 5,
                    width: 40
                  }}>
                    <Slider
                      vertical
                      value={isMuted ? 0 : volume}
                      onChange={handleVolumeChange}
                      style={{ height: 60 }}
                    />
                  </div>
                )}
              </div>
              <Button 
                type="text"
                icon={<ExpandOutlined />}
                size="small"
                style={{ color: 'white' }}
                onClick={(e) => {
                  e.stopPropagation();
                  setPreviewVisible(true);
                }}
              />
            </div>
          )}
        </div>
        
        {width && height && (
          <div style={{ fontSize: 12, color: '#666' }}>
            Video {width}x{height}
          </div>
        )}
        
        {/* 视频预览模态框 */}
        <Modal
          open={previewVisible}
          footer={null}
          onCancel={() => setPreviewVisible(false)}
          width="80%"
          style={{ top: 20 }}
          bodyStyle={{ padding: 0 }}
        >
          <div style={{ position: 'relative' }}>
            <video
              ref={videoRef}
              controls
              style={{ width: '100%' }}
              poster={getPreviewSrc()}
              src={url || "https://www.w3schools.com/html/mov_bbb.mp4"} // 使用示例视频
            />
            <div style={{
              position: 'absolute',
              top: 10,
              right: 10,
              zIndex: 10
            }}>
              <Tooltip title="Download video">
                <Button 
                  icon={<DownloadOutlined />} 
                  style={{
                    background: 'rgba(255,255,255,0.7)',
                    borderRadius: '50%'
                  }}
                  onClick={handleDownload}
                />
              </Tooltip>
            </div>
          </div>
        </Modal>
      </div>
    );
  }
  
  // 文件预览组件
  if (type === 'file') {
    return (
      <div style={{ 
        border: '1px solid #f0f0f0', 
        borderRadius: 4, 
        padding: '8px 12px',
        display: 'flex',
        alignItems: 'center',
        gap: 8,
        cursor: 'pointer'
      }}>
        <span style={{ 
          background: '#f5f5f5', 
          borderRadius: 4, 
          padding: '4px 8px',
          fontSize: 20
        }}>
          📄
        </span>
        <div style={{ flex: 1 }}>
          <div style={{ fontWeight: 500 }}>{filename || 'document.txt'}</div>
          <div style={{ fontSize: 12, color: '#666' }}>{filesize || '0 KB'}</div>
        </div>
        <Tooltip title="Download file">
          <Button 
            type="text"
            icon={<DownloadOutlined />}
            onClick={handleDownload}
          />
        </Tooltip>
      </div>
    );
  }
  
  return null;
};

export default MediaMessage; 