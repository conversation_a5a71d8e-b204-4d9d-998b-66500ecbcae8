'use client';

import React, { useState, useRef, ChangeEvent, useEffect } from 'react';
import Image from 'next/image';

type Mode = 'general' | 'analyst';

interface AIInputBoxProps {
  onSend: (message: string, files: File[]) => void;
  placeholder?: string;
  theme: 'light' | 'dark';
  showModeSwitch?: boolean; // 控制是否显示模式切换，默认为 true
  colors: {
    bgMain: string;
    bgCard: string;
    bgElevated: string;
    bgSubtle: string;
    textPrimary: string;
    textSecondary: string;
    textTertiary: string;
    borderSubtle: string;
    borderStrong: string;
    primary: string;
    primaryLight: string;
    accent: string;
    success: string;
    error: string;
  };
}

export default function AIInputBox({ onSend, placeholder = "Type your message...", theme, showModeSwitch = true, colors }: AIInputBoxProps) {
  const [inputValue, setInputValue] = useState('');
  const [mode, setMode] = useState<Mode>('general');
  const [files, setFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const [typewriterText, setTypewriterText] = useState('');
  const [typewriterIndex, setTypewriterIndex] = useState(0);
  const [currentCharIndex, setCurrentCharIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [cursorVisible, setCursorVisible] = useState(true);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 不同模式的提示用例
  const generalPrompts = [
    "apply for leave...",
    "schedule a meeting with sales team...",
    "create an expense claim for $120...",
    "find all documents about Q3 marketing plan...",
    "write a project summary for client presentation...",
    "check status of the latest product launch..."
  ];
  
  const analystPrompts = [
    "analyze Q3 sales data...",
    "create a dashboard for marketing metrics...",
    "compare revenue across regions...",
    "generate forecast for next quarter...",
    "find correlations between marketing spend and conversions...",
    "visualize customer demographics by product..."
  ];

  // 获取当前模式的提示数组
  const getCurrentPrompts = () => {
    return mode === 'general' ? generalPrompts : analystPrompts;
  };

  // 打字机效果
  useEffect(() => {
    if (isFocused || inputValue) return;
    
    const prompts = getCurrentPrompts();
    const currentPrompt = prompts[typewriterIndex];

    // 光标闪烁
    const cursorInterval = setInterval(() => {
      setCursorVisible(prev => !prev);
    }, 530);

    // 打字效果
    const typeInterval = setTimeout(() => {
      if (isDeleting) {
        setTypewriterText(currentPrompt.substring(0, currentCharIndex - 1));
        setCurrentCharIndex(prev => prev - 1);
        
        if (currentCharIndex <= 1) {
          setIsDeleting(false);
          setTypewriterIndex((typewriterIndex + 1) % prompts.length);
          setCurrentCharIndex(0);
          setTypewriterText('');
        }
      } else {
        setTypewriterText(currentPrompt.substring(0, currentCharIndex + 1));
        setCurrentCharIndex(prev => prev + 1);
        
        if (currentCharIndex >= currentPrompt.length) {
          setTimeout(() => {
            setIsDeleting(true);
          }, 1500);
        }
      }
    }, isDeleting ? 40 : 80);

    return () => {
      clearTimeout(typeInterval);
      clearInterval(cursorInterval);
    };
  }, [typewriterIndex, currentCharIndex, isDeleting, isFocused, inputValue, mode]);

  // 模式切换时重置打字机效果
  useEffect(() => {
    setTypewriterIndex(0);
    setCurrentCharIndex(0);
    setTypewriterText('');
    setIsDeleting(false);
  }, [mode]);

  // 发送消息和文件
  const handleSend = () => {
    if (inputValue.trim() || files.length > 0) {
      onSend(inputValue, files);
      setInputValue('');
      setFiles([]);
      setPreviewUrls([]);
    }
  };

  // 处理文件上传
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setFiles([...files, ...newFiles]);
      
      // 为图片创建预览URL
      const newPreviewUrls = newFiles.map(file => {
        if (file.type.startsWith('image/')) {
          return URL.createObjectURL(file);
        }
        return '';
      });
      
      setPreviewUrls([...previewUrls, ...newPreviewUrls]);
    }
  };

  // 删除已选择的文件
  const removeFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
    
    const newPreviewUrls = [...previewUrls];
    if (newPreviewUrls[index]) {
      URL.revokeObjectURL(newPreviewUrls[index]);
    }
    newPreviewUrls.splice(index, 1);
    setPreviewUrls(newPreviewUrls);
  };

  // 触发文件选择对话框
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 切换模式
  const toggleMode = () => {
    setMode(mode === 'general' ? 'analyst' : 'general');
  };

  return (
    <div style={{ 
      borderRadius: '12px',
      border: `1px solid ${colors.borderSubtle}`,
      overflow: 'hidden',
      backgroundColor: showModeSwitch ? (mode === 'general' ? colors.bgSubtle : '#1A1D21') : colors.bgSubtle,
      transition: 'background-color 0.3s ease',
    }}>
      {/* 模式切换 - 只在 showModeSwitch 为 true 时显示 */}
      {showModeSwitch && (
        <div style={{ 
          display: 'flex', 
          padding: '8px 16px',
          borderBottom: `1px solid ${colors.borderSubtle}`
        }}>
          <button
            onClick={() => setMode('general')}
            style={{
              padding: '6px 12px',
              borderRadius: '16px',
              border: 'none',
              backgroundColor: mode === 'general' ? colors.primary : 'transparent',
              color: mode === 'general' ? 'white' : colors.textSecondary,
              fontSize: '12px',
              fontWeight: 500,
              cursor: 'pointer',
              marginRight: '8px'
            }}
          >
            General
          </button>
          <button
            onClick={() => setMode('analyst')}
            style={{
              padding: '6px 12px',
              borderRadius: '16px',
              border: 'none',
              backgroundColor: mode === 'analyst' ? '#38B2AC' : 'transparent',
              color: mode === 'analyst' ? 'white' : colors.textSecondary,
              fontSize: '12px',
              fontWeight: 500,
              cursor: 'pointer'
            }}
          >
            Data Analyst
          </button>
        </div>
      )}

      {/* 文件预览区域 */}
      {files.length > 0 && (
        <div style={{ 
          display: 'flex', 
          flexWrap: 'wrap',
          gap: '8px',
          padding: '12px 16px',
          borderBottom: `1px solid ${colors.borderSubtle}`
        }}>
          {files.map((file, index) => (
            <div key={index} style={{ 
              position: 'relative',
              borderRadius: '8px',
              overflow: 'hidden',
              border: `1px solid ${colors.borderSubtle}`,
              width: '80px',
              height: '80px',
              backgroundColor: colors.bgCard
            }}>
              {previewUrls[index] ? (
                // 图片预览
                <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                  <Image 
                    src={previewUrls[index]}
                    alt={file.name}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              ) : (
                // 文件图标
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  height: '100%', 
                  flexDirection: 'column',
                  padding: '8px'
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke={colors.textSecondary} strokeWidth="2">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div style={{ 
                    fontSize: '9px', 
                    color: colors.textSecondary,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '100%',
                    textAlign: 'center'
                  }}>
                    {file.name.length > 10 ? `${file.name.slice(0, 8)}...` : file.name}
                  </div>
                </div>
              )}
              
              {/* 删除按钮 */}
              <button
                onClick={() => removeFile(index)}
                style={{
                  position: 'absolute',
                  top: '2px',
                  right: '2px',
                  width: '18px',
                  height: '18px',
                  borderRadius: '50%',
                  backgroundColor: 'rgba(0,0,0,0.5)',
                  color: 'white',
                  border: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  fontSize: '10px',
                  padding: 0
                }}
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* 输入区域 */}
      <div style={{ 
        display: 'flex',
        padding: '12px 16px',
        alignItems: 'center',
        position: 'relative'
      }}>
        {/* 文件上传按钮 */}
        <button
          onClick={triggerFileInput}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            color: showModeSwitch ? (mode === 'general' ? colors.textSecondary : '#4FD1C5') : colors.textSecondary,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '50%',
            marginRight: '8px'
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2">
            <path strokeLinecap="round" strokeLinejoin="round" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
          </svg>
        </button>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileChange}
          style={{ display: 'none' }}
        />
        
        {/* 文本输入 */}
        <div style={{ flex: 1, position: 'relative' }}>
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSend();
              }
            }}
            style={{ 
              width: '100%',
              border: 'none',
              backgroundColor: 'transparent',
              color: showModeSwitch ? (mode === 'general' ? colors.textPrimary : '#E2E8F0') : colors.textPrimary,
              fontSize: '14px',
              outline: 'none',
              padding: '8px 0',
              caretColor: showModeSwitch ? (mode === 'general' ? colors.primary : '#4FD1C5') : colors.primary,
            }}
          />
          
          {/* 打字机效果提示 - 当未聚焦且无输入时显示 */}
          {!isFocused && !inputValue && (
            <div 
              style={{ 
                position: 'absolute',
                top: '8px',
                left: '0',
                pointerEvents: 'none',
                color: showModeSwitch ? (mode === 'general' ? colors.textTertiary : 'rgba(226, 232, 240, 0.6)') : colors.textTertiary,
                fontSize: '14px',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <span>{typewriterText}</span>
              <span 
                style={{
                  opacity: cursorVisible ? 1 : 0,
                  transition: 'opacity 0.3s',
                  marginLeft: '1px',
                  fontWeight: 'bold',
                  color: showModeSwitch ? (mode === 'general' ? colors.primary : '#4FD1C5') : colors.primary,
                }}
              >
                |
              </span>
            </div>
          )}
        </div>
        
        {/* 发送按钮 */}
        <button
          onClick={handleSend}
          style={{
            backgroundColor: showModeSwitch ? (mode === 'general' ? colors.primary : '#38B2AC') : colors.primary,
            border: 'none',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '32px',
            height: '32px',
            borderRadius: '50%',
            cursor: 'pointer'
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
            <path strokeLinecap="round" strokeLinejoin="round" d="M5 12h14M12 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
} 