'use client';

import React from 'react';
import MessageItem from './MessageItem';

interface ConversationMessage {
  role: string;
  content: string;
  time: string;
  type?: 'text' | 'image' | 'video' | 'file';
  preview?: string;
  filename?: string;
  filesize?: string;
  width?: number;
  height?: number;
  needsConfirmation?: boolean;
  isConfirmation?: boolean;
}

interface ConversationViewProps {
  conversation: ConversationMessage[];
  getTranslatedText: (text: string, language: string) => string;
  selectedLanguage: string;
  forceUpdateKey?: number;
  conversationId: string;
  userName?: string;
  onConfirm?: (messageIndex: number) => void;
}

const ConversationView: React.FC<ConversationViewProps> = ({
  conversation,
  getTranslatedText,
  selectedLanguage,
  forceUpdateKey = 0,
  conversationId,
  userName,
  onConfirm
}) => {
  return (
    <div style={{ maxHeight: 'calc(100vh - 290px)', overflowY: 'auto', marginTop: '4px' }}>
      {conversation.map((msg, index) => (
        <MessageItem 
          key={`msg-${conversationId}-${index}-${forceUpdateKey}`}
          role={msg.role}
          content={msg.content}
          time={msg.time}
          type={msg.type || 'text'}
          preview={msg.preview}
          filename={msg.filename}
          filesize={msg.filesize}
          width={msg.width}
          height={msg.height}
          translatedContent={
            msg.type === 'text' || !msg.type 
              ? getTranslatedText(msg.content, selectedLanguage) 
              : undefined
          }
          userName={userName}
          needsConfirmation={msg.needsConfirmation}
          isConfirmation={msg.isConfirmation}
          onConfirm={() => onConfirm?.(index)}
        />
      ))}
    </div>
  );
};

export default ConversationView; 