'use client';

import React, { useEffect } from 'react';

export default function SDKPage() {
  useEffect(() => {
    // 动态加载外部聊天SDK
    const script = document.createElement('script');
    script.src = 'https://d.stg.g123.jp/guest-chatbox-sdk.js';
    script.async = true;
    
    // 脚本加载完成后的回调
    script.onload = () => {
      console.log('Guest chatbox SDK loaded successfully');
    };
    
    script.onerror = () => {
      console.error('Failed to load guest chatbox SDK');
    };
    
    document.head.appendChild(script);
    
    // 清理函数：组件卸载时移除脚本
    return () => {
      const existingScript = document.querySelector('script[src="https://d.stg.g123.jp/guest-chatbox-sdk.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  return (
      <div style={{ position: 'relative', width: '100vw', height: '100vh' }}>
      {/* 嵌入CTW官网 */}
      <iframe
        src="https://ctw.inc/?lang=en"
        style={{
          width: '100%',
          height: '100%',
          border: 'none'
        }}
        title="CTW Official Website"
      />

      {/* 外部聊天SDK将自动在此页面上渲染聊天组件 */}
     </div>
   );
 } 