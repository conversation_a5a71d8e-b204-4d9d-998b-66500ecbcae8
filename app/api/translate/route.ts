import { createOpenAI } from '@ai-sdk/openai';
import { streamText } from 'ai';

export const runtime = 'edge';

const openrouter = createOpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY,
});

const TRANSLATION_SYSTEM_PROMPT = `You are a professional translation assistant. Your task is to provide accurate and natural translations between languages.

Translation Guidelines:
- Provide accurate, natural translations that maintain the original meaning
- Preserve the tone and style of the original text
- For formal text, use formal language in the target language
- For casual text, use casual language in the target language
- Do not add explanations or additional content unless specifically requested
- Return ONLY the translated text

Supported Languages:
- Chinese (Simplified): zh-CN
- English: en
- Japanese: ja
- Korean: ko
- French: fr
- German: de
- Spanish: es`;

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    // 支持两种格式：旧格式和useCompletion格式
    const text = body.text || body.prompt;
    const fromLanguage = body.fromLanguage;
    const toLanguage = body.toLanguage;
    
    console.log('Translation API received:', { 
      textLength: text?.length, 
      fromLanguage, 
      toLanguage,
      apiKey: process.env.OPENROUTER_API_KEY ? 'CONFIGURED' : 'MISSING'
    });

    if (!text) {
      return new Response('Missing text or prompt', { status: 400 });
    }

    // 如果prompt已经包含完整指令，直接使用
    const prompt = text.includes('Translate') ? text : 
      `Translate the following text from ${fromLanguage || 'auto-detected language'} to ${toLanguage || 'zh-CN'}:

${text}`;

    const result = await streamText({
      model: openrouter('anthropic/claude-3.5-sonnet'),
      system: TRANSLATION_SYSTEM_PROMPT,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3,
      maxTokens: 1000,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Translation API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
} 