import { createOpenAI } from '@ai-sdk/openai';
import { streamText } from 'ai';

export const runtime = 'edge';

const openrouter = createOpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY,
});

const TEXT_CHAT_SYSTEM_PROMPT = `You are a helpful AI assistant specializing in text analysis and conversation. You can help users understand, analyze, discuss, and work with text content.

Your capabilities include:
- Explaining text content in detail
- Answering questions about the text
- Providing analysis and insights
- Helping with related topics and questions
- Offering different perspectives on the content
- Providing summaries when requested

Guidelines:
- Always respond in the same language the user is using
- Be helpful, informative, and engaging
- Reference the original text when relevant
- Provide clear and well-structured responses
- Ask clarifying questions if needed
- Offer additional insights and related information when appropriate`;

export async function POST(req: Request) {
  try {
    const { messages, selectedText, systemPrompt } = await req.json();
    
    console.log('Text Chat API received:', { 
      messagesCount: messages?.length, 
      selectedTextLength: selectedText?.length,
      apiKey: process.env.OPENROUTER_API_KEY ? 'CONFIGURED' : 'MISSING'
    });

    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    // 构建上下文信息
    let contextPrompt = systemPrompt || TEXT_CHAT_SYSTEM_PROMPT;
    
    if (selectedText) {
      contextPrompt += `\n\nContext: The user has selected the following text for discussion:
"${selectedText}"

Please use this text as context for your responses when relevant.`;
    }

    const result = await streamText({
      model: openrouter('anthropic/claude-3.5-sonnet'),
      system: contextPrompt,
      messages,
      temperature: 0.7,
      maxTokens: 1500,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Text Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
} 