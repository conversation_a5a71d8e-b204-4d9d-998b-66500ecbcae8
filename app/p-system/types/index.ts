// 角色权限管理系统类型定义

// 权限点类型
export interface Permission {
  id: string;
  name: string; // 权限名称
  code: string; // 英文标识，如 user:view
  module: string; // 所属模块/分组
  description: string; // 权限说明
  children?: Permission[]; // 子权限
  parentId?: string; // 父权限ID
  level?: number; // 层级深度，用于样式缩进
}

// 角色类型
export interface Role {
  id: string;
  name: string; // 角色名称
  description: string; // 角色描述
  permissions: string[]; // 绑定的权限点ID列表
  createdAt: string; // 创建时间
  createdBy: string; // 创建人
  updatedAt?: string; // 更新时间
  updatedBy?: string; // 更新人
}

// 用户类型
export interface User {
  id: string;
  name: string;
  email: string;
  roles: string[]; // 绑定的角色ID列表
  avatar?: string;
  status: 'active' | 'inactive';
  type: 'employee' | 'project' | 'department';
  department?: string;
  position?: string;
  phone?: string;
}

// 员工类型
export interface Employee {
  id: string;
  name: string;
  email: string;
  department: string;
  position: string;
  phone?: string;
  status: 'active' | 'inactive';
}

// 项目类型
export interface Project {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'completed';
  startDate: string;
  endDate?: string;
  manager: string;
}

// 部门类型
export interface Department {
  id: string;
  name: string;
  description: string;
  manager: string;
  parentId?: string;
  status: 'active' | 'inactive';
  level?: number; // 层级深度：0=一级，1=二级，2=三级
  children?: string[]; // 子部门ID列表
}

// 操作日志类型
export interface AuditLog {
  id: string;
  operator: string; // 操作人
  operatorId: string;
  operationType: 'CREATE_ROLE' | 'UPDATE_ROLE' | 'DELETE_ROLE' | 'ASSIGN_PERMISSION' | 'REMOVE_PERMISSION' | 'ASSIGN_ROLE' | 'REMOVE_ROLE' | 'ADD_USER_TO_ROLE' | 'REMOVE_USER_FROM_ROLE' | 'CREATE_EMPLOYEE' | 'CREATE_PROJECT' | 'CREATE_DEPARTMENT';
  operationTime: string;
  target: string; // 操作目标（角色名称或用户名称）
  content: string; // 操作内容摘要
  details?: Record<string, any>; // 详细变更内容
}

// 权限模块分组
export interface PermissionModule {
  key: string;
  name: string;
  permissions: Permission[];
}

// 表格分页参数
export interface PaginationParams {
  current: number;
  pageSize: number;
  total?: number;
}

// 搜索筛选参数
export interface SearchParams {
  keyword?: string;
  module?: string;
  operationType?: string;
  dateRange?: [string, string];
}

// 权限变更记录
export interface PermissionChange {
  added: string[];
  removed: string[];
}

// 角色表单数据
export interface RoleFormData {
  name: string;
  description: string;
}

// 用户角色绑定数据
export interface UserRoleBinding {
  userId: string;
  roleIds: string[];
}

// 用户管理表单数据
export interface UserFormData {
  name: string;
  email: string;
  type: 'employee' | 'project' | 'department';
  department?: string;
  position?: string;
  phone?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  manager?: string;
  parentId?: string;
} 