import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { Role, Permission, User, AuditLog, Employee, Project, Department } from '../types';

// 扩展dayjs插件
dayjs.extend(relativeTime);

// 格式化时间
export const formatTime = (time: string, format: string = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(time).format(format);
};

// 格式化相对时间
export const formatRelativeTime = (time: string) => {
  return dayjs(time).fromNow();
};

// 生成唯一ID
export const generateId = (prefix: string = '') => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `${prefix}${timestamp}_${random}`;
};

// 权限校验函数
export const hasPermission = (userPermissions: string[], requiredPermission: string): boolean => {
  return userPermissions.includes(requiredPermission) || userPermissions.includes('*');
};

// 获取用户所有权限（基于角色）
export const getUserPermissions = (userRoles: string[], allRoles: Role[]): string[] => {
  const permissions = new Set<string>();
  
  userRoles.forEach(roleId => {
    const role = allRoles.find(r => r.id === roleId);
    if (role) {
      role.permissions.forEach(permission => permissions.add(permission));
    }
  });
  
  return Array.from(permissions);
};

// 检查角色名称是否重复
export const isRoleNameDuplicate = (name: string, allRoles: Role[], excludeId?: string): boolean => {
  return allRoles.some(role => role.name === name && role.id !== excludeId);
};

// 搜索过滤函数
export const filterRoles = (roles: Role[], keyword: string): Role[] => {
  if (!keyword) return roles;
  
  const lowerKeyword = keyword.toLowerCase();
  return roles.filter(role => 
    role.name.toLowerCase().includes(lowerKeyword) || 
    role.description.toLowerCase().includes(lowerKeyword)
  );
};

// 搜索权限点
export const filterPermissions = (permissions: Permission[], keyword: string): Permission[] => {
  if (!keyword) return permissions;
  
  const lowerKeyword = keyword.toLowerCase();
  return permissions.filter(permission => 
    permission.name.toLowerCase().includes(lowerKeyword) || 
    permission.code.toLowerCase().includes(lowerKeyword) ||
    permission.description.toLowerCase().includes(lowerKeyword)
  );
};

// 搜索用户
export const filterUsers = (users: User[], keyword: string): User[] => {
  if (!keyword) return users;
  
  const lowerKeyword = keyword.toLowerCase();
  return users.filter(user => 
    user.name.toLowerCase().includes(lowerKeyword) || 
    user.email.toLowerCase().includes(lowerKeyword)
  );
};

// 搜索操作日志
export const filterAuditLogs = (
  logs: AuditLog[], 
  keyword?: string, 
  operationType?: string,
  dateRange?: [string, string]
): AuditLog[] => {
  let filtered = logs;
  
  if (keyword) {
    const lowerKeyword = keyword.toLowerCase();
    filtered = filtered.filter(log => 
      log.operator.toLowerCase().includes(lowerKeyword) ||
      log.target.toLowerCase().includes(lowerKeyword) ||
      log.content.toLowerCase().includes(lowerKeyword)
    );
  }
  
  if (operationType) {
    filtered = filtered.filter(log => log.operationType === operationType);
  }
  
  if (dateRange && dateRange[0] && dateRange[1]) {
    const [startDate, endDate] = dateRange;
    filtered = filtered.filter(log => {
      const logTime = dayjs(log.operationTime);
      return logTime.isAfter(dayjs(startDate)) && logTime.isBefore(dayjs(endDate));
    });
  }
  
  return filtered;
};

// 比较权限变更
export const comparePermissions = (oldPermissions: string[], newPermissions: string[]) => {
  const added = newPermissions.filter(p => !oldPermissions.includes(p));
  const removed = oldPermissions.filter(p => !newPermissions.includes(p));
  
  return { added, removed };
};

// 获取操作类型的中文名称
export const getOperationTypeLabel = (operationType: string): string => {
  const typeLabels: Record<string, string> = {
    'CREATE_ROLE': '创建角色',
    'UPDATE_ROLE': '更新角色',
    'DELETE_ROLE': '删除角色',
    'ASSIGN_PERMISSION': '分配权限',
    'REMOVE_PERMISSION': '移除权限',
    'ASSIGN_ROLE': '分配角色',
    'REMOVE_ROLE': '移除角色',
  };
  
  return typeLabels[operationType] || operationType;
};

// 导出CSV格式的数据
export const exportToCSV = (data: any[], filename: string) => {
  if (!data.length) return;
  
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 验证角色表单数据
export const validateRoleForm = (data: { name: string; description: string }) => {
  const errors: Record<string, string> = {};
  
  if (!data.name.trim()) {
    errors.name = 'Role name is required';
  } else if (data.name.length < 2) {
    errors.name = 'Role name must be at least 2 characters';
  } else if (data.name.length > 50) {
    errors.name = 'Role name must be less than 50 characters';
  }
  
  if (!data.description.trim()) {
    errors.description = 'Role description is required';
  } else if (data.description.length > 200) {
    errors.description = 'Role description must be less than 200 characters';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// 深拷贝对象
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

// 扁平化权限树结构
export const flattenPermissions = (permissions: Permission[]): Permission[] => {
  const result: Permission[] = [];
  
  const flatten = (perms: Permission[]) => {
    perms.forEach(permission => {
      result.push(permission);
      if (permission.children && permission.children.length > 0) {
        flatten(permission.children);
      }
    });
  };
  
  flatten(permissions);
  return result;
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// 搜索员工
export const searchEmployees = (employees: Employee[], keyword: string): Employee[] => {
  if (!keyword) return employees;
  
  const lowerKeyword = keyword.toLowerCase();
  return employees.filter(employee => 
    employee.name.toLowerCase().includes(lowerKeyword) || 
    employee.email.toLowerCase().includes(lowerKeyword) ||
    employee.department.toLowerCase().includes(lowerKeyword) ||
    employee.position.toLowerCase().includes(lowerKeyword)
  );
};

// 搜索项目
export const searchProjects = (projects: Project[], keyword: string): Project[] => {
  if (!keyword) return projects;
  
  const lowerKeyword = keyword.toLowerCase();
  return projects.filter(project => 
    project.name.toLowerCase().includes(lowerKeyword) || 
    project.description.toLowerCase().includes(lowerKeyword) ||
    project.manager.toLowerCase().includes(lowerKeyword)
  );
};

// 搜索部门
export const searchDepartments = (departments: Department[], keyword: string): Department[] => {
  if (!keyword) return departments;
  
  const lowerKeyword = keyword.toLowerCase();
  return departments.filter(department => 
    department.name.toLowerCase().includes(lowerKeyword) || 
    department.description.toLowerCase().includes(lowerKeyword) ||
    department.manager.toLowerCase().includes(lowerKeyword)
  );
}; 