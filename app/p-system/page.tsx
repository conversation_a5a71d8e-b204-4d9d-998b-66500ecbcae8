'use client';

import React, { useState } from 'react';
import {
  Layout,
  Typography,
  Button,
  Space,
  Card,
  Tabs,
  message,
  Modal,
  Divider,
  App
} from 'antd';
import {
  PlusOutlined,
  SafetyOutlined,
  UserOutlined,
  HistoryOutlined,
  SettingOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';

// 导入组件
import RoleList from './components/RoleList';
import PermissionManager from './components/PermissionManager';
import RoleFormModal from './components/RoleFormModal';
import AuditLogTable from './components/AuditLogTable';
import UserManager from './components/UserManager';
import UserPermissionManager from './components/UserPermissionManager';

// 导入类型和数据
import { Role, RoleFormData, User, Employee, Project, Department, UserFormData } from './types';
import {
  mockRoles,
  mockPermissionModules,
  mockAuditLogs,
  mockUsers,
  mockEmployees,
  mockProjects,
  mockDepartments,
  currentUser
} from './data/mockData';
import { generateId, comparePermissions, formatTime } from './utils';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

function PSystemPageContent() {
  // 状态管理
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('permissions');
  const [currentView, setCurrentView] = useState<'role-management' | 'user-permissions'>('user-permissions');
  
  // 用户管理相关状态
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [employees, setEmployees] = useState<Employee[]>(mockEmployees);
  const [projects, setProjects] = useState<Project[]>(mockProjects);
  const [departments, setDepartments] = useState<Department[]>(mockDepartments);
  
  // 模态框状态
  const [roleModalVisible, setRoleModalVisible] = useState(false);
  const [roleModalMode, setRoleModalMode] = useState<'create' | 'edit'>('create');

  // 处理角色选择
  const handleRoleSelect = (role: Role) => {
    setSelectedRole(role);
    setSelectedPermissions([...role.permissions]);
    setHasChanges(false);
    setActiveTab('permissions');
  };

  // 处理权限变更
  const handlePermissionChange = async (permissions: string[]) => {
    // 立即更新UI状态，不显示loading
    setSelectedPermissions(permissions);
    
    if (selectedRole) {
      const hasChanged = JSON.stringify(permissions.sort()) !== 
                        JSON.stringify(selectedRole.permissions.sort());
      
      if (hasChanged) {
        // 立即更新本地状态，后台静默保存
        const updatedRoles = roles.map(role => 
          role.id === selectedRole.id 
            ? { 
                ...role, 
                permissions: [...permissions],
                updatedAt: new Date().toISOString(),
                updatedBy: currentUser.name
              }
            : role
        );
        
        setRoles(updatedRoles);
        setSelectedRole(prev => prev ? {
          ...prev,
          permissions: [...permissions],
          updatedAt: new Date().toISOString(),
          updatedBy: currentUser.name
        } : null);
        
        // 后台静默保存，不影响UI
        setTimeout(async () => {
          try {
            // 模拟API调用，静默进行
            await new Promise(resolve => setTimeout(resolve, 300));
            
            const oldPermissions = selectedRole.permissions;
            const changes = comparePermissions(oldPermissions, permissions);
            
            // 记录日志
            console.log('Permission changes saved:', changes);
          } catch (error) {
            console.error('Failed to save permissions in background:', error);
            // 静默失败，不显示错误信息给用户
          }
        }, 0);
      }
      
      setHasChanges(false);
    }
  };

  // 保存权限变更
  const handleSavePermissions = async () => {
    if (!selectedRole) return;

    setLoading(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const oldPermissions = selectedRole.permissions;
      const changes = comparePermissions(oldPermissions, selectedPermissions);
      
      // 更新角色权限
      const updatedRoles = roles.map(role => 
        role.id === selectedRole.id 
          ? { 
              ...role, 
              permissions: [...selectedPermissions],
              updatedAt: new Date().toISOString(),
              updatedBy: currentUser.name
            }
          : role
      );
      
      setRoles(updatedRoles);
      setSelectedRole(prev => prev ? {
        ...prev,
        permissions: [...selectedPermissions],
        updatedAt: new Date().toISOString(),
        updatedBy: currentUser.name
      } : null);
      setHasChanges(false);
      
      // 记录日志
      console.log('Permission changes:', changes);
      
      message.success('Permissions updated successfully');
    } catch (error) {
      console.error('Failed to save permissions:', error);
      message.error('Failed to save permissions');
    } finally {
      setLoading(false);
    }
  };

  // 取消权限变更
  const handleCancelChanges = () => {
    if (selectedRole) {
      setSelectedPermissions([...selectedRole.permissions]);
      setHasChanges(false);
    }
  };

  // 创建角色
  const handleCreateRole = async (data: RoleFormData) => {
    setLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newRole: Role = {
        id: generateId('role_'),
        name: data.name,
        description: data.description,
        permissions: [],
        createdAt: new Date().toISOString(),
        createdBy: currentUser.name
      };
      
      setRoles(prev => [...prev, newRole]);
      setRoleModalVisible(false);
      
      message.success('Role created successfully');
    } catch (error) {
      message.error('Failed to create role');
    } finally {
      setLoading(false);
    }
  };

  // 编辑角色
  const handleEditRole = async (data: RoleFormData) => {
    if (!selectedRole) return;
    
    setLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const updatedRoles = roles.map(role => 
        role.id === selectedRole.id 
          ? { 
              ...role, 
              name: data.name, 
              description: data.description,
              updatedAt: new Date().toISOString(),
              updatedBy: currentUser.name
            }
          : role
      );
      
      setRoles(updatedRoles);
      setSelectedRole(prev => prev ? {
        ...prev,
        name: data.name,
        description: data.description,
        updatedAt: new Date().toISOString(),
        updatedBy: currentUser.name
      } : null);
      setRoleModalVisible(false);
      
      message.success('Role updated successfully');
    } catch (error) {
      message.error('Failed to update role');
    } finally {
      setLoading(false);
    }
  };

  // 删除角色
  const handleDeleteRole = () => {
    if (!selectedRole) return;
    
    Modal.confirm({
      title: 'Delete Role',
      content: `Are you sure you want to delete the role "${selectedRole.name}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        setLoading(true);
        
        try {
          await new Promise(resolve => setTimeout(resolve, 800));
          
          setRoles(prev => prev.filter(role => role.id !== selectedRole.id));
          setSelectedRole(null);
          setSelectedPermissions([]);
          setHasChanges(false);
          
          message.success('Role deleted successfully');
        } catch (error) {
          message.error('Failed to delete role');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 添加用户到角色
  const handleUserAdd = async (userId: string, type: 'employee' | 'project' | 'department') => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedUsers = users.map(user => 
        user.id === userId 
          ? { ...user, roles: [...user.roles, selectedRole?.id].filter(Boolean) as string[] }
          : user
      );
      
      setUsers(updatedUsers);
      message.success(`User added to role successfully`);
    } catch (error) {
      message.error('Failed to add user to role');
    }
  };

  // 从角色移除用户
  const handleUserRemove = async (userId: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const updatedUsers = users.map(user => 
        user.id === userId 
          ? { ...user, roles: user.roles.filter(roleId => roleId !== selectedRole?.id) }
          : user
      );
      
      setUsers(updatedUsers);
    } catch (error) {
      message.error('Failed to remove user from role');
    }
  };

  // 创建员工
  const handleEmployeeCreate = async (data: UserFormData) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newEmployee: Employee = {
        id: generateId('emp_'),
        name: data.name,
        email: data.email,
        department: data.department || '',
        position: data.position || '',
        phone: data.phone,
        status: 'active'
      };
      
      setEmployees(prev => [...prev, newEmployee]);
    } catch (error) {
      message.error('Failed to create employee');
    }
  };

  const handleProjectCreate = async (data: UserFormData) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newProject: Project = {
        id: generateId('proj_'),
        name: data.name,
        description: data.description || '',
        status: 'active',
        startDate: data.startDate || new Date().toISOString().split('T')[0],
        endDate: data.endDate,
        manager: data.manager || ''
      };
      
      setProjects(prev => [...prev, newProject]);
    } catch (error) {
      message.error('Failed to create project');
    }
  };

  const handleDepartmentCreate = async (data: UserFormData) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newDepartment: Department = {
        id: generateId('dept_'),
        name: data.name,
        description: data.description || '',
        manager: data.manager || '',
        status: 'active'
      };
      
      setDepartments(prev => [...prev, newDepartment]);
    } catch (error) {
      message.error('Failed to create department');
    }
  };

  // 处理用户角色变更 - 仅UI切换，不保存数据
  const handleUserRoleChange = async (employeeId: string, roleId: string, enabled: boolean) => {
    // 仅在控制台记录，不调用任何message方法
    console.log(`Role ${enabled ? 'assigned' : 'removed'} for employee ${employeeId}, role ${roleId}`);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 左侧导航栏 */}
      <Layout.Sider 
        width={60} 
        style={{ 
          background: '#001529',
          position: 'fixed',
          height: '100vh',
          left: 0,
          top: 0,
          zIndex: 999
        }}
        collapsed={true}
        collapsedWidth={60}
      >
        <div style={{ 
          height: '60px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #303a52'
        }}>
          <SafetyOutlined style={{ color: '#1890ff', fontSize: '24px' }} />
        </div>
        <div style={{ padding: '16px 0' }}>
          <div 
            onClick={() => setCurrentView('role-management')}
            style={{
              height: '40px',
              margin: '8px',
              borderRadius: '6px',
              background: currentView === 'role-management' ? '#1890ff' : 'rgba(255,255,255,0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
          >
            <UserOutlined style={{ color: 'white', fontSize: '16px' }} />
          </div>
          <div 
            onClick={() => setCurrentView('user-permissions')}
            style={{
              height: '40px',
              margin: '8px',
              borderRadius: '6px',
              background: currentView === 'user-permissions' ? '#1890ff' : 'rgba(255,255,255,0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s'
            }}
          >
            <SettingOutlined style={{ color: 'white', fontSize: '16px' }} />
          </div>
        </div>
      </Layout.Sider>

      {/* 主内容区域 */}
      <Layout style={{ marginLeft: '60px', background: '#f5f7fa' }}>
        <Content style={{ padding: '32px', height: '100vh', overflow: 'hidden' }}>
          {currentView === 'role-management' ? (
            <div style={{ display: 'flex', gap: '24px', height: '100%' }}>
            {/* 左侧角色列表 */}
            <Card 
              title={
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <UserOutlined style={{ color: '#1890ff' }} />
                    <Text strong style={{ fontSize: '16px' }}>Roles</Text>
                    <div style={{
                      backgroundColor: '#e6f7ff',
                      color: '#1890ff',
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: 500
                    }}>
                      {roles.length}
                    </div>
                  </div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    size="small"
                    onClick={() => {
                      setRoleModalMode('create');
                      setRoleModalVisible(true);
                    }}
                    style={{
                      borderRadius: '6px',
                      fontSize: '12px',
                      height: '28px',
                      fontWeight: 500
                    }}
                  >
                    Create
                  </Button>
                </div>
              }
              style={{ 
                width: '320px', 
                height: 'calc(100vh - 64px)',
                display: 'flex', 
                flexDirection: 'column',
                borderRadius: '12px',
                boxShadow: '0 2px 8px 0 rgba(0,0,0,0.06)',
                border: '1px solid #f0f0f0'
              }}
              styles={{ 
                body: { padding: 0, flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' },
                header: { borderBottom: '1px solid #f5f5f5', backgroundColor: '#fafbfc' }
              }}
            >
            <RoleList
              roles={roles}
              users={users}
              departments={departments}
              selectedRoleId={selectedRole?.id}
              onRoleSelect={handleRoleSelect}
            />
          </Card>

          {/* 右侧内容区域 */}
          <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            {selectedRole ? (
              <Card 
                style={{ 
                  flex: 1, 
                  display: 'flex', 
                  flexDirection: 'column',
                  borderRadius: '12px',
                  boxShadow: '0 2px 8px 0 rgba(0,0,0,0.06)',
                  border: '1px solid #f0f0f0'
                }}
                                 styles={{ 
                   body: { padding: 0, flex: 1, display: 'flex', flexDirection: 'column' },
                   header: { borderBottom: '1px solid #f5f5f5', backgroundColor: '#fafbfc' }
                 }}
                title={
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div>
                      <UserOutlined style={{ marginRight: '8px' }} />
                      {selectedRole.name}
                      <Text type="secondary" style={{ marginLeft: '12px', fontSize: '12px' }}>
                        Created by {selectedRole.createdBy} • {formatTime(selectedRole.createdAt, 'MMM DD, YYYY')}
                      </Text>
                    </div>
                    <Space>
                      <Button
                        icon={<EditOutlined />}
                        onClick={() => {
                          setRoleModalMode('edit');
                          setRoleModalVisible(true);
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        danger
                        icon={<DeleteOutlined />}
                        onClick={handleDeleteRole}
                        disabled={selectedRole.id === 'admin'}
                      >
                        Delete
                      </Button>
                    </Space>
                  </div>
                }
              >
                <Tabs 
                  activeKey={activeTab} 
                  onChange={setActiveTab}
                  style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
                  tabBarStyle={{ padding: '0 24px', margin: 0 }}
                  items={[
                    {
                      key: 'permissions',
                      label: (
                        <span>
                          <SettingOutlined />
                          Permissions
                        </span>
                      ),
                      children: (
                        <div style={{ 
                          height: 'calc(100vh - 240px)', 
                          overflow: 'auto',
                          paddingRight: '8px'
                        }}>
                          <PermissionManager
                            permissionModules={mockPermissionModules}
                            selectedPermissions={selectedPermissions}
                            onPermissionChange={handlePermissionChange}
                          />
                        </div>
                      )
                    },
                    {
                      key: 'users',
                      label: (
                        <span>
                          <UserOutlined />
                          User Management
                        </span>
                      ),
                      children: (
                        <div style={{ 
                          height: 'calc(100vh - 240px)', 
                          overflow: 'auto',
                          paddingRight: '8px'
                        }}>
                          <UserManager
                            roleId={selectedRole.id}
                            roleName={selectedRole.name}
                            users={users}
                            employees={employees}
                            projects={projects}
                            departments={departments}
                            onUserAdd={handleUserAdd}
                            onUserRemove={handleUserRemove}
                            onEmployeeCreate={handleEmployeeCreate}
                            onProjectCreate={handleProjectCreate}
                            onDepartmentCreate={handleDepartmentCreate}
                            disabled={loading}
                          />
                        </div>
                      )
                    },
                    {
                      key: 'logs',
                      label: (
                        <span>
                          <HistoryOutlined />
                          Audit Logs
                        </span>
                      ),
                      children: (
                        <div style={{ 
                          height: 'calc(100vh - 240px)', 
                          overflow: 'auto',
                          paddingRight: '8px'
                        }}>
                          <AuditLogTable logs={mockAuditLogs} />
                        </div>
                      )
                    }
                  ]}
                />
              </Card>
            ) : (
              <Card style={{ 
                flex: 1, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                borderRadius: '12px',
                boxShadow: '0 2px 8px 0 rgba(0,0,0,0.06)',
                border: '2px dashed #d9d9d9',
                backgroundColor: '#fafafa'
              }}>
                <div style={{ textAlign: 'center', color: '#999', padding: '60px' }}>
                  <div style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '50%',
                    backgroundColor: '#f0f0f0',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 24px'
                  }}>
                    <UserOutlined style={{ fontSize: '32px', color: '#bfbfbf' }} />
                  </div>
                  <Title level={4} style={{ color: '#8c8c8c', marginBottom: '8px' }}>
                    Select a Role
                  </Title>
                  <Text style={{ color: '#bfbfbf', fontSize: '14px' }}>
                    Choose a role from the left panel to manage its permissions and settings
                  </Text>
                </div>
              </Card>
            )}
          </div>
        </div>
        ) : (
          // 用户权限管理视图
          <div style={{ height: '100%' }}>
            <UserPermissionManager
              employees={employees}
              users={users}
              projects={projects}
              departments={departments}
              roles={roles}
              onUserRoleChange={handleUserRoleChange}
            />
          </div>
        )}
      </Content>

        {/* 角色表单模态框 */}
        <RoleFormModal
          visible={roleModalVisible}
          mode={roleModalMode}
          role={roleModalMode === 'edit' ? selectedRole || undefined : undefined}
          roles={roles}
          onCancel={() => setRoleModalVisible(false)}
          onSubmit={roleModalMode === 'create' ? handleCreateRole : handleEditRole}
          loading={loading}
        />
      </Layout>
    </Layout>
  );
}

export default function PSystemPage() {
  return (
    <App>
      <PSystemPageContent />
    </App>
  );
}
