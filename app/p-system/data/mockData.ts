import { Permission, Role, User, AuditLog, PermissionModule, Employee, Project, Department } from '../types';

// 权限点模拟数据
export const mockPermissions: Permission[] = [
  // 用户管理模块
  {
    id: 'user_view',
    name: 'View Users',
    code: 'user:view',
    module: 'user',
    description: 'View user list and user details',
    level: 0,
  },
  {
    id: 'user_create',
    name: 'Create Users',
    code: 'user:create',
    module: 'user',
    description: 'Create new users',
    level: 0,
  },
  {
    id: 'user_edit',
    name: 'Edit Users',
    code: 'user:edit',
    module: 'user',
    description: 'Edit user information',
    level: 0,
  },
  {
    id: 'user_delete',
    name: 'Delete Users',
    code: 'user:delete',
    module: 'user',
    description: 'Delete user accounts',
    level: 0,
  },
  
  // 角色管理模块
  {
    id: 'role_view',
    name: 'View Roles',
    code: 'role:view',
    module: 'role',
    description: 'View role list and role details',
    level: 0,
  },
  {
    id: 'role_create',
    name: 'Create Roles',
    code: 'role:create',
    module: 'role',
    description: 'Create new roles',
    level: 0,
  },
  {
    id: 'role_edit',
    name: 'Edit Roles',
    code: 'role:edit',
    module: 'role',
    description: 'Edit role information and permissions',
    level: 0,
  },
  {
    id: 'role_delete',
    name: 'Delete Roles',
    code: 'role:delete',
    module: 'role',
    description: 'Delete roles',
    level: 0,
  },

  // CS Expert Agent - Reply 操作权限
  {
    id: 'cs_expert_reply',
    name: 'CS Expert Reply',
    code: 'cs_expert:reply',
    module: 'cs_expert',
    description: 'Access CS Expert Agent entry and perform replies',
    level: 0,
    children: [
      {
        id: 'cs_expert_data_all',
        name: 'View All CS Consultations',
        code: 'cs_expert:data:all',
        module: 'cs_expert',
        description: 'View all consultation sessions in CS Expert Agent',
        parentId: 'cs_expert_reply',
        level: 1,
      },
      {
        id: 'cs_expert_data_mention_me',
        name: 'View Mentioned CS Consultations',
        code: 'cs_expert:data:mention_me',
        module: 'cs_expert',
        description: 'View consultation sessions where user is mentioned (default)',
        parentId: 'cs_expert_reply',
        level: 1,
      },
    ]
  },

  // CS Expert Agent - Knowledge 操作权限
  {
    id: 'cs_expert_knowledge',
    name: 'CS Expert Knowledge Management',
    code: 'cs_expert:knowledge',
    module: 'cs_expert',
    description: 'Add, modify, submit for approval and all knowledge base operations',
    level: 0,
    children: [
      {
        id: 'cs_expert_knowledge_data_all',
        name: 'View All CS Knowledge',
        code: 'cs_expert:knowledge:data:all',
        module: 'cs_expert',
        description: 'View all knowledge created by everyone',
        parentId: 'cs_expert_knowledge',
        level: 1,
      },
      {
        id: 'cs_expert_knowledge_data_creator_self',
        name: 'View Own CS Knowledge',
        code: 'cs_expert:knowledge:data:creator_self',
        module: 'cs_expert',
        description: 'View only self-created knowledge including manual creation and chat extraction (default)',
        parentId: 'cs_expert_knowledge',
        level: 1,
      },
    ]
  },

  // CS Expert Agent - Evaluation 操作权限
  {
    id: 'cs_expert_evaluation',
    name: 'CS Expert Evaluation',
    code: 'cs_expert:evaluation',
    module: 'cs_expert',
    description: 'Perform evaluations in CS Expert Agent',
    level: 0,
    children: [
      {
        id: 'cs_expert_evaluation_data_all',
        name: 'View All CS Evaluation Data',
        code: 'cs_expert:evaluation:data:all',
        module: 'cs_expert',
        description: 'View all evaluation data (default)',
        parentId: 'cs_expert_evaluation',
        level: 1,
      },
    ]
  },

  // CS Expert Agent - Staff Skill 权限
  {
    id: 'cs_expert_staff_skill',
    name: 'CS Expert Staff Skill View',
    code: 'cs_expert:staff_skill',
    module: 'cs_expert',
    description: 'View all staff skill information',
    level: 0,
    children: [
      {
        id: 'cs_expert_staff_skill_data_all',
        name: 'View All CS Staff Skills',
        code: 'cs_expert:staff_skill:data:all',
        module: 'cs_expert',
        description: 'View all staff skill information',
        parentId: 'cs_expert_staff_skill',
        level: 1,
      },
      {
        id: 'cs_expert_staff_skill_data_self',
        name: 'View Own CS Staff Skills',
        code: 'cs_expert:staff_skill:data:self',
        module: 'cs_expert',
        description: 'View only own staff skill information',
        parentId: 'cs_expert_staff_skill',
        level: 1,
      },
    ]
  },
  {
    id: 'cs_expert_staff_skill_edit',
    name: 'CS Expert Staff Skill Edit',
    code: 'cs_expert:staff_skill:edit',
    module: 'cs_expert',
    description: 'Edit staff skill information within data permission scope',
    level: 0,
  },

  // i18n Expert Agent - Reply 操作权限
  {
    id: 'i18n_expert_reply',
    name: 'i18n Expert Reply',
    code: 'i18n_expert:reply',
    module: 'i18n_expert',
    description: 'Access i18n Expert Agent entry and perform replies',
    level: 0,
    children: [
      {
        id: 'i18n_expert_data_all',
        name: 'View All i18n Consultations',
        code: 'i18n_expert:data:all',
        module: 'i18n_expert',
        description: 'View all consultation sessions in i18n Expert Agent',
        parentId: 'i18n_expert_reply',
        level: 1,
      },
      {
        id: 'i18n_expert_data_mention_me',
        name: 'View Mentioned i18n Consultations',
        code: 'i18n_expert:data:mention_me',
        module: 'i18n_expert',
        description: 'View consultation sessions where user is mentioned (default)',
        parentId: 'i18n_expert_reply',
        level: 1,
      },
    ]
  },

  // i18n Expert Agent - Knowledge 操作权限
  {
    id: 'i18n_expert_knowledge',
    name: 'i18n Expert Knowledge Management',
    code: 'i18n_expert:knowledge',
    module: 'i18n_expert',
    description: 'Add, modify, submit for approval and all knowledge base operations',
    level: 0,
    children: [
      {
        id: 'i18n_expert_knowledge_data_all',
        name: 'View All i18n Knowledge',
        code: 'i18n_expert:knowledge:data:all',
        module: 'i18n_expert',
        description: 'View all knowledge created by everyone',
        parentId: 'i18n_expert_knowledge',
        level: 1,
      },
      {
        id: 'i18n_expert_knowledge_data_creator_self',
        name: 'View Own i18n Knowledge',
        code: 'i18n_expert:knowledge:data:creator_self',
        module: 'i18n_expert',
        description: 'View only self-created knowledge including manual creation and chat extraction (default)',
        parentId: 'i18n_expert_knowledge',
        level: 1,
      },
    ]
  },

  // i18n Expert Agent - Evaluation 操作权限
  {
    id: 'i18n_expert_evaluation',
    name: 'i18n Expert Evaluation',
    code: 'i18n_expert:evaluation',
    module: 'i18n_expert',
    description: 'Perform evaluations in i18n Expert Agent',
    level: 0,
    children: [
      {
        id: 'i18n_expert_evaluation_data_all',
        name: 'View All i18n Evaluation Data',
        code: 'i18n_expert:evaluation:data:all',
        module: 'i18n_expert',
        description: 'View all evaluation data (default)',
        parentId: 'i18n_expert_evaluation',
        level: 1,
      },
    ]
  },

  // i18n Expert Agent - Staff Skill 权限
  {
    id: 'i18n_expert_staff_skill',
    name: 'i18n Expert Staff Skill View',
    code: 'i18n_expert:staff_skill',
    module: 'i18n_expert',
    description: 'View all staff skill information',
    level: 0,
    children: [
      {
        id: 'i18n_expert_staff_skill_data_all',
        name: 'View All i18n Staff Skills',
        code: 'i18n_expert:staff_skill:data:all',
        module: 'i18n_expert',
        description: 'View all staff skill information',
        parentId: 'i18n_expert_staff_skill',
        level: 1,
      },
      {
        id: 'i18n_expert_staff_skill_data_self',
        name: 'View Own i18n Staff Skills',
        code: 'i18n_expert:staff_skill:data:self',
        module: 'i18n_expert',
        description: 'View only own staff skill information',
        parentId: 'i18n_expert_staff_skill',
        level: 1,
      },
    ]
  },
  {
    id: 'i18n_expert_staff_skill_edit',
    name: 'i18n Expert Staff Skill Edit',
    code: 'i18n_expert:staff_skill:edit',
    module: 'i18n_expert',
    description: 'Edit staff skill information within data permission scope',
    level: 0,
  },
];

// 权限模块分组
export const mockPermissionModules: PermissionModule[] = [
  {
    key: 'user',
    name: 'User Management',
    permissions: mockPermissions.filter(p => p.module === 'user' && (p.level === 0 || !p.parentId)),
  },
  {
    key: 'role',
    name: 'Role Management',
    permissions: mockPermissions.filter(p => p.module === 'role' && (p.level === 0 || !p.parentId)),
  },
  {
    key: 'cs_expert',
    name: 'CS Expert Agent',
    permissions: mockPermissions.filter(p => p.module === 'cs_expert' && (p.level === 0 || !p.parentId)),
  },
  {
    key: 'i18n_expert',
    name: 'i18n Expert Agent',
    permissions: mockPermissions.filter(p => p.module === 'i18n_expert' && (p.level === 0 || !p.parentId)),
  },
];

// 角色模拟数据
export const mockRoles: Role[] = [
  {
    id: 'admin',
    name: 'System Administrator',
    description: 'Super administrator with all system permissions',
    permissions: (() => {
      // 使用扁平化函数获取所有权限ID
      const flattenPermissions = (permissions: any[]): any[] => {
        const result: any[] = [];
        const flatten = (perms: any[]) => {
          perms.forEach((permission: any) => {
            result.push(permission);
            if (permission.children && permission.children.length > 0) {
              flatten(permission.children);
            }
          });
        };
        flatten(permissions);
        return result;
      };
      return flattenPermissions(mockPermissions).map(p => p.id);
    })(),
    createdAt: '2024-01-01T00:00:00Z',
    createdBy: 'system',
  },
  {
    id: 'cs_expert_evaluator',
    name: 'CS Expert Evaluator',
    description: 'CS Expert Agent manager with full permissions and all data access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill',
      'cs_expert_staff_skill_edit',
      'cs_expert_staff_skill_data_all'
    ],
    createdAt: '2024-01-02T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'i18n_expert_evaluator',
    name: 'i18n Expert Evaluator',
    description: 'i18n Expert Agent manager with full permissions and all data access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'i18n_expert_reply',
      'i18n_expert_data_all',
      'i18n_expert_knowledge',
      'i18n_expert_knowledge_data_all',
      'i18n_expert_evaluation',
      'i18n_expert_evaluation_data_all',
      'i18n_expert_staff_skill',
      'i18n_expert_staff_skill_edit',
      'i18n_expert_staff_skill_data_all'
    ],
    createdAt: '2024-01-03T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'cs_expert_responder',
    name: 'CS Expert Responder',
    description: 'CS Expert Agent staff with default data permissions',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill_data_self'
    ],
    createdAt: '2024-01-04T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'i18n_expert_responder',
    name: 'i18n Expert Responder',
    description: 'i18n Expert Agent staff with default data permissions',
    permissions: [
      'i18n_expert_reply',
      'i18n_expert_data_mention_me',
      'i18n_expert_knowledge',
      'i18n_expert_knowledge_data_creator_self',
      'i18n_expert_evaluation',
      'i18n_expert_evaluation_data_all',
      'i18n_expert_staff_skill_data_self'
    ],
    createdAt: '2024-01-05T00:00:00Z',
    createdBy: 'admin',
  },

  // 部门管理角色
  {
    id: 'tech_expert_evaluator',
    name: 'Tech Expert Evaluator',
    description: 'Technology department expert agent manager with full departmental access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill',
      'cs_expert_staff_skill_edit',
      'cs_expert_staff_skill_data_all'
    ],
    createdAt: '2024-01-07T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'creative_expert_evaluator',
    name: 'Creative Expert Evaluator',
    description: 'Creative department expert agent manager with design and content management',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-08T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'marketing_expert_evaluator',
    name: 'Marketing Expert Evaluator',
    description: 'Marketing department expert agent manager with campaign and analytics access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-09T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'operation_expert_evaluator',
    name: 'Operation Expert Evaluator',
    description: 'Operations department expert agent manager with process optimization access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-10T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'management_expert_evaluator',
    name: 'Management Expert Evaluator',
    description: 'Management department expert agent manager with strategic oversight access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill',
      'cs_expert_staff_skill_edit',
      'cs_expert_staff_skill_data_all'
    ],
    createdAt: '2024-01-11T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'cp_expert_evaluator',
    name: 'CP Expert Evaluator',
    description: 'Corporate Partnership department expert agent manager with partnership management',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-12T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'executive_expert_evaluator',
    name: 'Executive Expert Evaluator',
    description: 'Executive department expert agent manager with high-level strategic access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view',
      'cs_expert_reply',
      'cs_expert_data_all',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_all',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill',
      'cs_expert_staff_skill_edit',
      'cs_expert_staff_skill_data_all'
    ],
    createdAt: '2024-01-13T00:00:00Z',
    createdBy: 'admin',
  },
  // 部门员工角色
  {
    id: 'tech_expert_responder',
    name: 'Tech Expert Responder',
    description: 'Technology department expert agent staff with operational access',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill_data_self'
    ],
    createdAt: '2024-01-14T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'creative_expert_responder',
    name: 'Creative Expert Responder',
    description: 'Creative department expert agent staff with design operational access',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-15T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'marketing_expert_responder',
    name: 'Marketing Expert Responder',
    description: 'Marketing department expert agent staff with campaign operational access',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-16T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'operation_expert_responder',
    name: 'Operation Expert Responder',
    description: 'Operations department expert agent staff with process operational access',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-17T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'management_expert_responder',
    name: 'Management Expert Responder',
    description: 'Management department expert agent staff with strategic operational access',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill_data_self'
    ],
    createdAt: '2024-01-18T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'cp_expert_responder',
    name: 'CP Expert Responder',
    description: 'Corporate Partnership department expert agent staff with partnership operational access',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all'
    ],
    createdAt: '2024-01-19T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'executive_expert_responder',
    name: 'Executive Expert Responder',
    description: 'Executive department expert agent staff with high-level operational access',
    permissions: [
      'cs_expert_reply',
      'cs_expert_data_mention_me',
      'cs_expert_knowledge',
      'cs_expert_knowledge_data_creator_self',
      'cs_expert_evaluation',
      'cs_expert_evaluation_data_all',
      'cs_expert_staff_skill_data_self'
    ],
    createdAt: '2024-01-20T00:00:00Z',
    createdBy: 'admin',
  },
  // 新增组织角色
  {
    id: 'executives',
    name: 'Executives',
    description: 'Executive level personnel with strategic decision-making authority',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view'
    ],
    createdAt: '2024-01-21T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'group_managers',
    name: 'Group Managers',
    description: 'Group-level managers with cross-functional oversight',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view'
    ],
    createdAt: '2024-01-22T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'boss',
    name: 'CEO',
    description: 'Chief Executive Officer with ultimate authority',
    permissions: [
      'user_view', 'user_create', 'user_edit', 'user_delete',
      'role_view', 'role_create', 'role_edit', 'role_delete'
    ],
    createdAt: '2024-01-23T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'labors',
    name: 'Labors',
    description: 'General labor workforce with basic operational access',
    permissions: [
      'user_view'
    ],
    createdAt: '2024-01-24T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'hr',
    name: 'HR',
    description: 'Human Resources personnel with employee management access',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view'
    ],
    createdAt: '2024-01-25T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'hr_leaders',
    name: 'HR Leaders',
    description: 'HR leadership with advanced human resource management',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view', 'role_create', 'role_edit'
    ],
    createdAt: '2024-01-26T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'global_labors',
    name: 'Global Labors',
    description: 'International workforce with global operational access',
    permissions: [
      'user_view'
    ],
    createdAt: '2024-01-27T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'team_leaders',
    name: 'Team Leaders',
    description: 'Team-level leadership with operational oversight',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view'
    ],
    createdAt: '2024-01-28T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'department_managers',
    name: 'Department Managers',
    description: 'Department-level managers with functional authority',
    permissions: [
      'user_view', 'user_create', 'user_edit',
      'role_view', 'role_create', 'role_edit'
    ],
    createdAt: '2024-01-29T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'interviewer',
    name: 'Interviewer',
    description: 'Personnel authorized to conduct interviews and assessments',
    permissions: [
      'user_view'
    ],
    createdAt: '2024-01-30T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'members',
    name: 'Members',
    description: 'General organization members with basic access',
    permissions: [
      'user_view'
    ],
    createdAt: '2024-01-31T00:00:00Z',
    createdBy: 'admin',
  },
];

// 用户模拟数据 - 根据岗位分配对应角色
export const mockUsers: User[] = [
  // Alice Johnson - Senior Developer (Engineering) - System Administrator + Tech Expert
  {
    id: 'user001',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    roles: ['admin', 'tech_expert_evaluator', 'team_leaders'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=alice',
    status: 'active',
    type: 'employee',
    department: 'Engineering',
    position: 'Senior Developer'
  },
  // Bob Smith - Frontend Developer - Technical Staff
  {
    id: 'user002',
    name: 'Bob Smith',
    email: '<EMAIL>',
    roles: ['tech_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=bob',
    status: 'active',
    type: 'employee',
    department: 'Frontend Team',
    position: 'Frontend Developer'
  },
  // Carol White - Product Manager - Department Manager + Management Expert
  {
    id: 'user003',
    name: 'Carol White',
    email: '<EMAIL>',
    roles: ['department_managers', 'management_expert_evaluator', 'team_leaders'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=carol',
    status: 'active',
    type: 'employee',
    department: 'Product',
    position: 'Product Manager'
  },
  // David Brown - UI/UX Designer - Creative Staff
  {
    id: 'user004',
    name: 'David Brown',
    email: '<EMAIL>',
    roles: ['creative_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=david',
    status: 'active',
    type: 'employee',
    department: 'Product Design',
    position: 'UI/UX Designer'
  },
  // Eva Martinez - Marketing Specialist - Marketing Staff
  {
    id: 'user005',
    name: 'Eva Martinez',
    email: '<EMAIL>',
    roles: ['marketing_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=eva',
    status: 'active',
    type: 'employee',
    department: 'Marketing',
    position: 'Marketing Specialist'
  },
  // Frank Wilson - Sales Manager - Department Manager + CP Expert
  {
    id: 'user006',
    name: 'Frank Wilson',
    email: '<EMAIL>',
    roles: ['department_managers', 'team_leaders', 'cp_expert_evaluator'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=frank',
    status: 'active',
    type: 'employee',
    department: 'Sales',
    position: 'Sales Manager'
  },
  // Grace Lee - HR Specialist - HR Role
  {
    id: 'user007',
    name: 'Grace Lee',
    email: '<EMAIL>',
    roles: ['hr', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=grace',
    status: 'active',
    type: 'employee',
    department: 'Human Resources',
    position: 'HR Specialist'
  },
  // Henry Davis - Financial Analyst - Basic Member + Interviewer
  {
    id: 'user008',
    name: 'Henry Davis',
    email: '<EMAIL>',
    roles: ['members', 'interviewer'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=henry',
    status: 'active',
    type: 'employee',
    department: 'Finance',
    position: 'Financial Analyst'
  },
  // Iris Chen - Backend Developer - Technical Staff
  {
    id: 'user009',
    name: 'Iris Chen',
    email: '<EMAIL>',
    roles: ['tech_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=iris',
    status: 'active',
    type: 'employee',
    department: 'Backend Team',
    position: 'Backend Developer'
  },
  // Jack Turner - Operations Manager - Department Manager + Operations Expert
  {
    id: 'user010',
    name: 'Jack Turner',
    email: '<EMAIL>',
    roles: ['department_managers', 'operation_expert_evaluator', 'team_leaders'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=jack',
    status: 'active',
    type: 'employee',
    department: 'Operations',
    position: 'Operations Manager'
  },
  // Kate Anderson - QA Engineer - Technical Staff
  {
    id: 'user011',
    name: 'Kate Anderson',
    email: '<EMAIL>',
    roles: ['tech_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=kate',
    status: 'active',
    type: 'employee',
    department: 'QA Team',
    position: 'QA Engineer'
  },
  // Luke Roberts - DevOps Engineer (inactive) - Basic Technical Role
  {
    id: 'user012',
    name: 'Luke Roberts',
    email: '<EMAIL>',
    roles: ['tech_expert_responder'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=luke',
    status: 'inactive',
    type: 'employee',
    department: 'QA Team',
    position: 'DevOps Engineer'
  },
  // CEO - Richard Chen - Chief Executive Officer - Highest Authority
  {
    id: 'user013',
    name: 'Richard Chen',
    email: '<EMAIL>',
    roles: ['boss', 'executives', 'group_managers'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=richard',
    status: 'active',
    type: 'employee',
    department: 'Executive',
    position: 'Chief Executive Officer'
  },
  // HR Leader - Sarah Wilson - HR Department Head
  {
    id: 'user014',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    roles: ['hr_leaders', 'department_managers', 'team_leaders'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah',
    status: 'active',
    type: 'employee',
    department: 'Human Resources',
    position: 'HR Director'
  },
  // CS Expert Manager - Michael Zhang - Customer Service Head
  {
    id: 'user015',
    name: 'Michael Zhang',
    email: '<EMAIL>',
    roles: ['cs_expert_evaluator', 'team_leaders', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=michael',
    status: 'active',
    type: 'employee',
    department: 'Customer Service',
    position: 'CS Expert Manager'
  },
  // CS Expert Staff - Lisa Wang - Customer Service Agent
  {
    id: 'user016',
    name: 'Lisa Wang',
    email: '<EMAIL>',
    roles: ['cs_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=lisa',
    status: 'active',
    type: 'employee',
    department: 'Customer Service',
    position: 'CS Expert Agent'
  },
  // i18n Expert Manager - Kevin Martinez - Internationalization Head
  {
    id: 'user017',
    name: 'Kevin Martinez',
    email: '<EMAIL>',
    roles: ['i18n_expert_evaluator', 'team_leaders', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=kevin',
    status: 'active',
    type: 'employee',
    department: 'Internationalization',
    position: 'i18n Expert Manager'
  },
  // i18n Expert Staff - Emma Rodriguez - Localization Specialist
  {
    id: 'user018',
    name: 'Emma Rodriguez',
    email: '<EMAIL>',
    roles: ['i18n_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=emma',
    status: 'active',
    type: 'employee',
    department: 'Internationalization',
    position: 'Localization Specialist'
  },
  // Junior Developer - Tom Brown - Entry Level
  {
    id: 'user019',
    name: 'Tom Brown',
    email: '<EMAIL>',
    roles: ['global_labors', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=tom',
    status: 'active',
    type: 'employee',
    department: 'Engineering',
    position: 'Junior Developer'
  },
  // Executive Assistant - Amanda Taylor - Executive Support
  {
    id: 'user020',
    name: 'Amanda Taylor',
    email: '<EMAIL>',
    roles: ['executive_expert_responder', 'members'],
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=amanda',
    status: 'active',
    type: 'employee',
    department: 'Executive',
    position: 'Executive Assistant'
  }
];

// 员工模拟数据
export const mockEmployees: Employee[] = [
  {
    id: 'emp_001',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    department: 'dept_002', // Engineering
    position: 'Senior Developer',
    phone: '******-0101',
    status: 'active'
  },
  {
    id: 'emp_002',
    name: 'Bob Smith',
    email: '<EMAIL>',
    department: 'dept_005', // Frontend Team
    position: 'Frontend Developer',
    phone: '******-0102',
    status: 'active'
  },
  {
    id: 'emp_003',
    name: 'Carol White',
    email: '<EMAIL>',
    department: 'dept_003', // Product
    position: 'Product Manager',
    phone: '******-0103',
    status: 'active'
  },
  {
    id: 'emp_004',
    name: 'David Brown',
    email: '<EMAIL>',
    department: 'dept_008', // Product Design
    position: 'UI/UX Designer',
    phone: '******-0104',
    status: 'active'
  },
  {
    id: 'emp_005',
    name: 'Eva Martinez',
    email: '<EMAIL>',
    department: 'dept_012', // Marketing
    position: 'Marketing Specialist',
    phone: '******-0105',
    status: 'active'
  },
  {
    id: 'emp_006',
    name: 'Frank Wilson',
    email: '<EMAIL>',
    department: 'dept_011', // Sales
    position: 'Sales Manager',
    phone: '******-0106',
    status: 'active'
  },
  {
    id: 'emp_007',
    name: 'Grace Lee',
    email: '<EMAIL>',
    department: 'dept_021', // Human Resources
    position: 'HR Specialist',
    phone: '******-0107',
    status: 'active'
  },
  {
    id: 'emp_008',
    name: 'Henry Davis',
    email: '<EMAIL>',
    department: 'dept_013', // Finance
    position: 'Financial Analyst',
    phone: '******-0108',
    status: 'active'
  },
  {
    id: 'emp_009',
    name: 'Iris Chen',
    email: '<EMAIL>',
    department: 'dept_006', // Backend Team
    position: 'Backend Developer',
    phone: '******-0109',
    status: 'active'
  },
  {
    id: 'emp_010',
    name: 'Jack Turner',
    email: '<EMAIL>',
    department: 'dept_022', // Operations
    position: 'Operations Manager',
    phone: '******-0110',
    status: 'active'
  },
  {
    id: 'emp_011',
    name: 'Kate Anderson',
    email: '<EMAIL>',
    department: 'dept_007', // QA Team
    position: 'QA Engineer',
    phone: '******-0111',
    status: 'active'
  },
  {
    id: 'emp_012',
    name: 'Luke Roberts',
    email: '<EMAIL>',
    department: 'dept_007', // QA Team (DevOps相关)
    position: 'DevOps Engineer',
    phone: '******-0112',
    status: 'inactive'
  }
];

// 项目模拟数据
export const mockProjects: Project[] = [
  {
    id: 'proj_001',
    name: 'Customer Portal Redesign',
    description: 'Complete redesign of the customer portal with modern UI/UX',
    status: 'active',
    startDate: '2024-01-15',
    endDate: '2024-06-30',
    manager: 'Carol White'
  },
  {
    id: 'proj_002',
    name: 'Mobile App Development',
    description: 'Native mobile application for iOS and Android platforms',
    status: 'active',
    startDate: '2024-02-01',
    endDate: '2024-08-31',
    manager: 'Alice Johnson'
  },
  {
    id: 'proj_003',
    name: 'Data Analytics Platform',
    description: 'Business intelligence and analytics dashboard',
    status: 'active',
    startDate: '2024-03-01',
    endDate: '2024-09-30',
    manager: 'Henry Davis'
  },
  {
    id: 'proj_004',
    name: 'E-commerce Integration',
    description: 'Integration with third-party e-commerce platforms',
    status: 'active',
    startDate: '2024-01-01',
    endDate: '2024-05-31',
    manager: 'Frank Wilson'
  },
  {
    id: 'proj_005',
    name: 'Security Audit',
    description: 'Comprehensive security assessment and improvements',
    status: 'completed',
    startDate: '2023-10-01',
    endDate: '2023-12-31',
    manager: 'Luke Roberts'
  },
  {
    id: 'proj_006',
    name: 'API Modernization',
    description: 'Upgrade legacy APIs to RESTful architecture',
    status: 'active',
    startDate: '2024-02-15',
    endDate: '2024-07-15',
    manager: 'Iris Chen'
  },
  {
    id: 'proj_007',
    name: 'Cloud Migration',
    description: 'Migrate infrastructure to cloud platforms',
    status: 'active',
    startDate: '2024-04-01',
    endDate: '2024-10-31',
    manager: 'Jack Turner'
  },
  {
    id: 'proj_008',
    name: 'Marketing Automation',
    description: 'Implement automated marketing workflows',
    status: 'inactive',
    startDate: '2024-05-01',
    endDate: '2024-11-30',
    manager: 'Eva Martinez'
  }
];

// 部门模拟数据（支持多级层次结构）
export const mockDepartments: Department[] = [
  // 一级部门
  {
    id: 'dept_001',
    name: 'Technology',
    description: 'Technology and Innovation Division',
    manager: 'Alice Johnson',
    status: 'active',
    level: 0,
    parentId: undefined,
    children: ['dept_002', 'dept_003', 'dept_004']
  },
  {
    id: 'dept_010',
    name: 'Business',
    description: 'Business Operations Division',
    manager: 'Frank Wilson',
    status: 'active',
    level: 0,
    parentId: undefined,
    children: ['dept_011', 'dept_012', 'dept_013']
  },
  {
    id: 'dept_020',
    name: 'Support',
    description: 'Support and Operations Division',
    manager: 'Grace Lee',
    status: 'active',
    level: 0,
    parentId: undefined,
    children: ['dept_021', 'dept_022', 'dept_023']
  },

  // Technology Division - 二级部门
  {
    id: 'dept_002',
    name: 'Engineering',
    description: 'Software development and technical implementation',
    manager: 'David Brown',
    status: 'active',
    level: 1,
    parentId: 'dept_001',
    children: ['dept_005', 'dept_006', 'dept_007']
  },
  {
    id: 'dept_003',
    name: 'Product',
    description: 'Product strategy and design',
    manager: 'Carol White',
    status: 'active',
    level: 1,
    parentId: 'dept_001',
    children: ['dept_008', 'dept_009']
  },
  {
    id: 'dept_004',
    name: 'Research & Development',
    description: 'Innovation and technology research',
    manager: 'Eva Martinez',
    status: 'active',
    level: 1,
    parentId: 'dept_001',
    children: []
  },

  // Business Division - 二级部门
  {
    id: 'dept_011',
    name: 'Sales',
    description: 'Revenue generation and customer relations',
    manager: 'Henry Davis',
    status: 'active',
    level: 1,
    parentId: 'dept_010',
    children: ['dept_014', 'dept_015']
  },
  {
    id: 'dept_012',
    name: 'Marketing',
    description: 'Brand promotion and customer acquisition',
    manager: 'Jack Turner',
    status: 'active',
    level: 1,
    parentId: 'dept_010',
    children: ['dept_016', 'dept_017']
  },
  {
    id: 'dept_013',
    name: 'Finance',
    description: 'Financial planning and budget management',
    manager: 'Kate Anderson',
    status: 'active',
    level: 1,
    parentId: 'dept_010',
    children: []
  },

  // Support Division - 二级部门
  {
    id: 'dept_021',
    name: 'Human Resources',
    description: 'Employee management and organizational development',
    manager: 'Luke Roberts',
    status: 'active',
    level: 1,
    parentId: 'dept_020',
    children: []
  },
  {
    id: 'dept_022',
    name: 'Operations',
    description: 'Business operations and process optimization',
    manager: 'Iris Chen',
    status: 'active',
    level: 1,
    parentId: 'dept_020',
    children: []
  },
  {
    id: 'dept_023',
    name: 'Customer Support',
    description: 'Customer service and technical support',
    manager: 'Mike Zhang',
    status: 'active',
    level: 1,
    parentId: 'dept_020',
    children: []
  },

  // Engineering - 三级部门
  {
    id: 'dept_005',
    name: 'Frontend Team',
    description: 'User interface development',
    manager: 'Tom Wilson',
    status: 'active',
    level: 2,
    parentId: 'dept_002',
    children: []
  },
  {
    id: 'dept_006',
    name: 'Backend Team',
    description: 'Server-side development',
    manager: 'Sarah Brown',
    status: 'active',
    level: 2,
    parentId: 'dept_002',
    children: []
  },
  {
    id: 'dept_007',
    name: 'DevOps Team',
    description: 'Infrastructure and deployment automation',
    manager: 'Kevin Lee',
    status: 'active',
    level: 2,
    parentId: 'dept_002',
    children: []
  },

  // Product - 三级部门
  {
    id: 'dept_008',
    name: 'Product Management',
    description: 'Product strategy and planning',
    manager: 'Anna Garcia',
    status: 'active',
    level: 2,
    parentId: 'dept_003',
    children: []
  },
  {
    id: 'dept_009',
    name: 'UX Design',
    description: 'User experience design',
    manager: 'Chris Johnson',
    status: 'active',
    level: 2,
    parentId: 'dept_003',
    children: []
  },

  // Sales - 三级部门
  {
    id: 'dept_014',
    name: 'Enterprise Sales',
    description: 'B2B sales and enterprise accounts',
    manager: 'Diana Kim',
    status: 'active',
    level: 2,
    parentId: 'dept_011',
    children: []
  },
  {
    id: 'dept_015',
    name: 'SMB Sales',
    description: 'Small and medium business sales',
    manager: 'Eric Thompson',
    status: 'active',
    level: 2,
    parentId: 'dept_011',
    children: []
  },

  // Marketing - 三级部门
  {
    id: 'dept_016',
    name: 'Digital Marketing',
    description: 'Online marketing and campaigns',
    manager: 'Fiona Davis',
    status: 'active',
    level: 2,
    parentId: 'dept_012',
    children: []
  },
  {
    id: 'dept_017',
    name: 'Content Marketing',
    description: 'Content creation and strategy',
    manager: 'George Miller',
    status: 'active',
    level: 2,
    parentId: 'dept_012',
    children: []
  }
];

// 操作日志模拟数据
export const mockAuditLogs: AuditLog[] = [
  {
    id: 'log001',
    operator: 'Alice Johnson',
    operatorId: 'user001',
    operationType: 'CREATE_ROLE',
    operationTime: '2024-01-10T09:00:00Z',
    target: 'Business Manager',
    content: 'Created role "Business Manager" with 8 permissions',
    details: {
      roleName: 'Business Manager',
      permissions: ['user_view', 'user_create', 'order_view', 'order_create']
    }
  },
  {
    id: 'log002',
    operator: 'Alice Johnson',
    operatorId: 'user001',
    operationType: 'ASSIGN_ROLE',
    operationTime: '2024-01-10T10:30:00Z',
    target: 'Bob Smith',
    content: 'Assigned role "Business Manager" to user Bob Smith',
    details: {
      userId: 'user002',
      roleId: 'manager'
    }
  },
  {
    id: 'log003',
    operator: 'Bob Smith',
    operatorId: 'user002',
    operationType: 'UPDATE_ROLE',
    operationTime: '2024-01-11T14:15:00Z',
    target: 'Operator',
    content: 'Updated permissions for role "Operator"',
    details: {
      added: ['product_edit'],
      removed: ['user_edit']
    }
  },
  {
    id: 'log004',
    operator: 'Alice Johnson',
    operatorId: 'user001',
    operationType: 'REMOVE_ROLE',
    operationTime: '2024-01-12T16:45:00Z',
    target: 'Carol Davis',
    content: 'Removed role "Manager" from user Carol Davis',
    details: {
      userId: 'user003',
      roleId: 'manager'
    }
  },
  {
    id: 'log005',
    operator: 'Alice Johnson',
    operatorId: 'user001',
    operationType: 'DELETE_ROLE',
    operationTime: '2024-01-13T11:20:00Z',
    target: 'Temp Role',
    content: 'Deleted role "Temp Role"',
    details: {
      roleId: 'temp_role',
      roleName: 'Temp Role'
    }
  },
];

// 当前用户信息（用于权限校验）
export const currentUser = {
  id: 'user001',
  name: 'Alice Johnson',
  email: '<EMAIL>',
  roles: ['admin'],
}; 