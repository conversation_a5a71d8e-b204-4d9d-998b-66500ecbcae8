'use client';

import React, { useState, useMemo, useCallback } from 'react';
import {
  Card,
  Table,
  Input,
  Select,
  Space,
  Avatar,
  Switch,
  Typography,
  Tag,
  TreeSelect,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  TeamOutlined,
  ProjectOutlined,
  UserOutlined
} from '@ant-design/icons';
import { Employee, Project, Department, Role, User } from '../types';

const { Text } = Typography;

// 扩展Employee类型包含角色信息和头像
interface EmployeeWithRoles extends Employee {
  roleIds?: string[];
  avatar?: string;
}

interface UserPermissionManagerProps {
  employees: Employee[];
  users: User[]; // 用于获取角色信息
  projects: Project[];
  departments: Department[];
  roles: Role[];
  onUserRoleChange: (userId: string, roleId: string, enabled: boolean) => void;
}

export default function UserPermissionManager({
  employees,
  users,
  projects,
  departments,
  roles,
  onUserRoleChange
}: UserPermissionManagerProps) {
  const [searchText, setSearchText] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<string | undefined>();
  const [selectedProject, setSelectedProject] = useState<string | undefined>();
  // 内部管理开关状态
  const [userRoles, setUserRoles] = useState<Record<string, string[]>>({});

  // Mock头像数据
  const avatarUrls = [
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Alice',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Bob', 
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Carol',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=David',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Eva',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Frank',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Grace',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Henry',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Iris',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Jack',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Kate',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=Luke'
  ];

  // 合并员工和用户数据
  const employeesWithRoles: EmployeeWithRoles[] = employees.map((emp, index) => {
    const user = users.find(u => u.email === emp.email && u.type === 'employee');
    const currentRoles = userRoles[emp.id] || user?.roles || [];
    return {
      ...emp,
      roleIds: currentRoles,
      avatar: avatarUrls[index % avatarUrls.length]
    };
  });

  // 转换部门数据为树形结构
  const departmentTreeData = useMemo(() => {
    const buildTree = (deptIds: string[]): any[] => {
      const result: any[] = [];
      
      deptIds.forEach(deptId => {
        const dept = departments.find(d => d.id === deptId);
        if (!dept) return;
        
        if (!dept.children || dept.children.length === 0) {
          result.push({
            title: dept.name,
            value: dept.id,
            key: dept.id
          });
        } else {
          result.push({
            title: dept.name,
            value: dept.id,
            key: dept.id,
            children: buildTree(dept.children)
          });
        }
      });
      
      return result;
    };

    // 获取顶级部门（没有parentId的部门）
    const topLevelDepts = departments.filter(d => !d.parentId);
    return buildTree(topLevelDepts.map(d => d.id));
  }, [departments]);

  // 递归获取部门及其子部门ID
  const getDepartmentAndChildrenIds = useCallback((deptId: string): string[] => {
    const ids: string[] = [deptId];
    
    const findChildren = (dept: Department) => {
      if (dept.children) {
        dept.children.forEach(childId => {
          ids.push(childId);
          const childDept = departments.find(d => d.id === childId);
          if (childDept) {
            findChildren(childDept);
          }
        });
      }
    };

    const targetDept = departments.find(d => d.id === deptId);
    if (targetDept) {
      findChildren(targetDept);
    }

    return ids;
  }, [departments]);

  // 过滤后的员工数据
  const filteredEmployees = useMemo(() => {
    let filtered = [...employeesWithRoles];

    // 搜索过滤
    if (searchText) {
      const search = searchText.toLowerCase();
      filtered = filtered.filter(emp => 
        emp.name.toLowerCase().includes(search) ||
        emp.id.toLowerCase().includes(search) ||
        emp.position.toLowerCase().includes(search)
      );
    }

    // 部门过滤
    if (selectedDepartment) {
      const deptIds = getDepartmentAndChildrenIds(selectedDepartment);
      filtered = filtered.filter(emp => deptIds.includes(emp.department));
    }

    // 项目过滤 - 暂时跳过，因为Employee类型没有项目字段
    if (selectedProject) {
      // TODO: 需要在Employee类型中添加projectIds字段
      // filtered = filtered.filter(emp => emp.projectIds?.includes(selectedProject));
    }

    return filtered;
  }, [employeesWithRoles, searchText, selectedDepartment, selectedProject, departments, getDepartmentAndChildrenIds]);

  const handleRoleToggle = (userId: string, roleId: string, checked: boolean) => {
    // 更新内部状态
    setUserRoles(prev => {
      const currentRoles = prev[userId] || [];
      const newRoles = checked 
        ? [...currentRoles, roleId]
        : currentRoles.filter(r => r !== roleId);
      
      return {
        ...prev,
        [userId]: newRoles
      };
    });

    // 调用外部回调（但现在只是记录日志）
    onUserRoleChange(userId, roleId, checked);
  };

  const getDepartmentName = (departmentId: string) => {
    const dept = departments.find(d => d.id === departmentId);
    return dept?.name || 'Unknown';
  };

  // 角色分组和排序：System Administrator -> 组织角色 -> Expert Agent角色
  const adminRole = roles.find(role => role.id === 'admin');
  
  // 组织角色（按指定顺序）
  const organizationalRoleIds = [
    'executives', 'group_managers', 'boss', 'labors', 'hr', 'hr_leaders', 
    'global_labors', 'team_leaders', 'department_managers', 'interviewer', 'members'
  ];
  const organizationalRoles = organizationalRoleIds
    .map(id => roles.find(role => role.id === id))
    .filter(Boolean) as Role[];

  // Expert Agent角色，并按部门成对排序
  const expertAgentRoles = (() => {
    const filtered = roles.filter(role => 
      role.id.includes('cs_expert') || 
      role.id.includes('i18n_expert') ||
      role.id.includes('tech_expert') ||
      role.id.includes('creative_expert') ||
      role.id.includes('marketing_expert') ||
      role.id.includes('operation_expert') ||
      role.id.includes('management_expert') ||
      role.id.includes('cp_expert') ||
      role.id.includes('executive_expert')
    );
    
    // 按部门分组并成对排序（管理员 + 员工）
    const departments = ['cs', 'i18n', 'tech', 'creative', 'marketing', 'operation', 'management', 'cp', 'executive'];
    const sorted: Role[] = [];
    
    departments.forEach(dept => {
      const evaluator = filtered.find(role => role.id.includes(`${dept}_expert_evaluator`));
      const responder = filtered.find(role => role.id.includes(`${dept}_expert_responder`) || role.id.includes(`${dept}_expert_operator`));
      
      if (evaluator) sorted.push(evaluator);
      if (responder) sorted.push(responder);
    });
    
    return sorted;
  })();

  // 获取角色权限描述用于hover提示
  const getRoleDescription = (role: Role) => {
    const permissionCount = role.permissions?.length || 0;
    return `${role.description}\nPermissions: ${permissionCount} items`;
  };

  const columns = [
    {
      title: 'Employee',
      key: 'employee',
      width: 200,
      fixed: 'left' as const,
      render: (record: EmployeeWithRoles) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Avatar 
            src={record.avatar}
            icon={<UserOutlined />}
            size={32}
          />
          <div>
            <div style={{ fontWeight: 500, fontSize: '13px' }}>
              {record.name}
            </div>
            <div style={{ color: '#666', fontSize: '11px' }}>
              {record.id}
            </div>
          </div>
        </div>
      )
    },
    {
      title: 'Information',
      key: 'info',
      width: 180,
      fixed: 'left' as const,
      render: (record: EmployeeWithRoles) => (
        <div>
          <div style={{ marginBottom: '2px' }}>
            <Text style={{ fontSize: '12px', fontWeight: 500 }}>
              {getDepartmentName(record.department)}
            </Text>
          </div>
          <div>
            <Tag color="blue" style={{ fontSize: '10px', padding: '1px 6px' }}>
              {record.position}
            </Tag>
          </div>
        </div>
      )
    },
    // System Administrator 列
    ...(adminRole ? [{
      title: (
        <Tooltip title={getRoleDescription(adminRole)} placement="top">
          <span>{adminRole.name}</span>
        </Tooltip>
      ),
      key: adminRole.id,
      width: 140,
      align: 'center' as const,
      render: (record: EmployeeWithRoles) => {
        const hasRole = record.roleIds?.includes(adminRole.id) || false;
        return (
          <Switch
            size="small"
            checked={hasRole}
            onChange={(checked) => handleRoleToggle(record.id, adminRole.id, checked)}
          />
        );
      }
    }] : []),
    // 组织角色列
    ...organizationalRoles.map(role => ({
      title: (
        <Tooltip title={getRoleDescription(role)} placement="top">
          <span>{role.name}</span>
        </Tooltip>
      ),
      key: role.id,
      width: 120,
      align: 'center' as const,
      render: (record: EmployeeWithRoles) => {
        const hasRole = record.roleIds?.includes(role.id) || false;
        return (
          <Switch
            size="small"
            checked={hasRole}
            onChange={(checked) => handleRoleToggle(record.id, role.id, checked)}
          />
        );
      }
    })),
    // Expert Agent 父级分组
    {
      title: 'Expert Agent',
      key: 'expert_agent',
      children: expertAgentRoles.map(role => ({
        title: (
          <Tooltip title={getRoleDescription(role)} placement="top">
            <span style={{ fontSize: '12px' }}>{role.name}</span>
          </Tooltip>
        ),
        key: role.id,
        width: 120,
        align: 'center' as const,
        render: (record: EmployeeWithRoles) => {
          const hasRole = record.roleIds?.includes(role.id) || false;
          return (
            <Switch
              size="small"
              checked={hasRole}
              onChange={(checked) => handleRoleToggle(record.id, role.id, checked)}
            />
          );
        }
      }))
    }
  ];

  return (
    <Card 
      title="User Permission Management"
      style={{ height: '100%' }}
      styles={{ body: { padding: 0, height: 'calc(100% - 57px)' } }}
    >
      {/* 筛选区域 */}
      <div style={{ 
        padding: '16px 24px', 
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafbfc'
      }}>
        <Space size="middle" wrap>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SearchOutlined style={{ color: '#666' }} />
            <Input
              placeholder="Search by name, ID, or position"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
              allowClear
            />
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <TeamOutlined style={{ color: '#666' }} />
            <TreeSelect
              style={{ width: 200 }}
              placeholder="Filter by department"
              allowClear
              showSearch
              treeDefaultExpandAll
              value={selectedDepartment}
              onChange={setSelectedDepartment}
              treeData={departmentTreeData}
            />
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ProjectOutlined style={{ color: '#666' }} />
            <Select
              style={{ width: 200 }}
              placeholder="Filter by project"
              allowClear
              showSearch
              value={selectedProject}
              onChange={setSelectedProject}
              options={projects.map(project => ({
                label: project.name,
                value: project.id
              }))}
            />
          </div>
        </Space>
      </div>

      {/* 表格区域 */}
      <div style={{ height: 'calc(100% - 81px)', overflow: 'auto' }}>
        <Table
          columns={columns}
          dataSource={filteredEmployees}
          rowKey="id"
          pagination={false}
          scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}
          size="small"
        />
      </div>
    </Card>
  );
} 