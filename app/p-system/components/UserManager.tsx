'use client';

import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  message,
  Input,
  Divider,
  Card
} from 'antd';
import {
  UserOutlined,
  PlusOutlined,
  DeleteOutlined,
  SearchOutlined
} from '@ant-design/icons';

import { User, Employee, Project, Department } from '../types';
import { filterUsers } from '../utils';
import UserAddModal from './UserAddModal';

const { Text } = Typography;
const { Search } = Input;

interface UserManagerProps {
  roleId: string;
  roleName: string;
  users: User[];
  employees: Employee[];
  projects: Project[];
  departments: Department[];
  onUserAdd: (userId: string, type: 'employee' | 'project' | 'department') => void;
  onUserRemove: (userId: string) => void;
  onEmployeeCreate: (data: any) => void;
  onProjectCreate: (data: any) => void;
  onDepartmentCreate: (data: any) => void;
  disabled?: boolean;
}

export default function UserManager({
  roleId,
  roleName,
  users,
  employees,
  projects,
  departments,
  onUserAdd,
  onUserRemove,
  disabled = false
}: UserManagerProps) {
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 获取当前角色的用户
  const roleUsers = users.filter(user => user.roles.includes(roleId));
  const filteredUsers = filterUsers(roleUsers, searchKeyword);

  // 处理添加用户
  const handleAddUsers = async (selectedItems: any[]) => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      for (const item of selectedItems) {
        await onUserAdd(item.id, item.type);
      }
      
      setAddModalVisible(false);
      message.success(`Successfully added ${selectedItems.length} user(s) to role ${roleName}`);
    } catch (error) {
      console.error('Failed to add users:', error);
      message.error('Failed to add users');
    } finally {
      setLoading(false);
    }
  };

  // 处理移除用户
  const handleRemoveUser = (userId: string, userName: string) => {
    Modal.confirm({
      title: 'Remove User from Role',
      content: `Are you sure you want to remove "${userName}" from role "${roleName}"?`,
      okText: 'Remove',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => onUserRemove(userId)
    });
  };

  // 用户类型标签颜色
  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'employee': return 'blue';
      case 'project': return 'green';
      case 'department': return 'orange';
      default: return 'default';
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: User) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <UserOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontWeight: 500 }}>{text}</span>
                         <Tag color={getUserTypeColor(record.type)}>
               {record.type.charAt(0).toUpperCase() + record.type.slice(1)}
             </Tag>
          </div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.email}
          </Text>
        </div>
      )
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      render: (department: string) => department || '-'
    },
    {
      title: 'Position',
      dataIndex: 'position',
      key: 'position',
      render: (position: string) => position || '-'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </Tag>
      )
    },
    {
      title: 'Action',
      key: 'action',
      width: 120,
      render: (_: any, record: User) => (
        <Button
          danger
          size="small"
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveUser(record.id, record.name)}
          disabled={disabled}
        >
          Remove
        </Button>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 头部操作区 */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <div>
            <Text strong style={{ fontSize: '16px', color: '#262626' }}>
              Users with Role: {roleName}
            </Text>
            <Text type="secondary" style={{ marginLeft: '12px', fontSize: '14px' }}>
              {roleUsers.length} users assigned
            </Text>
          </div>
          
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setAddModalVisible(true)}
            disabled={disabled}
            style={{
              borderRadius: '6px',
              height: '36px',
              fontWeight: 500
            }}
          >
            Add
          </Button>
        </div>

        <Search
          placeholder="Search users by name or email..."
          allowClear
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          style={{ maxWidth: '400px' }}
          prefix={<SearchOutlined />}
        />
      </div>

      <Divider style={{ margin: '16px 0' }} />

      {/* 用户列表 */}
      <Card 
        style={{ 
          borderRadius: '8px',
          boxShadow: '0 1px 3px 0 rgba(0,0,0,0.1)'
        }}
        styles={{ body: { padding: 0 } }}
      >
        <Table
          columns={columns}
          dataSource={filteredUsers}
          rowKey="id"
          pagination={false}
          locale={{
            emptyText: (
              <div style={{ padding: '40px', textAlign: 'center', color: '#999' }}>
                <UserOutlined style={{ fontSize: '48px', marginBottom: '16px', color: '#d9d9d9' }} />
                <div style={{ fontSize: '16px', marginBottom: '8px' }}>No users assigned to this role</div>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  Click &quot;Add Users&quot; button to assign users to this role
                </Text>
              </div>
            )
          }}
        />
      </Card>

      {/* 用户添加模态框 */}
      <UserAddModal
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onSubmit={handleAddUsers}
        employees={employees}
        projects={projects}
        departments={departments}
        loading={loading}
        roleName={roleName}
      />
    </div>
  );
} 