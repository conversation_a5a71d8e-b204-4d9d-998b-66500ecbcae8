'use client';

import React, { useState } from 'react';
import { 
  List, 
  Input, 
  Tag, 
  Typography, 
  Empty, 
  Spin,
  TreeSelect,
  Space
} from 'antd';
import { SearchOutlined, TeamOutlined } from '@ant-design/icons';
import { Role, User, Department } from '../types';
import { filterRoles, formatTime } from '../utils';

const { Text } = Typography;
const { Search } = Input;

interface RoleListProps {
  roles: Role[];
  users: User[];
  departments: Department[];
  loading?: boolean;
  selectedRoleId?: string;
  onRoleSelect: (role: Role) => void;
}

export default function RoleList({ 
  roles, 
  users,
  departments,
  loading = false, 
  selectedRoleId, 
  onRoleSelect 
}: RoleListProps) {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState<string | undefined>(undefined);

  // 构建部门树形数据
  const buildDepartmentTreeData = () => {
    const buildTreeNode = (dept: Department): any => {
      return {
        title: dept.name,
        value: dept.id,
        key: dept.id,
        children: dept.children?.map(childId => {
          const childDept = departments.find(d => d.id === childId);
          return childDept ? buildTreeNode(childDept) : null;
        }).filter(Boolean) || []
      };
    };

    // 只返回顶级部门（level 0）
    return departments
      .filter(dept => dept.status === 'active' && (dept.level || 0) === 0)
      .map(dept => buildTreeNode(dept));
  };

  // 获取部门及其所有子部门的用户
  const getAllDepartmentUsers = (deptId: string): User[] => {
    const selectedDept = departments.find(d => d.id === deptId);
    if (!selectedDept) return [];

    const departmentUsers: User[] = [];
    
    // 递归获取部门及其子部门的所有用户
    const collectUsers = (dept: Department) => {
      // 直接按部门ID匹配用户（如果用户有部门ID）
      const directUsers = users.filter(user => user.department === dept.name);
      departmentUsers.push(...directUsers);
      
      // 递归处理子部门
      if (dept.children) {
        dept.children.forEach(childId => {
          const childDept = departments.find(d => d.id === childId);
          if (childDept) {
            collectUsers(childDept);
          }
        });
      }
    };

    collectUsers(selectedDept);
    return departmentUsers;
  };

  // 按部门筛选角色
  const filterRolesByDepartment = (roleList: Role[]) => {
    if (!selectedDepartment) return roleList;
    
    // 获取该部门及其子部门下的所有用户
    const departmentUsers = getAllDepartmentUsers(selectedDepartment);
    
    // 获取这些用户所拥有的角色ID
    const departmentRoleIds = new Set(
      departmentUsers.flatMap(user => user.roles)
    );
    
    // 筛选出包含这些角色的角色列表
    return roleList.filter(role => departmentRoleIds.has(role.id));
  };

  const searchFilteredRoles = filterRoles(roles, searchKeyword);
  const filteredRoles = filterRolesByDepartment(searchFilteredRoles);

  const handleSearch = (value: string) => {
    setSearchKeyword(value.trim());
  };

  const handleDepartmentChange = (value: string | undefined) => {
    setSelectedDepartment(value);
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 搜索和筛选区域 */}
      <div style={{ padding: '20px 20px 12px 20px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Search
            placeholder="Search roles..."
            allowClear
            prefix={<SearchOutlined />}
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ 
              width: '100%',
              borderRadius: '8px'
            }}
            size="large"
          />
          
          <TreeSelect
            placeholder="Filter by department"
            allowClear
            value={selectedDepartment}
            onChange={handleDepartmentChange}
            treeData={buildDepartmentTreeData()}
            style={{ width: '100%' }}
            size="large"
            suffixIcon={<TeamOutlined />}
            showSearch
            treeDefaultExpandAll
            filterTreeNode={(inputValue, treeNode) =>
              treeNode.title?.toString().toLowerCase().includes(inputValue.toLowerCase()) || false
            }
          />
        </Space>
      </div>

      {/* 角色列表 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        <Spin spinning={loading}>
          {filteredRoles.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="No roles found"
              style={{ padding: '40px 16px' }}
            />
          ) : (
            <List
              dataSource={filteredRoles}
              renderItem={(role) => (
                <List.Item
                  key={role.id}
                  onClick={() => onRoleSelect(role)}
                  style={{
                    cursor: 'pointer',
                    backgroundColor: selectedRoleId === role.id ? '#e6f7ff' : 'transparent',
                    borderLeft: selectedRoleId === role.id ? '4px solid #1890ff' : '4px solid transparent',
                    padding: '20px',
                    margin: '0 16px 8px 16px',
                    borderRadius: selectedRoleId === role.id ? '0 8px 8px 0' : '8px',
                    transition: 'all 0.2s',
                    border: selectedRoleId === role.id ? 'none' : '1px solid #f0f0f0'
                  }}
                  className="role-list-item"
                >
                  <div style={{ width: '100%' }}>
                    {/* 标题单独一行 */}
                    <div style={{ marginBottom: '8px' }}>
                      <Text strong style={{ fontSize: '14px', display: 'block' }}>
                        {role.name}
                      </Text>
                    </div>
                    
                    {/* 只显示Super Admin标签 */}
                    {role.id === 'admin' && (
                      <div style={{ marginBottom: '8px' }}>
                        <Tag color="red">Super Admin</Tag>
                      </div>
                    )}
                    
                    {/* 描述信息 */}
                    <div>
                      <Text 
                        type="secondary" 
                        style={{ 
                          fontSize: '12px',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          lineHeight: '1.4'
                        }}
                      >
                        {role.description}
                      </Text>
                      <div style={{ marginTop: '4px' }}>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          Created: {formatTime(role.createdAt, 'MMM DD, YYYY')}
                        </Text>
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          )}
        </Spin>
      </div>

      <style jsx>{`
        .role-list-item:hover {
          background-color: #f8f9fa !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px 0 rgba(0,0,0,0.1);
        }
      `}</style>
    </div>
  );
} 