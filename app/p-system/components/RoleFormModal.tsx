'use client';

import React, { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  message
} from 'antd';
import { Role, RoleFormData } from '../types';
import { validateRoleForm, isRoleNameDuplicate } from '../utils';

const { TextArea } = Input;

interface RoleFormModalProps {
  visible: boolean;
  mode: 'create' | 'edit';
  role?: Role;
  roles: Role[];
  onCancel: () => void;
  onSubmit: (data: RoleFormData) => Promise<void>;
  loading?: boolean;
}

export default function RoleFormModal({
  visible,
  mode,
  role,
  roles,
  onCancel,
  onSubmit,
  loading = false
}: RoleFormModalProps) {
  const [form] = Form.useForm();

  // 重置表单
  useEffect(() => {
    if (visible) {
      if (mode === 'edit' && role) {
        form.setFieldsValue({
          name: role.name,
          description: role.description
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, mode, role, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 验证表单数据
      const validation = validateRoleForm(values);
      if (!validation.isValid) {
        Object.keys(validation.errors).forEach(field => {
          form.setFields([
            {
              name: field,
              errors: [validation.errors[field]]
            }
          ]);
        });
        return;
      }

      // 检查角色名称重复
      if (isRoleNameDuplicate(values.name, roles, role?.id)) {
        form.setFields([
          {
            name: 'name',
            errors: ['Role name already exists']
          }
        ]);
        return;
      }

      await onSubmit(values);
      form.resetFields();
      message.success(`Role ${mode === 'create' ? 'created' : 'updated'} successfully`);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  return (
    <Modal
      title={mode === 'create' ? 'Create Role' : 'Edit Role'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={520}
      maskClosable={false}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
        style={{ marginTop: '16px' }}
      >
        <Form.Item
          label="Role Name"
          name="name"
          rules={[
            { required: true, message: 'Please enter role name' },
            { min: 2, message: 'Role name must be at least 2 characters' },
            { max: 50, message: 'Role name must be less than 50 characters' },
            {
              pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/,
              message: 'Role name can only contain letters, numbers, Chinese characters, spaces, hyphens and underscores'
            }
          ]}
        >
          <Input
            placeholder="Enter role name"
            showCount
            maxLength={50}
          />
        </Form.Item>

        <Form.Item
          label="Description"
          name="description"
          rules={[
            { required: true, message: 'Please enter role description' },
            { max: 200, message: 'Description must be less than 200 characters' }
          ]}
        >
          <TextArea
            placeholder="Enter role description"
            showCount
            maxLength={200}
            rows={4}
            style={{ resize: 'none' }}
          />
        </Form.Item>

        <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
          <div style={{ fontSize: '12px', color: '#389e0d', lineHeight: '1.5' }}>
            <strong>Note:</strong>
            <ul style={{ margin: '4px 0 0 16px', padding: 0 }}>
              <li>Role names must be unique within the system</li>
              <li>After creating the role, you can assign permissions on the next step</li>
              <li>Role changes will affect all users assigned to this role</li>
            </ul>
          </div>
        </div>
      </Form>
    </Modal>
  );
} 