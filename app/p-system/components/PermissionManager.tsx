'use client';

import React, { useState, useEffect } from 'react';
import {
  Checkbox,
  Input,
  Button,
  Typography,
  Space,
  Empty,
  Tooltip,
  Switch
} from 'antd';
import {
  SearchOutlined,
  CheckCircleOutlined,
  MinusCircleOutlined,
  CloseCircleOutlined,
  DownOutlined,
  RightOutlined
} from '@ant-design/icons';
import { PermissionModule, Permission } from '../types';
import { filterPermissions, flattenPermissions } from '../utils';

const { Text } = Typography;
const { Search } = Input;

interface PermissionManagerProps {
  permissionModules: PermissionModule[];
  selectedPermissions: string[];
  onPermissionChange: (permissions: string[]) => void;
  disabled?: boolean;
}

export default function PermissionManager({
  permissionModules,
  selectedPermissions,
  onPermissionChange,
  disabled = false
}: PermissionManagerProps) {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [collapsedModules, setCollapsedModules] = useState<Set<string>>(new Set());

  // 获取完整的权限列表，包括子权限
  const getCompletePermissionModules = () => {
    return permissionModules.map(module => ({
      ...module,
      permissions: getAllPermissionsFromModule(module.key)
    }));
  };

  // 从mockPermissions中获取指定模块的所有权限（包括子权限）
  const getAllPermissionsFromModule = (moduleKey: string) => {
    // 从父组件传入的数据中获取权限
    const modulePermissions = permissionModules.find(m => m.key === moduleKey)?.permissions || [];
    
    // 递归获取所有子权限
    const expandPermissions = (permissions: Permission[]): Permission[] => {
      const result: Permission[] = [];
      permissions.forEach(permission => {
        result.push(permission);
        if (permission.children && permission.children.length > 0) {
          result.push(...expandPermissions(permission.children));
        }
      });
      return result;
    };

    return expandPermissions(modulePermissions);
  };

  const completeModules = getCompletePermissionModules();

  // 过滤权限点
  const getFilteredModules = () => {
    if (!searchKeyword) return completeModules;

    return completeModules.map(module => ({
      ...module,
      permissions: filterPermissions(module.permissions, searchKeyword)
    })).filter(module => module.permissions.length > 0);
  };

  const filteredModules = getFilteredModules();

  // 处理单个权限勾选
  const handlePermissionCheck = (permissionId: string, checked: boolean) => {
    let newPermissions = [...selectedPermissions];

    if (checked) {
      if (!newPermissions.includes(permissionId)) {
        newPermissions.push(permissionId);
      }
    } else {
      newPermissions = newPermissions.filter(id => id !== permissionId);
    }

    onPermissionChange(newPermissions);
  };

  // 全选所有权限
  const handleSelectAll = () => {
    const allPermissionIds = completeModules.flatMap(module => 
      module.permissions.map(p => p.id)
    );
    onPermissionChange(allPermissionIds);
  };

  // 取消全选
  const handleDeselectAll = () => {
    onPermissionChange([]);
  };

  const totalPermissions = completeModules.reduce(
    (sum, module) => sum + module.permissions.length, 0
  );

  // 获取模块的权限结构
  const getModulePermissionStructure = () => {
    return filteredModules.map(module => {
      // 获取一级权限（level为0的权限）
      const primaryPermissions = module.permissions.filter(p => (p.level || 0) === 0);
      
      // 为每个一级权限查找其子权限
      const permissionsWithChildren = primaryPermissions.map(primaryPerm => {
        const childPermissions = module.permissions.filter(p => p.parentId === primaryPerm.id);
        return {
          ...primaryPerm,
          children: childPermissions
        };
      });

      return {
        ...module,
        permissions: permissionsWithChildren
      };
    });
  };

  const moduleStructure = getModulePermissionStructure();

  // 处理模块折叠/展开
  const handleModuleToggle = (moduleKey: string) => {
    const newCollapsed = new Set(collapsedModules);
    
    if (collapsedModules.has(moduleKey)) {
      // 展开模块
      newCollapsed.delete(moduleKey);
    } else {
      // 折叠模块，取消选择该模块下的所有权限
      newCollapsed.add(moduleKey);
      
      // 获取该模块下的所有权限ID
      const targetModule = moduleStructure.find(m => m.key === moduleKey);
      if (targetModule) {
        const modulePermissionIds: string[] = [];
        targetModule.permissions.forEach(permission => {
          modulePermissionIds.push(permission.id);
          if (permission.children) {
            modulePermissionIds.push(...permission.children.map(child => child.id));
          }
        });
        
        // 从选中权限中移除该模块的所有权限
        const newSelectedPermissions = selectedPermissions.filter(
          id => !modulePermissionIds.includes(id)
        );
        onPermissionChange(newSelectedPermissions);
      }
    }
    
    setCollapsedModules(newCollapsed);
  };

  // 获取模块的权限开启状态
  const getModulePermissionStatus = (module: any) => {
    const modulePermissionIds: string[] = [];
    module.permissions.forEach((permission: any) => {
      modulePermissionIds.push(permission.id);
      if (permission.children) {
        modulePermissionIds.push(...permission.children.map((child: any) => child.id));
      }
    });

    const selectedCount = modulePermissionIds.filter(id => selectedPermissions.includes(id)).length;
    
    return {
      hasAll: selectedCount === modulePermissionIds.length,
      hasPartial: selectedCount > 0 && selectedCount < modulePermissionIds.length,
      hasNone: selectedCount === 0,
      allPermissionIds: modulePermissionIds
    };
  };

  // 处理模块总开关
  const handleModuleSwitchChange = (moduleKey: string, checked: boolean) => {
    const targetModule = moduleStructure.find(m => m.key === moduleKey);
    if (!targetModule) return;

    const { allPermissionIds } = getModulePermissionStatus(targetModule);

    if (checked) {
      // 开启模块：选择该模块下的所有权限，并展开模块
      const newSelectedPermissions = Array.from(new Set([...selectedPermissions, ...allPermissionIds]));
      onPermissionChange(newSelectedPermissions);
      
      // 展开模块
      const newCollapsed = new Set(collapsedModules);
      newCollapsed.delete(moduleKey);
      setCollapsedModules(newCollapsed);
    } else {
      // 关闭模块：取消选择该模块下的所有权限，并折叠模块
      const newSelectedPermissions = selectedPermissions.filter(
        id => !allPermissionIds.includes(id)
      );
      onPermissionChange(newSelectedPermissions);
      
      // 折叠模块
      const newCollapsed = new Set(collapsedModules);
      newCollapsed.add(moduleKey);
      setCollapsedModules(newCollapsed);
    }
  };

  // 渲染权限项组件
  const renderPermissionItem = (permission: Permission, isChild = false) => (
    <div 
      key={permission.id} 
      style={{ 
        display: 'flex', 
        alignItems: 'flex-start', 
        gap: '8px',
        padding: isChild ? '4px 8px' : '8px 12px',
        backgroundColor: isChild ? '#f8f9fa' : '#fff',
        borderRadius: '6px',
        border: isChild ? '1px solid #e9ecef' : '1px solid #d9d9d9',
        marginBottom: '8px',
        opacity: isChild ? 0.8 : 1,
        fontSize: isChild ? '11px' : '12px'
      }}
    >
      <Checkbox
        checked={selectedPermissions.includes(permission.id)}
        onChange={(e) => handlePermissionCheck(permission.id, e.target.checked)}
        disabled={disabled}
        style={{ marginTop: '2px' }}
      />
      <div style={{ flex: 1 }}>
        <div style={{ marginBottom: isChild ? '2px' : '4px' }}>
          <Text strong style={{ fontSize: isChild ? '11px' : '12px' }}>
            {permission.name}
          </Text>
        </div>
        <Tooltip title={permission.description} placement="topLeft">
          <Text
            type="secondary"
            style={{
              fontSize: isChild ? '10px' : '11px',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              lineHeight: '1.3'
            }}
          >
            {permission.description}
          </Text>
        </Tooltip>
      </div>
    </div>
  );

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 搜索和操作栏 */}
      <div style={{ padding: '16px', backgroundColor: '#fafafa', borderBottom: '1px solid #f0f0f0' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Search
            placeholder="Search permissions..."
            allowClear
            prefix={<SearchOutlined />}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value.trim())}
            disabled={disabled}
          />
          
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <Button
                type="link"
                size="small"
                onClick={handleSelectAll}
                disabled={disabled || selectedPermissions.length === totalPermissions}
              >
                Select All
              </Button>
              <Button
                type="link"
                size="small"
                onClick={handleDeselectAll}
                disabled={disabled || selectedPermissions.length === 0}
              >
                Deselect All
              </Button>
            </Space>
            
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Selected / Total: {selectedPermissions.length} / {totalPermissions}
            </Text>
          </div>
        </Space>
      </div>

      {/* 权限展示区域 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '16px' }}>
        {moduleStructure.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No permissions found"
          />
        ) : (
          <div>
            {moduleStructure.map(module => {
              const isCollapsed = collapsedModules.has(module.key);
              const permissionStatus = getModulePermissionStatus(module);
              
              return (
                <div key={module.key} style={{ marginBottom: '24px' }}>
                  {/* 模块标题 */}
                  <div 
                    style={{ 
                      marginBottom: isCollapsed ? '0' : '16px', 
                      padding: '8px 0',
                      borderBottom: isCollapsed ? 'none' : '2px solid #1890ff',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px'
                    }}
                  >
                    {/* 模块总开关 */}
                    <div onClick={(e) => e.stopPropagation()}>
                      <Switch
                        checked={permissionStatus.hasAll}
                        onChange={(checked) => handleModuleSwitchChange(module.key, checked)}
                        disabled={disabled}
                        size="small"
                        style={{
                          backgroundColor: permissionStatus.hasPartial ? '#faad14' : undefined
                        }}
                      />
                    </div>
                    
                    <div 
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        cursor: 'pointer',
                        flex: 1
                      }}
                      onClick={() => handleModuleToggle(module.key)}
                    >
                      {isCollapsed ? (
                        <RightOutlined style={{ fontSize: '12px', color: '#1890ff' }} />
                      ) : (
                        <DownOutlined style={{ fontSize: '12px', color: '#1890ff' }} />
                      )}
                      <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
                        {module.name}
                      </Text>
                    </div>
                  </div>
                  
                  {/* 权限列展示 */}
                  {!isCollapsed && (
                    <div style={{ 
                      display: 'grid', 
                      gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
                      gap: '16px' 
                    }}>
                      {module.permissions.map(permission => (
                        <div key={permission.id} style={{
                          backgroundColor: '#fff',
                          borderRadius: '8px',
                          border: '1px solid #d9d9d9',
                          padding: '12px',
                          boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
                        }}>
                          {/* 一级权限 */}
                          {renderPermissionItem(permission, false)}
                          
                          {/* 数据权限（子权限） */}
                          {permission.children && permission.children.length > 0 && (
                            <div style={{ 
                              marginTop: '8px',
                              paddingLeft: '16px',
                              borderLeft: '2px solid #f0f0f0'
                            }}>
                              <div style={{ 
                                marginBottom: '8px',
                                fontSize: '10px',
                                color: '#999',
                                fontWeight: 500
                              }}>
                                Data Permissions
                              </div>
                              {permission.children.map(childPermission => 
                                renderPermissionItem(childPermission, true)
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
} 