'use client';

import React, { useState } from 'react';
import {
  Table,
  Tag,
  Space,
  Button,
  Input,
  Select,
  DatePicker,
  Typography,
  Drawer,
  Descriptions,
  Card,
  message
} from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  EyeOutlined,
  FilterOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { AuditLog } from '../types';
import { 
  formatTime, 
  getOperationTypeLabel, 
  filterAuditLogs,
  exportToCSV
} from '../utils';

const { RangePicker } = DatePicker;
const { Text } = Typography;

interface AuditLogTableProps {
  logs: AuditLog[];
  loading?: boolean;
}

export default function AuditLogTable({ logs, loading = false }: AuditLogTableProps) {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [operationType, setOperationType] = useState<string>('');
  const [dateRange, setDateRange] = useState<[string, string] | undefined>();
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);

  // 过滤数据
  const filteredLogs = filterAuditLogs(logs, searchKeyword, operationType, dateRange);

  // 操作类型选项
  const operationTypeOptions = [
    { label: 'All Types', value: '' },
    { label: '创建角色', value: 'CREATE_ROLE' },
    { label: '更新角色', value: 'UPDATE_ROLE' },
    { label: '删除角色', value: 'DELETE_ROLE' },
    { label: '分配权限', value: 'ASSIGN_PERMISSION' },
    { label: '移除权限', value: 'REMOVE_PERMISSION' },
    { label: '分配角色', value: 'ASSIGN_ROLE' },
    { label: '移除角色', value: 'REMOVE_ROLE' },
  ];

  // 获取操作类型标签颜色
  const getOperationTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'CREATE_ROLE': 'green',
      'UPDATE_ROLE': 'blue',
      'DELETE_ROLE': 'red',
      'ASSIGN_PERMISSION': 'orange',
      'REMOVE_PERMISSION': 'volcano',
      'ASSIGN_ROLE': 'purple',
      'REMOVE_ROLE': 'magenta',
    };
    return colorMap[type] || 'default';
  };

  // 查看详情
  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setDrawerVisible(true);
  };

  // 导出数据
  const handleExport = () => {
    if (filteredLogs.length === 0) {
      message.warning('No data to export');
      return;
    }

    const exportData = filteredLogs.map(log => ({
      'Operation Time': formatTime(log.operationTime),
      'Operator': log.operator,
      'Operation Type': getOperationTypeLabel(log.operationType),
      'Target': log.target,
      'Content': log.content
    }));

    exportToCSV(exportData, `audit-logs-${formatTime(new Date().toISOString(), 'YYYY-MM-DD')}`);
    message.success('Audit logs exported successfully');
  };

  // 重置筛选
  const handleResetFilters = () => {
    setSearchKeyword('');
    setOperationType('');
    setDateRange(undefined);
  };

  const columns: ColumnsType<AuditLog> = [
    {
      title: 'Operation Time',
      dataIndex: 'operationTime',
      width: 160,
      sorter: (a, b) => new Date(a.operationTime).getTime() - new Date(b.operationTime).getTime(),
      render: (time) => (
        <div>
          <div>{formatTime(time, 'YYYY-MM-DD')}</div>
          <Text type="secondary" style={{ fontSize: '11px' }}>
            {formatTime(time, 'HH:mm:ss')}
          </Text>
        </div>
      ),
    },
    {
      title: 'Operator',
      dataIndex: 'operator',
      width: 120,
      render: (operator) => <Text strong>{operator}</Text>,
    },
    {
      title: 'Operation Type',
      dataIndex: 'operationType',
      width: 120,
      render: (type) => (
        <Tag color={getOperationTypeColor(type)}>
          {getOperationTypeLabel(type)}
        </Tag>
      ),
    },
    {
      title: 'Target',
      dataIndex: 'target',
      width: 120,
      render: (target) => <Text>{target}</Text>,
    },
    {
      title: 'Content',
      dataIndex: 'content',
      width: 200,
      ellipsis: true,
      render: (content) => (
        <Text 
          style={{ 
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
          }}
        >
          {content}
        </Text>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetails(record)}
        >
          Details
        </Button>
      ),
    },
  ];

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 筛选栏 */}
      <Card size="small" style={{ marginBottom: '16px', flexShrink: 0 }}>
        <Space wrap>
          <Input
            placeholder="Search operator, target, or content..."
            prefix={<SearchOutlined />}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            style={{ width: 280 }}
            allowClear
          />
          
          <Select
            placeholder="Operation Type"
            value={operationType}
            onChange={setOperationType}
            style={{ width: 140 }}
            options={operationTypeOptions}
          />
          
          <RangePicker
            placeholder={['Start Date', 'End Date']}
            value={dateRange ? [
              require('dayjs')(dateRange[0]),
              require('dayjs')(dateRange[1])
            ] : undefined}
            onChange={(dates) => {
              if (dates && dates[0] && dates[1]) {
                setDateRange([
                  dates[0].toISOString(),
                  dates[1].toISOString()
                ]);
              } else {
                setDateRange(undefined);
              }
            }}
            style={{ width: 240 }}
          />
          
          <Button 
            icon={<FilterOutlined />} 
            onClick={handleResetFilters}
          >
            Reset
          </Button>
          
          <Button 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={handleExport}
            disabled={filteredLogs.length === 0}
          >
            Export CSV
          </Button>
        </Space>
      </Card>

      {/* 表格 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Table
          columns={columns}
          dataSource={filteredLogs}
          loading={loading}
          rowKey="id"
          size="small"
          pagination={false}
          scroll={{ 
            x: 1000,
            y: 'calc(100vh - 360px)'
          }}
        />
      </div>

      {/* 详情抽屉 */}
      <Drawer
        title="Operation Details"
        placement="right"
        width={600}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
      >
        {selectedLog && (
          <div>
            <Descriptions
              column={1}
              bordered
              size="small"
              style={{ marginBottom: '16px' }}
            >
              <Descriptions.Item label="Operation Time">
                {formatTime(selectedLog.operationTime)}
              </Descriptions.Item>
              <Descriptions.Item label="Operator">
                {selectedLog.operator}
              </Descriptions.Item>
              <Descriptions.Item label="Operation Type">
                <Tag color={getOperationTypeColor(selectedLog.operationType)}>
                  {getOperationTypeLabel(selectedLog.operationType)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Target">
                {selectedLog.target}
              </Descriptions.Item>
              <Descriptions.Item label="Content">
                {selectedLog.content}
              </Descriptions.Item>
            </Descriptions>

            {selectedLog.details && (
              <Card title="Detailed Changes" size="small">
                <pre style={{ 
                  backgroundColor: '#f5f5f5', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontSize: '12px',
                  lineHeight: '1.5',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(selectedLog.details, null, 2)}
                </pre>
              </Card>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
} 