'use client';

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Tabs,
  Table,
  Button,
  Space,
  Input,
  Badge,
  Tag,
  Tooltip,
  Typography,
  Divider,
  Empty,
  message,
  Tree
} from 'antd';
import {
  UserOutlined,
  ProjectOutlined,
  TeamOutlined,
  SearchOutlined,
  CloseOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';

import { Employee, Project, Department } from '../types';
import { searchEmployees, searchProjects, searchDepartments } from '../utils';

const { Search } = Input;
const { Text } = Typography;

interface SelectedItem {
  id: string;
  name: string;
  type: 'employee' | 'project' | 'department';
  email?: string;
  position?: string;
  department?: string;
  description?: string;
  manager?: string;
  status?: string;
}

interface UserAddModalProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (selectedItems: SelectedItem[]) => void;
  employees: Employee[];
  projects: Project[];
  departments: Department[];
  loading?: boolean;
  roleName: string;
}

export default function UserAddModal({
  visible,
  onCancel,
  onSubmit,
  employees,
  projects,
  departments,
  loading = false,
  roleName
}: UserAddModalProps) {
  const [activeTab, setActiveTab] = useState('employee');
  const [selectedItems, setSelectedItems] = useState<SelectedItem[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 重置状态
  useEffect(() => {
    if (visible) {
      setSelectedItems([]);
      setSearchKeyword('');
      setActiveTab('employee');
    }
  }, [visible]);

  // 转换数据格式
  const convertToSelectedItem = (
    item: Employee | Project | Department,
    type: 'employee' | 'project' | 'department'
  ): SelectedItem => {
    if (type === 'employee') {
      const emp: Employee = item as Employee;
      return {
        id: emp.id,
        name: emp.name,
        type: 'employee',
        email: emp.email,
        position: emp.position,
        department: emp.department,
        status: emp.status
      };
    } else if (type === 'project') {
      const proj: Project = item as Project;
      return {
        id: proj.id,
        name: proj.name,
        type: 'project',
        description: proj.description,
        manager: proj.manager,
        status: proj.status
      };
    } else {
      const dept: Department = item as Department;
      return {
        id: dept.id,
        name: dept.name,
        type: 'department',
        description: dept.description,
        manager: dept.manager,
        status: dept.status
      };
    }
  };

  // 处理选择项
  const handleItemSelect = (item: Employee | Project | Department, type: 'employee' | 'project' | 'department') => {
    const selectedItem = convertToSelectedItem(item, type);
    
    // 检查是否已选择
    const isSelected = selectedItems.some(selected => selected.id === selectedItem.id);
    
    if (isSelected) {
      setSelectedItems(prev => prev.filter(selected => selected.id !== selectedItem.id));
    } else {
      setSelectedItems(prev => [...prev, selectedItem]);
    }
  };

  // 移除选中项
  const handleItemRemove = (id: string) => {
    setSelectedItems(prev => prev.filter(item => item.id !== id));
  };

  // 清空所有选择
  const handleClearAll = () => {
    setSelectedItems([]);
  };

  // 提交处理
  const handleSubmit = () => {
    if (selectedItems.length === 0) {
      message.warning('Please select at least one item');
      return;
    }
    onSubmit(selectedItems);
  };

  // 获取过滤后的数据
  const getFilteredEmployees = () => {
    return searchEmployees(employees, searchKeyword).filter(emp => emp.status === 'active');
  };

  const getFilteredProjects = () => {
    return searchProjects(projects, searchKeyword).filter(proj => proj.status === 'active');
  };

  const getFilteredDepartments = () => {
    return searchDepartments(departments, searchKeyword).filter(dept => dept.status === 'active');
  };

  // 构建部门树形数据
  const buildDepartmentTree = () => {
    const filteredDepts = getFilteredDepartments();
    
    const buildTreeNode = (dept: Department): any => {
      const isSelected = isItemSelected(dept.id);
      
      return {
        title: (
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            padding: '4px 8px',
            borderRadius: '4px',
            backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
            border: isSelected ? '1px solid #91d5ff' : '1px solid transparent'
          }}>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: 500, fontSize: '13px' }}>{dept.name}</div>
              <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                Manager: {dept.manager}
              </div>
            </div>
            <Button
              type={isSelected ? 'default' : 'primary'}
              size="small"
              icon={isSelected ? <CloseOutlined /> : <PlusOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleItemSelect(dept, 'department');
              }}
            >
              {isSelected ? 'Remove' : 'Add'}
            </Button>
          </div>
        ),
        key: dept.id,
        children: dept.children?.map(childId => {
          const childDept = filteredDepts.find(d => d.id === childId);
          return childDept ? buildTreeNode(childDept) : null;
        }).filter(Boolean) || []
      };
    };

    // 只返回顶级部门（level 0）
    return filteredDepts
      .filter(dept => (dept.level || 0) === 0)
      .map(dept => buildTreeNode(dept));
  };

  // 检查是否已选择
  const isItemSelected = (id: string) => {
    return selectedItems.some(item => item.id === id);
  };

  // 员工表格列定义
  const employeeColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Employee) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>{record.email}</Text>
        </div>
      )
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department'
    },
    {
      title: 'Position',
      dataIndex: 'position',
      key: 'position'
    },
    {
      title: 'Action',
      key: 'action',
      width: 100,
      render: (_: any, record: Employee) => (
        <Button
          type={isItemSelected(record.id) ? 'default' : 'primary'}
          size="small"
          icon={isItemSelected(record.id) ? <CloseOutlined /> : <PlusOutlined />}
          onClick={() => handleItemSelect(record, 'employee')}
        >
          {isItemSelected(record.id) ? 'Remove' : 'Add'}
        </Button>
      )
    }
  ];

  // 项目表格列定义
  const projectColumns = [
    {
      title: 'Project Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Project) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>{record.description}</Text>
        </div>
      )
    },
    {
      title: 'Manager',
      dataIndex: 'manager',
      key: 'manager'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          status={status === 'active' ? 'processing' : status === 'completed' ? 'success' : 'default'}
          text={status.charAt(0).toUpperCase() + status.slice(1)}
        />
      )
    },
    {
      title: 'Action',
      key: 'action',
      width: 100,
      render: (_: any, record: Project) => (
        <Button
          type={isItemSelected(record.id) ? 'default' : 'primary'}
          size="small"
          icon={isItemSelected(record.id) ? <CloseOutlined /> : <PlusOutlined />}
          onClick={() => handleItemSelect(record, 'project')}
        >
          {isItemSelected(record.id) ? 'Remove' : 'Add'}
        </Button>
      )
    }
  ];



  // Tab配置
  const tabItems = [
    {
      key: 'employee',
      label: (
        <span>
          <UserOutlined />
          Employees
          <Badge count={employees.filter(emp => emp.status === 'active').length} style={{ marginLeft: '8px' }} />
        </span>
      ),
      children: (
        <div style={{ height: '100%', overflow: 'auto' }}>
          <Table
            columns={employeeColumns}
            dataSource={getFilteredEmployees()}
            rowKey="id"
            pagination={false}
            size="small"
            scroll={{ y: 'calc(50vh - 160px)' }}
          />
        </div>
      )
    },
    {
      key: 'project',
      label: (
        <span>
          <ProjectOutlined />
          Projects
          <Badge count={projects.filter(proj => proj.status === 'active').length} style={{ marginLeft: '8px' }} />
        </span>
      ),
      children: (
        <div style={{ height: '100%', overflow: 'auto' }}>
          <Table
            columns={projectColumns}
            dataSource={getFilteredProjects()}
            rowKey="id"
            pagination={false}
            size="small"
            scroll={{ y: 'calc(50vh - 160px)' }}
          />
        </div>
      )
    },
    {
      key: 'department',
      label: (
        <span>
          <TeamOutlined />
          Departments
          <Badge count={departments.filter(dept => dept.status === 'active').length} style={{ marginLeft: '8px' }} />
        </span>
      ),
      children: (
        <div style={{ height: '100%', overflow: 'auto', padding: '8px 0' }}>
          {buildDepartmentTree().length > 0 ? (
            <Tree
              treeData={buildDepartmentTree()}
              defaultExpandAll
              showLine
              blockNode
              style={{ 
                backgroundColor: '#fafafa',
                padding: '12px',
                borderRadius: '6px',
                border: '1px solid #f0f0f0'
              }}
            />
          ) : (
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="No departments found"
              style={{ marginTop: '40px' }}
            />
          )}
        </div>
      )
    }
  ];

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <UserOutlined style={{ color: '#1890ff' }} />
          <span>Add Users to Role: {roleName}</span>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={900}
      styles={{
        body: { 
          maxHeight: '70vh', 
          overflow: 'hidden',
          padding: '20px 24px'
        }
      }}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
          disabled={selectedItems.length === 0}
        >
          Add Selected ({selectedItems.length})
        </Button>
      ]}
      destroyOnClose
    >
      <div style={{ marginBottom: '16px' }}>
        <Search
          placeholder="Search by name, email, or description..."
          allowClear
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          style={{ marginBottom: '16px' }}
          prefix={<SearchOutlined />}
        />

        {/* 选中项回显区域 */}
        {selectedItems.length > 0 && (
          <div style={{ 
            background: '#f6ffed', 
            border: '1px solid #b7eb8f',
            borderRadius: '6px',
            padding: '12px',
            marginBottom: '16px',
            maxHeight: '120px',
            overflow: 'auto'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <Text strong style={{ color: '#52c41a' }}>
                Selected Items ({selectedItems.length})
              </Text>
              <Button
                type="link"
                size="small"
                icon={<DeleteOutlined />}
                onClick={handleClearAll}
                style={{ color: '#ff4d4f' }}
              >
                Clear All
              </Button>
            </div>
            
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
              {selectedItems.map(item => (
                <Tag
                  key={item.id}
                  closable
                  onClose={() => handleItemRemove(item.id)}
                  color={
                    item.type === 'employee' ? 'blue' :
                    item.type === 'project' ? 'green' : 'orange'
                  }
                  style={{ marginBottom: '4px' }}
                >
                  <Space size={4}>
                    {item.type === 'employee' && <UserOutlined />}
                    {item.type === 'project' && <ProjectOutlined />}
                    {item.type === 'department' && <TeamOutlined />}
                    <span style={{ fontWeight: 500 }}>{item.name}</span>
                    {item.type === 'employee' && item.position && (
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        ({item.position})
                      </Text>
                    )}
                  </Space>
                </Tag>
              ))}
            </div>
          </div>
        )}
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        style={{ 
          height: '50vh',
          display: 'flex',
          flexDirection: 'column'
        }}
        tabBarStyle={{ flexShrink: 0 }}
      />
    </Modal>
  );
} 