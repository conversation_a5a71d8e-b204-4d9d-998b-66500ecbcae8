import { ConfigProvider, App as AntdApp } from "antd";
import enUS from "antd/locale/en_US";
import "./globals.css";
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import StagewiseWrapper from './components/StagewiseWrapper'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Enterprise System Prototype',
  description: 'Based on Next.js and Ant Design 5',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <title>Enterprise System Prototype</title>
        <meta name="description" content="Enterprise system prototype built with Next.js and Ant Design" />
      </head>
      <body className={inter.className} style={{margin: 0, padding: 0}}>
        {/* @ts-ignore - React 19 type compatibility */}
        <ConfigProvider
          locale={enUS}
          theme={{
            token: {
              colorPrimary: "#1677ff",
            },
          }}
        >
          {/* @ts-ignore - React 19 type compatibility */}
          <AntdApp>{children}</AntdApp>
        </ConfigProvider>
        
        {/* 使用客户端组件包装器 */}
        <StagewiseWrapper />
      </body>
    </html>
  );
}
