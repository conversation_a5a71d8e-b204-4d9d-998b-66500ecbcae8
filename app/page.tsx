'use client';

import { useState } from 'react';
import { Layout, Typography, Card, Row, Col, Button, Space } from 'antd';
import {
  DatabaseOutlined,
  AppstoreOutlined,
  GlobalOutlined,
  RightOutlined,
  ApiOutlined,
  SelectOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';

const { Content } = Layout;
const { Title, Text } = Typography;

export default function Home() {
  const router = useRouter();

  // 应用列表数据
  const applications = [
    {
      key: 'd',
      title: 'Doraemon System',
      icon: <DatabaseOutlined style={{ fontSize: 36, color: '#52c41a' }} />,
      description: '哆啦A梦系统，包含数据仪表盘、存储和报告功能',
      path: '/d-system'
    },

    {
      key: 'sdk',
      title: 'SDK Demo',
      icon: <ApiOutlined style={{ fontSize: 36, color: '#eb2f96' }} />,
      description: 'SDK functionality demonstration and testing interface',
      path: '/sdk'
    },
    {
      key: 'text-selection',
      title: 'Text Selection Demo',
      icon: <SelectOutlined style={{ fontSize: 36, color: '#13c2c2' }} />,
      description: 'Text selection and context menu functionality demonstration',
      path: '/text-selection-demo'
    },
    {
      key: 'p-system',
      title: 'P System Permission Management',
      icon: <AppstoreOutlined style={{ fontSize: 36, color: '#fadb14' }} />,
      description: '管理P系统的用户权限。',
      path: '/p-system'
    }
  ];

  const handleAppClick = (path: string) => {
    router.push(path);
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f7fa' }}>
      <Content style={{ padding: '60px 24px' }}>
        <div style={{ maxWidth: 900, margin: '0 auto' }}>
          <Title level={2} style={{ marginBottom: 50, textAlign: 'center' }}>G123 Enterprise System Prototype</Title>
          
          <Row gutter={[32, 32]} justify="center">
            {applications.map(app => (
              <Col xs={24} sm={12} md={8} lg={8} xl={8} key={app.key}>
                <Card 
                  hoverable
                  style={{ 
                    height: '100%', 
                    minHeight: '280px',
                    borderRadius: 12, 
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                  }}
                  styles={{ 
                    body: {
                      padding: 24, 
                      display: 'flex', 
                      flexDirection: 'column', 
                      height: '100%' 
                    }
                  }}
                  onClick={() => handleAppClick(app.path)}
                >
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'center', 
                    marginBottom: 16,
                    padding: '24px 0',
                    background: '#f0f5ff',
                    borderRadius: 8
                  }}>
                    {app.icon}
                  </div>
                  <Title level={4} style={{ marginBottom: 8 }}>{app.title}</Title>
                  <Text type="secondary" style={{ marginBottom: 16, flex: 1 }}>{app.description}</Text>
                  <Button 
                    type="primary" 
                    style={{ 
                      width: '100%', 
                      marginTop: 'auto',
                      background: app.key === 'cs' ? '#1677ff' : 
                                 app.key === 'd' ? '#52c41a' : 
                                 app.key === 'a' ? '#fa8c16' : 
                                 app.key === 'w' ? '#722ed1' :
                                 app.key === 'sdk' ? '#eb2f96' : '#13c2c2'
                    }}
                  >
                    <Space>
                      进入系统
                      <RightOutlined />
                    </Space>
                  </Button>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </Content>
    </Layout>
  );
}
