'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function OnboardingPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  
  // 聊天状态
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'user',
      content: 'What can D do?',
      timestamp: new Date().toLocaleTimeString()
    },
    {
      id: 2,
      type: 'ai',
      content: 'Hi! I\'m your AI-powered D-Pal assistant. Here\'s what I can do for you:\n\n🌐 **Text Translation** - Instantly translate content across multiple languages\n📝 **Content Summarization** - Get quick summaries of lengthy documents\n💻 **Code Assistance** - Review, optimize and debug your code\n📋 **Task Management** - Organize your workflow and priorities\n✍️ **Creative Writing** - Generate ideas, articles, and creative content\n📊 **Data Analysis** - Process and interpret your data insights\n\n**🎯 Smart Features:**\n✨ **Text Selection** - Simply select any text to translate, explain, or summarize\n📸 **Smart Screenshots** - Capture and analyze screenshots with AI insights\n\nI\'m designed to learn your preferences and adapt to your working style. What would you like to explore first?',
      timestamp: new Date().toLocaleTimeString()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [selectedAvatar, setSelectedAvatar] = useState('🤖');
  const [isAvatarAnimating, setIsAvatarAnimating] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState<'default' | 'granted' | 'denied'>('default');
  const [isNotificationSetupComplete, setIsNotificationSetupComplete] = useState(false);
  
  // 可选头像列表
  const avatarOptions = ['🤖', '👨‍💻', '👩‍💻', '🧙‍♂️', '🧙‍♀️', '🦸‍♂️', '🦸‍♀️', '👑', '🎭', '🚀'];
  
  // 消息列表引用，用于自动滚动
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // 主题色配置
  const colors = {
    bgMain: '#0f172a',
    bgCard: '#1e293b',
    bgElevated: '#334155',
    bgSubtle: '#475569',
    textPrimary: '#f8fafc',
    textSecondary: '#cbd5e1',
    textTertiary: '#94a3b8',
    borderSubtle: '#334155',
    borderStrong: '#475569',
    primary: '#3b82f6',
    primaryLight: '#60a5fa',
    accent: '#8b5cf6',
    success: '#10b981',
    error: '#ef4444'
  };

  const steps = [
    {
      title: 'Welcome to D-Pal Studio',
      description: 'Your AI-powered personal assistant awaits customization',
      icon: '🤖'
    },
    {
      title: 'Customize Your Avatar',
      description: 'Personalize your AI companion with unique styles and accessories',
      icon: '🎨'
    },
    {
      title: 'Configure Tools',
      description: 'Set up translation tools and custom features for enhanced productivity',
      icon: '⚙️'
    },
    {
      title: 'Ready to Go!',
      description: 'Your D-Pal is configured and ready to assist you',
      icon: '🚀'
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      // 检查第三步是否完成
      if (currentStep === 2 && !isNotificationSetupComplete) {
        // 第三步未完成，AI发送提醒消息
        const reminderMessage = {
          id: messages.length + 1,
          type: 'ai' as const,
          content: 'notification-reminder',
          timestamp: new Date().toLocaleTimeString()
        };
        setMessages(prev => [...prev, reminderMessage]);
        return;
      }
      
      setCurrentStep(currentStep + 1);
      
      // 进入第二步时，AI发送avatar自定义消息
      if (currentStep === 0) {
        setTimeout(() => {
          const avatarMessage = {
            id: messages.length + 1,
            type: 'ai' as const,
            content: 'avatar-customization',
            timestamp: new Date().toLocaleTimeString()
          };
          setMessages(prev => [...prev, avatarMessage]);
        }, 500);
      }
      
      // 进入第三步时，AI发送通知权限设置消息
      if (currentStep === 1) {
        setTimeout(() => {
          const notificationMessage = {
            id: messages.length + 1,
            type: 'ai' as const,
            content: 'notification-setup',
            timestamp: new Date().toLocaleTimeString()
          };
          setMessages(prev => [...prev, notificationMessage]);
        }, 500);
      }
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    window.close();
  };

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;
    
    const newMessage = {
      id: messages.length + 1,
      type: 'user' as const,
      content: inputMessage,
      timestamp: new Date().toLocaleTimeString()
    };
    
    setMessages(prev => [...prev, newMessage]);
    setInputMessage('');
    
    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = {
        id: messages.length + 2,
        type: 'ai' as const,
        content: 'Great question! I can assist you with that. Would you like me to demonstrate any specific feature?',
        timestamp: new Date().toLocaleTimeString()
      };
      setMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  const handleAvatarComplete = () => {
    // 头像变化动画
    setIsAvatarAnimating(true);
    
    // AI回复消息
    const completeMessage = {
      id: messages.length + 1,
      type: 'ai' as const,
      content: `Perfect! I love your choice of ${selectedAvatar}. Your D-Pal assistant now has a personalized touch that reflects your style. This avatar will appear throughout your interactions with me. Ready to explore more customization options?`,
      timestamp: new Date().toLocaleTimeString()
    };
    
    setMessages(prev => [...prev, completeMessage]);
    
    // 动画完成后重置
    setTimeout(() => {
      setIsAvatarAnimating(false);
    }, 2000);
  };

  const handleNotificationSetup = async () => {
    try {
      // 检查浏览器是否支持通知
      if (!('Notification' in window)) {
        alert('This browser does not support notifications');
        return;
      }

      // 请求通知权限
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);

      if (permission === 'granted') {
        setIsNotificationSetupComplete(true);
        
        // AI回复成功消息
        const successMessage = {
          id: messages.length + 1,
          type: 'ai' as const,
          content: 'notification-success',
          timestamp: new Date().toLocaleTimeString()
        };
        setMessages(prev => [...prev, successMessage]);

        // 发送测试通知
        setTimeout(() => {
          new Notification('D-Pal Assistant', {
            body: '🎉 Great! Notifications are now enabled. I can now alert you about important updates and text selection features.',
            icon: '/favicon.ico'
          });
        }, 1000);
      } else {
        // 权限被拒绝时的处理
        const deniedMessage = {
          id: messages.length + 1,
          type: 'ai' as const,
          content: 'notification-denied',
          timestamp: new Date().toLocaleTimeString()
        };
        setMessages(prev => [...prev, deniedMessage]);
      }
    } catch (error) {
      console.error('Notification permission error:', error);
    }
  };

  // 自动滚动到最新消息
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 监听消息变化，自动滚动
  React.useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const renderRightContent = () => {
    // 所有步骤都使用聊天界面
    return (
      <div style={{
        backgroundColor: colors.bgCard,
        borderRadius: '16px',
        border: `1px solid ${colors.borderSubtle}`,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
            {/* 聊天头部 */}
            <div style={{
              padding: '1rem 1.5rem',
              borderBottom: `1px solid ${colors.borderSubtle}`,
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem'
            }}>
                             <div style={{
                 width: '32px',
                 height: '32px',
                 borderRadius: '50%',
                 backgroundColor: colors.primary,
                 display: 'flex',
                 alignItems: 'center',
                 justifyContent: 'center',
                 fontSize: '1.2rem',
                 transition: 'all 0.5s ease',
                 transform: isAvatarAnimating ? 'scale(1.2) rotate(360deg)' : 'scale(1)',
                 boxShadow: isAvatarAnimating ? '0 0 20px rgba(59, 130, 246, 0.5)' : 'none'
               }}>
                 {selectedAvatar}
               </div>
              <div>
                <div style={{ fontWeight: '600', fontSize: '1rem' }}>D-Pal Assistant</div>
                <div style={{ fontSize: '0.8rem', color: colors.textSecondary }}>Online • Ready to help</div>
              </div>
            </div>

            {/* 消息列表 */}
            <div style={{
              flex: 1,
              padding: '1.5rem',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '1.5rem',
              backgroundColor: colors.bgMain
            }}>
              {messages.map((message) => (
                <div
                  key={message.id}
                  style={{
                    display: 'flex',
                    justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'
                  }}
                >
                  <div style={{
                    maxWidth: '85%',
                    padding: message.type === 'ai' ? '1.25rem 1.5rem' : '0.75rem 1rem',
                    borderRadius: message.type === 'ai' ? '20px' : '16px',
                    backgroundColor: message.type === 'user' ? colors.primary : colors.bgElevated,
                    color: message.type === 'user' ? 'white' : colors.textPrimary,
                    fontSize: message.type === 'ai' ? '0.95rem' : '0.9rem',
                    lineHeight: message.type === 'ai' ? '1.6' : '1.4',
                    boxShadow: message.type === 'ai' ? '0 4px 12px rgba(0, 0, 0, 0.1)' : 'none',
                    border: message.type === 'ai' ? `1px solid ${colors.borderSubtle}` : 'none'
                  }}>
                    {message.type === 'ai' && message.content === 'avatar-customization' ? (
                      // Avatar自定义卡片
                      <div style={{
                        backgroundColor: colors.bgMain,
                        border: `2px solid ${colors.primary}`,
                        borderRadius: '16px',
                        padding: '1.5rem',
                        textAlign: 'center'
                      }}>
                        <div style={{
                          fontSize: '2rem',
                          marginBottom: '1rem'
                        }}>
                          🎨
                        </div>
                        
                        <h3 style={{
                          margin: '0 0 0.5rem 0',
                          color: colors.primary,
                          fontSize: '1.1rem'
                        }}>
                          Customize Your D-Pal Avatar
                        </h3>
                        
                        <p style={{
                          margin: '0 0 1.5rem 0',
                          color: colors.textSecondary,
                          fontSize: '0.9rem'
                        }}>
                          Choose an avatar that represents you. This will be your D-Pal&apos;s appearance during conversations.
                        </p>
                        
                        <div style={{
                          display: 'grid',
                          gridTemplateColumns: 'repeat(5, 1fr)',
                          gap: '0.75rem',
                          marginBottom: '1.5rem'
                        }}>
                          {avatarOptions.map((avatar, index) => (
                            <button
                              key={index}
                              onClick={() => setSelectedAvatar(avatar)}
                              style={{
                                width: '48px',
                                height: '48px',
                                borderRadius: '50%',
                                border: selectedAvatar === avatar ? `3px solid ${colors.primary}` : `2px solid ${colors.borderSubtle}`,
                                backgroundColor: selectedAvatar === avatar ? colors.primary + '20' : colors.bgCard,
                                fontSize: '1.5rem',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                                transform: selectedAvatar === avatar ? 'scale(1.1)' : 'scale(1)',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            >
                              {avatar}
                            </button>
                          ))}
                        </div>
                        
                        <div style={{
                          marginBottom: '1rem',
                          padding: '0.75rem',
                          backgroundColor: colors.bgElevated,
                          borderRadius: '8px',
                          border: `1px solid ${colors.borderSubtle}`
                        }}>
                          <div style={{ fontSize: '0.8rem', color: colors.textSecondary, marginBottom: '0.25rem' }}>Preview:</div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                            <span style={{ fontSize: '1.2rem' }}>{selectedAvatar}</span>
                            <span style={{ fontSize: '0.9rem', color: colors.textPrimary }}>D-Pal Assistant</span>
                          </div>
                        </div>
                        
                        <button
                          onClick={handleAvatarComplete}
                          style={{
                            backgroundColor: colors.primary,
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '0.75rem 1.5rem',
                            fontSize: '0.9rem',
                            fontWeight: '500',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0px)';
                            e.currentTarget.style.boxShadow = 'none';
                          }}
                        >
                          Complete Customization
                        </button>
                      </div>
                    ) : message.type === 'ai' && message.content === 'notification-setup' ? (
                      // 通知权限设置卡片
                      <div style={{
                        backgroundColor: colors.bgMain,
                        border: `2px solid ${colors.primary}`,
                        borderRadius: '16px',
                        padding: '1.5rem',
                        textAlign: 'center'
                      }}>
                        <div style={{
                          fontSize: '2rem',
                          marginBottom: '1rem'
                        }}>
                          🔔
                        </div>
                        
                        <h3 style={{
                          margin: '0 0 0.5rem 0',
                          color: colors.primary,
                          fontSize: '1.1rem'
                        }}>
                          Enable Smart Notifications
                        </h3>
                        
                        <p style={{
                          margin: '0 0 1.5rem 0',
                          color: colors.textSecondary,
                          fontSize: '0.9rem',
                          lineHeight: '1.5'
                        }}>
                          Allow D-Pal to send you notifications for better text selection and screenshot features. This helps me provide instant feedback and updates.
                        </p>
                        
                        <div style={{
                          backgroundColor: colors.bgElevated,
                          borderRadius: '12px',
                          padding: '1rem',
                          marginBottom: '1.5rem',
                          textAlign: 'left'
                        }}>
                          <div style={{ marginBottom: '0.75rem' }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.25rem' }}>
                              <span>✨</span>
                              <strong style={{ fontSize: '0.9rem', color: colors.textPrimary }}>Text Selection Features:</strong>
                            </div>
                            <p style={{ margin: 0, fontSize: '0.8rem', color: colors.textSecondary, paddingLeft: '1.5rem' }}>
                              Get instant translation, explanation, and summary options when you select text
                            </p>
                          </div>
                          
                          <div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.25rem' }}>
                              <span>📸</span>
                              <strong style={{ fontSize: '0.9rem', color: colors.textPrimary }}>Screenshot Analysis:</strong>
                            </div>
                            <p style={{ margin: 0, fontSize: '0.8rem', color: colors.textSecondary, paddingLeft: '1.5rem' }}>
                              AI-powered screenshot analysis with actionable insights and text extraction
                            </p>
                          </div>
                        </div>
                        
                        <button
                          onClick={handleNotificationSetup}
                          style={{
                            backgroundColor: colors.primary,
                            color: 'white',
                            border: 'none',
                            borderRadius: '8px',
                            padding: '0.75rem 1.5rem',
                            fontSize: '0.9rem',
                            fontWeight: '500',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0px)';
                            e.currentTarget.style.boxShadow = 'none';
                          }}
                        >
                          Enable Notifications
                        </button>
                        
                        <p style={{
                          margin: '1rem 0 0 0',
                          fontSize: '0.75rem',
                          color: colors.textTertiary,
                          fontStyle: 'italic'
                        }}>
                          💡 If prompted to restart the app for permissions, don&apos;t worry - you&apos;ll continue from this step after restart.
                        </p>
                      </div>
                    ) : message.type === 'ai' && message.content === 'notification-success' ? (
                      <div style={{ whiteSpace: 'pre-line' }}>
                        🎉 Excellent! Notifications are now enabled successfully!

Your D-Pal is now fully equipped with:
• ✨ Text selection tools (select any text to see translation, explanation, and summary options)
• 📸 Smart screenshot analysis
• 🔔 Real-time notifications for important updates

You should have received a test notification just now. Your smart features are ready to use!
                      </div>
                    ) : message.type === 'ai' && message.content === 'notification-denied' ? (
                      <div style={{ whiteSpace: 'pre-line' }}>
                        😔 Notification permission was denied. 

Don&apos;t worry! You can still use D-Pal&apos;s core features:
• Text selection tools will work without notifications
• Screenshot analysis remains available
• You can enable notifications later in your browser settings

To enable notifications later:
1. Click the 🔒 lock icon in your browser&apos;s address bar
2. Change notifications from &ldquo;Block&rdquo; to &ldquo;Allow&rdquo;
3. Refresh the page

Your D-Pal experience is still fully functional!
                      </div>
                    ) : message.type === 'ai' && message.content === 'notification-reminder' ? (
                      <div style={{ whiteSpace: 'pre-line' }}>
                        🤔 It looks like you haven&apos;t completed the notification setup yet.

To get the most out of your D-Pal experience, I recommend enabling notifications first. This will unlock:
• Instant feedback for text selection features
• Screenshot analysis notifications
• Important system updates

Please click the &ldquo;Enable Notifications&rdquo; button in the card above to continue, or feel free to skip if you prefer to set this up later.

Remember: If the system asks you to restart the app for permissions, you can safely do so - you&apos;ll return to this exact step after restart! 🔄
                       </div>
                     ) : message.type === 'ai' && message.id === 2 ? (
                      <div>
                        <p style={{ margin: '0 0 1rem 0' }}>Hi! I&apos;m your AI-powered D-Pal assistant. Here&apos;s what I can do for you:</p>
                        
                        <div style={{ marginBottom: '1.5rem' }}>
                          {[
                            { icon: '🌐', title: 'Text Translation', desc: 'Instantly translate content across multiple languages' },
                            { icon: '📝', title: 'Content Summarization', desc: 'Get quick summaries of lengthy documents' },
                            { icon: '💻', title: 'Code Assistance', desc: 'Review, optimize and debug your code' },
                            { icon: '📋', title: 'Task Management', desc: 'Organize your workflow and priorities' },
                            { icon: '✍️', title: 'Creative Writing', desc: 'Generate ideas, articles, and creative content' },
                            { icon: '📊', title: 'Data Analysis', desc: 'Process and interpret your data insights' }
                          ].map((item, index) => (
                            <div key={index} style={{ 
                              display: 'flex', 
                              alignItems: 'flex-start', 
                              marginBottom: '0.75rem',
                              gap: '0.75rem'
                            }}>
                              <span style={{ fontSize: '1.1rem', minWidth: '24px' }}>{item.icon}</span>
                              <div>
                                <strong style={{ color: colors.primary }}>{item.title}</strong>
                                <span style={{ color: colors.textSecondary, marginLeft: '0.5rem' }}>- {item.desc}</span>
                              </div>
                            </div>
                          ))}
                        </div>

                        <div style={{
                          backgroundColor: colors.bgMain,
                          padding: '1rem',
                          borderRadius: '12px',
                          border: `1px solid ${colors.borderSubtle}`,
                          marginBottom: '1rem'
                        }}>
                          <div style={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            marginBottom: '0.75rem',
                            gap: '0.5rem'
                          }}>
                            <span style={{ fontSize: '1.1rem' }}>🎯</span>
                            <strong style={{ color: colors.primary }}>Smart Features:</strong>
                          </div>
                          
                          <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: '0.5rem', gap: '0.75rem' }}>
                            <span style={{ fontSize: '1.1rem', minWidth: '24px' }}>✨</span>
                            <div>
                              <strong style={{ color: colors.textPrimary }}>Text Selection</strong>
                              <span style={{ color: colors.textSecondary, marginLeft: '0.5rem' }}>- Simply select any text to translate, explain, or summarize</span>
                            </div>
                          </div>
                          
                          <div style={{ display: 'flex', alignItems: 'flex-start', gap: '0.75rem' }}>
                            <span style={{ fontSize: '1.1rem', minWidth: '24px' }}>📸</span>
                            <div>
                              <strong style={{ color: colors.textPrimary }}>Smart Screenshots</strong>
                              <span style={{ color: colors.textSecondary, marginLeft: '0.5rem' }}>- Capture and analyze screenshots with AI insights</span>
                            </div>
                          </div>
                        </div>

                        <p style={{ margin: 0, fontStyle: 'italic', color: colors.textSecondary }}>
                          I&apos;m designed to learn your preferences and adapt to your working style. What would you like to explore first?
                        </p>
                      </div>
                    ) : (
                      <div style={{ whiteSpace: 'pre-line' }}>{message.content}</div>
                    )}
                  </div>
                </div>
                              ))}
                {/* 滚动定位元素 */}
                <div ref={messagesEndRef} />
              </div>

              {/* 输入区域 */}
            <div style={{
              padding: '1rem',
              borderTop: `1px solid ${colors.borderSubtle}`,
              display: 'flex',
              gap: '0.75rem'
            }}>
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Type your message..."
                style={{
                  flex: 1,
                  padding: '0.75rem 1rem',
                  borderRadius: '12px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgElevated,
                  color: colors.textPrimary,
                  fontSize: '0.9rem',
                  outline: 'none'
                }}
              />
              <button
                onClick={handleSendMessage}
                style={{
                  padding: '0.75rem 1rem',
                  borderRadius: '12px',
                  border: 'none',
                  backgroundColor: colors.primary,
                  color: 'white',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
              >
                Send
              </button>
            </div>
          </div>
        );
  };

  return (
    <div style={{
      height: '100vh',
      width: '100vw',
      backgroundColor: colors.bgMain,
      color: colors.textPrimary,
      overflow: 'hidden',
      position: 'fixed',
      top: 0,
      left: 0,
      zIndex: 1000
    }}>
      {/* 关闭按钮 */}
      <button
        onClick={handleClose}
        style={{
          position: 'fixed',
          top: '1rem',
          right: '1rem',
          backgroundColor: colors.bgCard,
          border: `1px solid ${colors.borderSubtle}`,
          color: colors.textSecondary,
          fontSize: '1.5rem',
          cursor: 'pointer',
          padding: '0.75rem',
          borderRadius: '12px',
          transition: 'all 0.2s ease',
          zIndex: 1000
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = colors.bgSubtle;
          e.currentTarget.style.color = colors.textPrimary;
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = colors.bgCard;
          e.currentTarget.style.color = colors.textSecondary;
        }}
      >
        ×
      </button>

      {/* 主容器 - 左右布局 */}
      <div style={{
        display: 'flex',
        height: '100vh',
        gap: '1.5rem',
        padding: '1.5rem'
      }}>
        {/* 左侧：步骤讲解 */}
        <div style={{
          width: '480px',
          backgroundColor: colors.bgCard,
          borderRadius: '24px',
          padding: '2.5rem',
          border: `1px solid ${colors.borderSubtle}`,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5)',
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100vh - 3rem)'
        }}>
          {/* 进度指示器 */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '2rem',
            gap: '0.5rem'
          }}>
            {steps.map((_, index) => (
              <div
                key={index}
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: index <= currentStep ? colors.primary : colors.bgSubtle,
                  transition: 'all 0.3s ease'
                }}
              />
            ))}
          </div>

          {/* 内容区域 */}
          <div style={{
            textAlign: 'center',
            marginBottom: '3rem',
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center'
          }}>
            <div style={{
              fontSize: '4rem',
              marginBottom: '1.5rem',
              animation: 'bounce 2s infinite'
            }}>
              {steps[currentStep].icon}
            </div>

            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              marginBottom: '1rem',
              background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              {steps[currentStep].title}
            </h1>

            <p style={{
              fontSize: '1.1rem',
              color: colors.textSecondary,
              lineHeight: '1.6'
            }}>
              {steps[currentStep].description}
            </p>
          </div>

          {/* 操作按钮 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <button
              onClick={handlePrev}
              disabled={currentStep === 0}
              style={{
                backgroundColor: 'transparent',
                border: `1px solid ${colors.borderSubtle}`,
                borderRadius: '12px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.9rem',
                cursor: currentStep === 0 ? 'not-allowed' : 'pointer',
                color: currentStep === 0 ? colors.textTertiary : colors.textSecondary,
                transition: 'all 0.2s ease',
                opacity: currentStep === 0 ? 0.5 : 1
              }}
              onMouseEnter={(e) => {
                if (currentStep > 0) {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                  e.currentTarget.style.color = colors.textPrimary;
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = currentStep === 0 ? colors.textTertiary : colors.textSecondary;
              }}
            >
              Previous
            </button>

            <div style={{
              fontSize: '0.9rem',
              color: colors.textTertiary
            }}>
              {currentStep + 1} of {steps.length}
            </div>

            <button
              onClick={currentStep === steps.length - 1 ? handleClose : handleNext}
              style={{
                backgroundColor: colors.primary,
                border: 'none',
                borderRadius: '12px',
                padding: '0.75rem 1.5rem',
                fontSize: '0.9rem',
                cursor: 'pointer',
                color: 'white',
                fontWeight: '500',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 16px rgba(59, 130, 246, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0px)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              {currentStep === steps.length - 1 ? 'Get Started' : 'Next'}
            </button>
          </div>
        </div>

        {/* 右侧：展示区域 */}
        <div style={{
          flex: 1,
          height: 'calc(100vh - 3rem)'
        }}>
          {renderRightContent()}
        </div>
      </div>

      {/* CSS 动画 */}
      <style jsx>{`
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-10px);
          }
          60% {
            transform: translateY(-5px);
          }
        }
      `}</style>
    </div>
  );
} 