import { CSConsultation } from '../types';

/**
 * 用户等级处理工具函数
 * @param rank 用户积分等级
 * @returns 返回对应的R等级标签
 */
export const getUserRankLevel = (rank: number): string => {
  if (rank >= 3000) return 'R15';
  if (rank >= 2500) return 'R14';
  if (rank >= 2000) return 'R13';
  if (rank >= 1800) return 'R12';
  if (rank >= 1500) return 'R11';
  if (rank >= 1200) return 'R10';
  if (rank >= 1000) return 'R9';
  if (rank >= 800) return 'R8';
  if (rank >= 600) return 'R7';
  if (rank >= 500) return 'R6';
  if (rank >= 400) return 'R5';
  if (rank >= 300) return 'R4';
  if (rank >= 200) return 'R3';
  if (rank >= 100) return 'R2';
  if (rank >= 50) return 'R1';
  return ''; // 没有R等级标签
};

/**
 * 根据类型获取对应的颜色
 * @param type 类型字符串
 * @returns 返回对应的颜色代码
 */
export const getTypeColor = (type: string): string => {
  switch(type) {
    case 'Payment': return '#1677ff';
    case 'Refund': return '#52c41a';
    case 'Subscription': return '#722ed1';
    case 'Technical': return '#fa8c16';
    case 'Billing': return '#eb2f96';
    case 'Reward': return '#faad14';
    case 'Purchase': return '#13c2c2';
    case 'Account': return '#2f54eb';
    default: return '#8c8c8c';
  }
};

/**
 * 根据类型获取对应的图标
 * @param type 类型字符串
 * @returns 返回对应的图标
 */
export const getTypeIcon = (type: string): string => {
  switch(type) {
    case 'Payment': return '$';
    case 'Refund': return '↩';
    case 'Subscription': return '📋';
    case 'Technical': return '🔧';
    case 'Billing': return '🏦';
    case 'Reward': return '🎁';
    case 'Purchase': return '🛒';
    case 'Account': return '👤';
    default: return '?';
  }
};

/**
 * CS Agent 筛选逻辑
 * @param consultations CS咨询数组
 * @param tab 当前标签页
 * @param searchText 搜索文本
 * @param waitingFilter 是否只显示等待回复的
 * @returns 过滤后的CS咨询数组
 */
export const filterCSConsultations = (
  consultations: CSConsultation[], 
  tab: 'all' | 'vip' | 'non-vip',
  searchText: string,
  waitingFilter: boolean
): CSConsultation[] => {
  return consultations.filter(consultation => {
    // 搜索文本过滤
    const matchesSearch = searchText ? 
      consultation.title.toLowerCase().includes(searchText.toLowerCase()) || 
      consultation.lastMessage.toLowerCase().includes(searchText.toLowerCase()) ||
      consultation.userName.toLowerCase().includes(searchText.toLowerCase()) :
      true;
    
    // 等待回复过滤
    const matchesWaiting = waitingFilter ? consultation.status === 'pending' : true;
    
    // VIP过滤
    let matchesTab = true;
    if (tab === 'vip') {
      matchesTab = consultation.isVip;
    } else if (tab === 'non-vip') {
      matchesTab = !consultation.isVip;
    }
    
    return matchesSearch && matchesWaiting && matchesTab;
  }).sort((a, b) => b.userRank - a.userRank); // 按userRank降序排序
};

/**
 * 通知排序函数：urgent优先级置顶
 * @param notifications 通知数组
 * @returns 排序后的通知数组
 */
export const sortNotificationsByPriority = (notifications: any[]) => {
  const priorityOrder = { 'urgent': 1, 'high': 2, 'medium': 3, 'low': 4 };
  return [...notifications].sort((a, b) => {
    const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 5;
    const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 5;
    if (aPriority !== bPriority) {
      return aPriority - bPriority;
    }
    // 如果优先级相同，按时间排序（最新的在前）
    return new Date(b.time).getTime() - new Date(a.time).getTime();
  });
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
}; 