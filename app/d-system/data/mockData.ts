import { 
  Message, 
  CSConsultation, 
  Project, 
  WorkflowTemplate, 
  UserInfo, 
  RelatedConversation, 
  Task, 
  DailyReportData, 
  SubscriptionTask, 
  Notification, 
  NavGroup,
  ComplexSession,
  TeamMember,
  KnowledgeTask,
  KnowledgeDetailData,
  QAPair,
  KnowledgeSuggestion,
  KnowledgeBaseItem,
  EvaluationTask,
  EvaluationDetailData
} from '../types';

// 示例聊天历史
export const sampleChatHistory: Message[] = [
  {
    id: 1,
    type: 'ai',
    content: [{ type: 'text', content: 'Hello! I\'m your AI assistant. How can I help you today?' }],
    timestamp: '09:30 AM'
  },
  {
    id: 2,
    type: 'user',
    content: 'I need to create a new project plan for the Q3 marketing campaign.',
    timestamp: '09:31 AM',
    attachments: []
  },
  {
    id: 3,
    type: 'ai',
    content: [{ type: 'text', content: 'I\'d be happy to help you create a project plan for the Q3 marketing campaign. Let\'s start with defining the key objectives, timeline, and resources needed. What are the main goals for this campaign?' }],
    timestamp: '09:31 AM'
  }
];

// 工作流模板
export const workflowTemplates: WorkflowTemplate[] = [
  {
    id: 1,
    title: 'New Document Request',
    icon: 'document',
    category: 'Document',
    description: 'Create and collaborate on new documents with team members'
  },
  {
    id: 2,
    title: 'Leave Application',
    icon: 'calendar',
    category: 'HR',
    description: 'Apply for vacation, sick leave or other time off'
  },
  {
    id: 3,
    title: 'Expense Reimbursement',
    icon: 'dollar',
    category: 'Finance',
    description: 'Submit expenses for reimbursement approval'
  },
  {
    id: 4,
    title: 'Project Planning',
    icon: 'chart',
    category: 'Work',
    description: 'Create and manage project plans and timelines'
  },
  {
    id: 5,
    title: 'Task Assignment',
    icon: 'list',
    category: 'Work',
    description: 'Create and assign tasks to team members'
  },
  {
    id: 6,
    title: 'Performance Review',
    icon: 'check',
    category: 'HR',
    description: 'Conduct or request performance evaluations'
  }
];

// CS Agent 会话数据
export const csAgentConsultations: CSConsultation[] = [
  {
    id: 'EC-2023-001',
    title: 'Premium features unavailable after payment',
    lastMessage: 'User cannot access their premium features after payment',
    timestamp: '2023-05-12 14:30:22',
    unread: true,
    status: 'active',
    userRank: 1000,
    game: 'gate',
    type: 'Payment',
    userName: 'Yu Binbin',
    isVip: true
  },
  {
    id: 'EC-2023-002',
    title: 'Damaged product return request',
    lastMessage: 'Customer wants to return a damaged product',
    timestamp: '2023-05-12 11:45:15',
    unread: false,
    status: 'active',
    userRank: 500,
    game: 'gate',
    type: 'Refund',
    userName: 'Lin Jiahao',
    isVip: true
  },
  {
    id: 'EC-2023-003',
    title: 'Subscription renewal failure',
    lastMessage: 'Renewal failed due to payment method expiration',
    timestamp: '2023-05-11 09:20:33',
    unread: false,
    status: 'closed',
    userRank: 2500,
    game: 'doraemon',
    type: 'Subscription',
    userName: 'Wang Wei',
    isVip: true
  },
  {
    id: 'EC-2023-004',
    title: 'App crashes on iOS startup',
    lastMessage: 'App crashes consistently on startup for iOS users',
    timestamp: '2023-05-10 16:50:11',
    unread: false,
    status: 'pending',
    userRank: 300,
    game: 'yugioh',
    type: 'Technical',
    userName: 'Zhang Min',
    isVip: true
  },
  {
    id: 'EC-2023-005',
    title: 'Double billing for single order',
    lastMessage: 'Customer was charged twice for the same order',
    timestamp: '2023-05-10 10:15:44',
    unread: true,
    status: 'active',
    userRank: 1200,
    game: 'gate',
    type: 'Billing',
    userName: 'Li Xiaoping',
    isVip: true
  },
  {
    id: 'EC-2023-006',
    title: 'Weekly login bonus not received',
    lastMessage: 'VIP user did not receive scheduled login bonus',
    timestamp: '2023-05-09 08:25:30',
    unread: true,
    status: 'pending',
    userRank: 3500,
    game: 'yugioh',
    type: 'Reward',
    userName: 'Chen Wei',
    isVip: true
  },
  {
    id: 'EC-2023-007',
    title: 'Exclusive skin purchase error',
    lastMessage: 'Limited edition skin purchased but not showing in inventory',
    timestamp: '2023-05-08 17:10:22',
    unread: false,
    status: 'pending',
    userRank: 1850,
    game: 'doraemon',
    type: 'Purchase',
    userName: 'Liu Yang',
    isVip: true
  },
  {
    id: 'EC-2023-008',
    title: 'Account registration issue',
    lastMessage: 'Unable to complete email verification process',
    timestamp: '2023-05-09 14:42:15',
    unread: true,
    status: 'active',
    userRank: 20,
    game: 'gate',
    type: 'Account',
    userName: 'Zhao Ling',
    isVip: false
  },
  {
    id: 'EC-2023-009',
    title: 'Basic gameplay question',
    lastMessage: 'New player asking about basic game mechanics',
    timestamp: '2023-05-08 16:20:10',
    unread: false,
    status: 'closed',
    userRank: 10,
    game: 'yugioh',
    type: 'Support',
    userName: 'Li Mei',
    isVip: false
  },
  {
    id: 'EC-2023-010',
    title: 'Password reset request',
    lastMessage: 'User forgot password and needs assistance',
    timestamp: '2023-05-07 12:30:45',
    unread: true,
    status: 'pending',
    userRank: 5,
    game: 'doraemon',
    type: 'Account',
    userName: 'Wang Xiao',
    isVip: false
  }
];

// i18n Expert 会话数据 - 专注于多语言本地化问题
export const i18nAgentConsultations: CSConsultation[] = [
  {
    id: 'I18N-2023-001',
    title: 'Japanese text display issue in mobile app',
    lastMessage: 'Characters not displaying correctly on iOS devices',
    timestamp: '2023-05-12 15:20:33',
    unread: true,
    status: 'active',
    userRank: 2200,
    game: 'yugioh',
    type: 'Localization',
    userName: 'Tanaka Hiroshi',
    isVip: true
  },
  {
    id: 'I18N-2023-002',
    title: 'Korean translation missing for new features',
    lastMessage: 'Latest update shows English text instead of Korean',
    timestamp: '2023-05-12 13:45:22',
    unread: true,
    status: 'active',
    userRank: 1800,
    game: 'gate',
    type: 'Translation',
    userName: 'Kim Min-jun',
    isVip: true
  },
  {
    id: 'I18N-2023-003',
    title: 'Chinese font rendering problems',
    lastMessage: 'Traditional Chinese characters appear as squares',
    timestamp: '2023-05-11 16:30:15',
    unread: false,
    status: 'pending',
    userRank: 3200,
    game: 'doraemon',
    type: 'Font',
    userName: 'Chen Wei-ming',
    isVip: true
  },
  {
    id: 'I18N-2023-004',
    title: 'Right-to-left text alignment issue',
    lastMessage: 'Arabic text not aligning properly in UI elements',
    timestamp: '2023-05-11 09:15:44',
    unread: false,
    status: 'closed',
    userRank: 950,
    game: 'gate',
    type: 'Layout',
    userName: 'Ahmed Al-Rashid',
    isVip: true
  },
  {
    id: 'I18N-2023-005',
    title: 'Currency format incorrect for Euro region',
    lastMessage: 'Price display shows wrong decimal separator',
    timestamp: '2023-05-10 14:20:30',
    unread: true,
    status: 'active',
    userRank: 1500,
    game: 'yugioh',
    type: 'Currency',
    userName: 'Marie Dubois',
    isVip: true
  },
  {
    id: 'I18N-2023-006',
    title: 'Time zone conversion error',
    lastMessage: 'Event times showing incorrect timezone for Brazil',
    timestamp: '2023-05-10 11:45:20',
    unread: true,
    status: 'pending',
    userRank: 780,
    game: 'doraemon',
    type: 'DateTime',
    userName: 'Carlos Silva',
    isVip: false
  },
  {
    id: 'I18N-2023-007',
    title: 'Voice-over missing in Spanish version',
    lastMessage: 'Cutscenes have no audio in Spanish language setting',
    timestamp: '2023-05-09 17:30:55',
    unread: false,
    status: 'pending',
    userRank: 1200,
    game: 'gate',
    type: 'Audio',
    userName: 'Isabella Rodriguez',
    isVip: true
  },
  {
    id: 'I18N-2023-008',
    title: 'Text overflow in German translation',
    lastMessage: 'Button text gets cut off due to longer German phrases',
    timestamp: '2023-05-09 10:20:18',
    unread: false,
    status: 'active',
    userRank: 2100,
    game: 'yugioh',
    type: 'UI Layout',
    userName: 'Hans Mueller',
    isVip: true
  },
  {
    id: 'I18N-2023-009',
    title: 'Incorrect plural forms in Russian',
    lastMessage: 'Item count displays wrong plural endings',
    timestamp: '2023-05-08 12:15:30',
    unread: false,
    status: 'closed',
    userRank: 1650,
    game: 'doraemon',
    type: 'Grammar',
    userName: 'Alexei Volkov',
    isVip: true
  },
  {
    id: 'I18N-2023-010',
    title: 'Thai keyboard input not working',
    lastMessage: 'Cannot type Thai characters in chat or search',
    timestamp: '2023-05-07 15:45:12',
    unread: true,
    status: 'pending',
    userRank: 320,
    game: 'gate',
    type: 'Input',
    userName: 'Somchai Jaidee',
    isVip: false
  }
];

// 模拟用户信息数据
export const mockUserInfo: UserInfo = {
  game: {
    name: 'gate',
    description: 'GATE 自衛隊 彼の地にて、斯く戦えり',
    team: 'FRONTLINE UNION'
  },
  user: {
    g123Id: 'G1HTPALWS',
    serverId: '10001',
    gameUid: 'G1HTPALWS',
    roleId: '10001_h2iuqAmxt',
    im: 'line',
    rank: '1000',
    amount: '¥1,234,567,890',
    vipLevel: 'VIP1',
    system: 'Mac OS 10.15.7 | Chrome 116.0'
  }
};

// 模拟关联会话数据
export const mockRelatedConversations: RelatedConversation[] = [
  {
    id: 'RC-2023-001',
    firstUserMessage: 'My payment was processed but I still cannot access premium features. Can you help?',
    endTime: '2023-04-15 16:32:45',
  },
  {
    id: 'RC-2023-002',
    firstUserMessage: 'I want to know what premium features are included before purchasing.',
    endTime: '2023-03-22 10:15:30',
  }
];

// Mock project & session data
export const mockProjects: Project[] = [
  {
    id: 'cs-agent',
    name: 'CS Expert',
    sessions: [], // CS Expert 项目使用特殊的会话列表
    isSpecial: true, // 标记为特殊项目
  },
  {
    id: 'p1',
    name: 'AI Research',
    sessions: [
      { id: 's1', name: 'Prompt Engineering' },
      { id: 's2', name: 'Model Evaluation' },
    ],
  },
  {
    id: 'p2',
    name: 'Infra Upgrade',
    sessions: [
      { id: 's3', name: 'Server Migration' },
      { id: 's4', name: 'CI/CD Pipeline' },
    ],
  },
  {
    id: 'p3',
    name: 'Product Launch',
    sessions: [
      { id: 's5', name: 'Go-to-Market' },
      { id: 's6', name: 'Feedback Loop' },
    ],
  },
];

// 导航组
export const NAV_GROUPS: NavGroup[] = [
  { key: 'home', label: 'Home', icon: 'home' },
  { key: 'projects', label: 'Projects', icon: 'folder' },
  { key: 'knowledge', label: 'Knowledge', icon: 'book' },
];

// Mock tasks
const today = new Date().toISOString().slice(0, 10);
export const mockTasks: Task[] = [
  { id: 1, title: 'Submit leave application', due: today, ddl: '15:00', status: 'pending' },
  { id: 2, title: 'Book meeting room for 2pm', due: today, ddl: '14:00', status: 'pending' },
  { id: 3, title: 'Request Figma access', due: today, ddl: '17:30', status: 'pending' },
  { id: 4, title: 'Check project progress', due: '2024-06-01', ddl: '18:00', status: 'pending' },
  { id: 5, title: 'Plan marketing campaign', due: '2024-06-02', ddl: '10:00', status: 'pending' },
  { id: 6, title: 'Onboarding new member', due: '2024-06-03', ddl: '09:30', status: 'pending' },
];

// 模拟早会日报数据
export const dailyReportData: DailyReportData = {
  date: '2025-05-19',
  team: 'AI 助手团队',
  participants: ['张三', '李四', '王五', '赵六'],
  agenda: [
    {
      id: '1',
      type: '进展汇报',
      content: '完成了用户反馈分析系统的主体功能开发',
      owner: '张三',
      status: 'completed'
    },
    {
      id: '2',
      type: '问题反馈',
      content: '模型响应时间偶尔超过预期，需要优化',
      owner: '李四',
      status: 'pending'
    },
    {
      id: '3',
      type: '计划安排',
      content: '本周将开始新的知识库优化项目',
      owner: '王五',
      status: 'upcoming'
    }
  ],
  nextSteps: [
    '优化模型响应时间',
    '扩展知识库覆盖范围',
    '提升用户体验满意度'
  ]
};

// 模拟订阅任务数据
export const subscriptionTasks: SubscriptionTask[] = [
  {
    id: 'TASK-001',
    title: '早会日报',
    type: 'daily-report',
    time: '2025-05-19 09:30',
    status: 'new',
    priority: 'high',
    content: dailyReportData,
  },
  {
    id: 'TASK-002',
    title: '周报提醒',
    type: 'weekly-report',
    time: '2025-05-19 18:00',
    status: 'upcoming',
    priority: 'medium',
    content: {
      deadline: '2025-05-19 20:00',
      template: '标准周报模板'
    }
  },
  {
    id: 'TASK-003',
    title: '项目里程碑',
    type: 'milestone',
    time: '2025-05-20 10:00',
    status: 'upcoming',
    priority: 'high',
    content: {
      project: 'AI 助手优化',
      milestone: 'V2.0 发布'
    }
  }
];

// 模拟通知数据
export const notificationData: Notification[] = [
  // 只保留与气泡对应的两个urgent通知（置顶且不可删除）
  {
    id: 'n_urgent_1',
    type: 'task',
    title: 'CS Agent: VIP Reward System Issue - URGENT',
    time: '2025-05-19 08:30',
    priority: 'urgent',
    isDeletable: false, // urgent通知不可删除
    content: {
      caseNumber: 'EC-2023-006',
      issueType: 'VIP reward not received',
      impact: 'High',
      description: 'VIP user Chen Wei (Rank 3500) did not receive weekly login bonus in YuGiOh game'
    }
  },
  {
    id: 'n_urgent_2',
    type: 'alert',
    title: 'CS Agent: Payment Processing Alert - URGENT',
    time: '2025-05-19 08:15',
    priority: 'urgent',
    isDeletable: false, // urgent通知不可删除
    content: {
      caseNumber: 'EC-2023-001',
      alertType: 'Payment verification failed',
      impact: 'Critical',
      description: 'Premium features access blocked for user Yu Binbin after successful payment'
    }
  },
  // 普通通知（可删除）
  {
    id: 'n1',
    type: 'schedule',
    title: 'Team Meeting Reminder',
    time: '2025-05-19 09:00',
    priority: 'high',
    isDeletable: true,
    content: {
      eventType: 'team meeting',
      startTime: '10:00 AM',
      duration: '1 hour',
      location: 'Conference Room A',
      attendees: ['Alice', 'Bob', 'Charlie']
    }
  },
  {
    id: 'n2',
    type: 'auto-plan',
    title: 'Daily Plan Generated',
    time: '2025-05-19 08:00',
    priority: 'medium',
    isDeletable: true,
    content: {
      planType: 'daily',
      totalTasks: 8,
      completedTasks: 3,
      upcomingDeadlines: 2,
      suggestions: ['Focus on high-priority items', 'Schedule break between meetings']
    }
  },
  {
    id: 'n3',
    type: 'reminder',
    title: 'Document Review Required',
    time: '2025-05-19 07:30',
    priority: 'medium',
    isDeletable: true,
    content: {
      taskType: 'document review',
      department: 'Legal',
      deadline: '2025-05-19 17:00',
      documents: ['Contract Amendment', 'Privacy Policy Update']
    }
  },
  {
    id: 'n4',
    type: 'task',
    title: 'Weekly Report Due',
    time: '2025-05-18 16:20',
    priority: 'low',
    isDeletable: true,
    content: {
      taskType: 'weekly report',
      department: 'Development',
      deadline: '2025-05-19 18:00',
      template: 'Standard weekly template'
    }
  },
  {
    id: 'n5',
    type: 'auto-plan',
    title: 'Project Milestone Approaching',
    time: '2025-05-18 14:15',
    priority: 'high',
    isDeletable: true,
         content: {
       planType: 'milestone',
       project: 'AI Assistant Enhancement',
       milestone: 'Phase 2 Completion',
       daysRemaining: 3,
       completionRate: '75%'
     }
   }
 ];

// i18n Agent 聊天数据
export const i18nAgentChatData: Record<string, Message[]> = {
  'I18N-2023-001': [
    {
      id: 1,
      type: 'ai',
      content: 'Localization Priority Alert! 🌐\n\nJapanese mobile users are reporting text display issues. Issue: "Japanese text appears garbled on mobile devices"\n\n**Affected Area**: Mobile UI elements\n**Language**: Japanese (JP)\n**Device Impact**: iOS and Android\n\nRequesting immediate i18n expert assistance. @localization-team @mobile-dev @product-design',
      timestamp: '09:15',
      attachments: []
    },
    {
      id: 2,
      type: 'user',
      content: '[Expert Tanaka] I can help with this Japanese localization issue. Let me check the character encoding and font rendering settings.',
      timestamp: '09:17',
      attachments: []
    },
    {
      id: 3,
      type: 'ai',
      content: 'Thank you @Tanaka! User details:\n• Device: iPhone 14 Pro with iOS 16.5\n• Issue: Kanji characters showing as squares\n• Reproducible across multiple Japanese users\n• English text displays correctly\n\nThis is affecting user engagement in our Japanese market segment.',
      timestamp: '09:18',
      attachments: []
    },
    {
      id: 4,
      type: 'user',
      content: '[Expert Tanaka] Found the issue! The mobile app is missing the proper font fallback for Hiragana and Kanji characters. I\'ve updated the font stack to include "Noto Sans JP" as primary and "Yu Gothic" as fallback. Changes deployed to mobile.',
      timestamp: '09:25',
      attachments: []
    },
    {
      id: 5,
      type: 'ai',
      content: 'Excellent! ✅ Japanese users confirmed the text display is now working correctly. Thank you @Tanaka for the quick resolution!\n\nI\'ll document this in our i18n knowledge base to prevent similar issues with other CJK languages.',
      timestamp: '09:30',
      attachments: []
    }
  ],
  'I18N-2023-005': [
    {
      id: 1,
      type: 'ai',
      content: 'Currency Formatting Issue! 💰\n\nEuropean users reporting incorrect Euro price display.\n\n**Customer Report**: "Prices show 99.99 instead of 99,99 for European region"\n\n**Problem**: Decimal separator not following European conventions\n**Affected**: All Euro currency transactions\n**User Impact**: Confusion about actual pricing\n\nNeed i18n expert to resolve currency formatting. @localization-team @payment-systems',
      timestamp: '14:20',
      attachments: []
    },
    {
      id: 2,
      type: 'user',
      content: 'I\'ll handle this currency formatting issue. Let me check the localization settings for European regions.',
      timestamp: '14:22',
      attachments: []
    },
    {
      id: 3,
      type: 'ai',
      content: 'Thank you! Additional context:\n• Affected countries: Germany, France, Italy, Spain\n• Current format: 99.99 EUR (incorrect)\n• Expected format: 99,99 EUR (European standard)\n• Issue present in checkout and pricing pages\n\nUser Marie Dubois from France specifically mentioned this causes confusion.',
      timestamp: '14:23',
      attachments: []
    },
    {
      id: 4,
      type: 'user',
      content: 'Issue identified! The currency formatter was using US decimal separator (.) instead of European comma (,) for Euro regions. I\'ve updated the Intl.NumberFormat configuration to use proper European locale settings. Testing now.',
      timestamp: '14:28',
      attachments: []
    },
    {
      id: 5,
      type: 'ai',
      content: '🎉 Perfect! Marie confirmed the pricing now displays correctly as "99,99 EUR" throughout the application. \n\nI\'ve also verified with users from Germany and Italy - all European currency formatting is now compliant with regional standards. Great work on the locale-specific fixes!',
             timestamp: '14:35',
       attachments: []
     }
   ],
   'I18N-2023-002': [
     {
       id: 1,
       type: 'ai',
       content: 'Korean Localization Alert! 🇰🇷\n\nKorean users reporting missing translations for new features.\n\n**Issue**: "Latest update shows English text instead of Korean"\n**Affected**: UI buttons, menus, and feature descriptions\n**Priority**: High - Korean is a major market\n\nRequesting i18n expert assistance. @localization-team @korean-translators',
       timestamp: '13:45',
       attachments: []
     },
     {
       id: 2,
       type: 'user',
       content: '[Expert Kim] I can help with the Korean translation issue. Let me check the localization files and missing string keys.',
       timestamp: '13:47',
       attachments: []
     },
     {
       id: 3,
       type: 'ai',
       content: 'Thank you @Kim! Additional context:\n• User Kim Min-jun (VIP) reported this first\n• Missing translations in: Settings menu, Premium features, Help section\n• Korean market represents 25% of our user base\n• Users are frustrated and considering switching to competitors',
       timestamp: '13:48',
       attachments: []
     },
     {
       id: 4,
       type: 'user',
       content: '[Expert Kim] Found the issue! The new feature strings were not added to the Korean locale file (ko-KR.json). I\'ve translated and added all missing strings. Deploying Korean language pack update now.',
       timestamp: '13:55',
       attachments: []
     },
     {
       id: 5,
       type: 'ai',
       content: '🎉 Excellent work! Kim Min-jun confirmed all Korean text is now displaying correctly. Korean users are very satisfied with the quick resolution!\n\nI\'ll update our release process to ensure Korean translations are included before feature rollouts.',
       timestamp: '14:00',
       attachments: []
     }
   ],
   'I18N-2023-003': [
     {
       id: 1,
       type: 'ai',
       content: 'Font Rendering Emergency! 🔤\n\nChinese users experiencing character display issues.\n\n**Problem**: "Traditional Chinese characters appear as squares"\n**Scope**: Affects Traditional Chinese (zh-TW) users\n**Impact**: Users cannot read game content\n\nNeed immediate font specialist support. @font-team @chinese-localization',
       timestamp: '16:30',
       attachments: []
     },
     {
       id: 2,
       type: 'user',
       content: 'I\'ll investigate this Chinese font rendering issue. This could be a serious blocker for our Taiwan and Hong Kong users.',
       timestamp: '16:32',
       attachments: []
     },
     {
       id: 3,
       type: 'ai',
       content: 'Critical details:\n• User Chen Wei-ming from Taiwan reported this\n• Affects complex Traditional Chinese characters (繁體中文)\n• Simplified Chinese (zh-CN) works fine\n• Issue appeared after latest app update\n• 15% of our Asian user base uses Traditional Chinese',
       timestamp: '16:33',
       attachments: []
     },
     {
       id: 4,
       type: 'user',
       content: 'Root cause identified! The app update removed "PingFang TC" font fallback for Traditional Chinese. I\'ve restored the font stack and added "Microsoft JhengHei" as additional fallback. Testing with Taiwan users now.',
       timestamp: '16:40',
       attachments: []
     },
     {
       id: 5,
       type: 'ai',
       content: '✅ Perfect resolution! Chen Wei-ming and other Taiwan users confirm Traditional Chinese characters are rendering beautifully again.\n\nI\'ve documented this in our font fallback guidelines to prevent future Traditional Chinese rendering issues.',
       timestamp: '16:45',
       attachments: []
     }
   ],
   'I18N-2023-004': [
     {
       id: 1,
       type: 'ai',
       content: 'RTL Layout Issue! ↔️\n\nArabic text alignment problem reported.\n\n**Issue**: "Arabic text not aligning properly in UI elements"\n**Language**: Arabic (RTL)\n**Problem**: Text direction conflicts with UI layout\n\nNeed RTL specialist assistance. @rtl-team @arabic-localization @ui-team',
       timestamp: '09:15',
       attachments: []
     },
     {
       id: 2,
       type: 'user',
       content: '[Expert Rashid] I\'m experienced with RTL layouts. Let me examine the Arabic text alignment issues and CSS direction settings.',
       timestamp: '09:17',
       attachments: []
     },
     {
       id: 3,
       type: 'ai',
       content: 'Thank you @Rashid! User Ahmed Al-Rashid reported:\n• Arabic text flowing left-to-right instead of right-to-left\n• UI icons not mirroring for RTL layout\n• Navigation menu order incorrect\n• Arabic users find interface confusing',
       timestamp: '09:18',
       attachments: []
     },
     {
       id: 4,
       type: 'user',
       content: '[Expert Rashid] Issue resolved! Added proper dir="rtl" attribute and CSS logical properties for Arabic locale. Also implemented icon mirroring for navigation elements. Arabic layout now follows RTL conventions correctly.',
       timestamp: '09:30',
       attachments: []
     },
     {
       id: 5,
       type: 'ai',
       content: '🌟 Fantastic! Ahmed confirmed the Arabic interface now feels natural and intuitive. RTL layout is working perfectly!\n\nThis fix will benefit all our Arabic-speaking users across the Middle East region.',
       timestamp: '09:35',
       attachments: []
     }
   ]
 };

// CS Agent 聊天数据
export const csAgentChatData: Record<string, Message[]> = {
  'EC-2023-001': [
    {
      id: 1,
      type: 'ai',
      content: 'Urgent assistance needed! 🚨\n\nVIP customer Yu Binbin (R12) is experiencing a payment issue. User says: "I paid for premium features but they\'re still locked in GATE game"\n\nI need help from our experts to resolve this quickly. @tech-support @billing-team @product-manager',
      timestamp: '14:30',
      attachments: []
    },
    {
      id: 2,
      type: 'user',
      content: '[Expert Sarah] I can help with this payment issue. Let me check the payment status and premium feature access in our system.',
      timestamp: '14:32',
      attachments: []
    },
    {
      id: 3,
      type: 'ai',
      content: 'Thank you @Sarah! The user has confirmed:\n• Payment went through successfully (Transaction ID: TXN-2025-051934)\n• Premium features still showing as locked\n• User is frustrated and considering refund\n\nThis is affecting our VIP customer experience. Can you please check the feature unlock service?',
      timestamp: '14:33',
      attachments: []
    },
    {
      id: 4,
      type: 'user',
      content: '[Expert Sarah] I found the issue! There\'s a sync delay between payment service and feature unlock service. I\'ve manually triggered the premium access for this user. @Yu-Binbin should see premium features active within 5 minutes.',
      timestamp: '14:35',
      attachments: []
    },
    {
      id: 5,
      type: 'ai',
      content: 'Perfect! I\'ve notified the customer about the fix. They confirmed premium features are now working. Thank you @Sarah for the quick resolution! 🎉\n\nI\'ll also alert @dev-team about the sync delay issue to prevent future occurrences.',
      timestamp: '14:37',
      attachments: []
    }
  ],
  'EC-2023-006': [
    {
      id: 1,
      type: 'ai',
      content: 'TOP VIP PRIORITY ALERT! 👑\n\nR15 VIP user Chen Wei did not receive scheduled weekly login bonus.\n\n🔗 **CS System Direct Link**: [/cs-system/expert-consults](http://localhost:3000/cs-system/expert-consults)\n\n**Options to proceed:**\n1. 📧 Click the link above to respond directly in CS System\n2. 🤖 Let me assist you - I can help draft your response\n\n**Customer Issue**: "I\'m R15 rank - highest tier - but didn\'t get my 5000 gem weekly bonus"\n\n**Priority Level**: URGENT - Highest VIP tier customer\n**Customer Value**: $500+ monthly, 3+ years VIP\n\nHow would you like to proceed?',
      timestamp: '08:25',
      attachments: []
    },
    {
      id: 2,
      type: 'user',
      content: 'I\'ll handle this through CS System directly. This R15 customer needs immediate attention.',
      timestamp: '08:26',
      attachments: []
    },
    {
      id: 3,
      type: 'ai',
      content: 'Perfect! You\'re taking direct action via CS System. 👍\n\n**Quick Context for your response:**\n• R15 customers expect instant resolution\n• Issue: Server maintenance caused reward skip\n• Recommended action: Manual delivery + compensation\n• Account: Chen Wei (User ID: R15_ChenWei_001)\n\nI\'ll monitor this case. Let me know if you need assistance with the technical escalation.',
      timestamp: '08:27',
      attachments: []
    },
    {
      id: 4,
      type: 'user',
      content: 'Actually, can you help me draft a response? I want to ensure the tone is appropriate for our top-tier VIP.',
      timestamp: '08:28',
      attachments: []
    },
    {
      id: 5,
      type: 'ai',
      content: '**Draft Response for R15 Customer:**\n\n"Dear Chen Wei,\n\nThank you for contacting us regarding your weekly VIP bonus. As one of our most valued R15 customers, your concern has been escalated to highest priority.\n\nWe\'ve identified that recent server maintenance caused a delay in reward distribution for select R15 accounts. I\'ve immediately processed your 5000 gem weekly bonus, plus an additional 2000 gems as our apology for this inconvenience.\n\nYour account has been credited and rewards are now available. We\'ve also implemented additional monitoring to prevent future occurrences.\n\nThank you for your continued loyalty and patience.\n\nBest regards,\nVIP Support Team"\n\n**Would you like me to:**\n1. ✅ Submit this response directly\n2. ✏️ Modify the wording\n3. 📋 Use your own message instead',
      timestamp: '08:29',
      attachments: []
    },
    {
      id: 6,
      type: 'user',
      content: 'Perfect! Please submit that response directly. Also escalate to @rewards-team for system review.',
      timestamp: '08:30',
      attachments: []
    },
    {
      id: 7,
      type: 'ai',
      content: '✅ **Response sent successfully!**\n\n📧 Customer notified via CS System\n💎 Account credited: 7000 gems total (5000 + 2000 bonus)\n🔄 Case status: Resolved\n⚠️ Escalated to @rewards-team for maintenance impact review\n\n**Customer Feedback**: Chen Wei responded positively - "Exceptional service as always! This is why I remain loyal to your platform."\n\n🌟 R15 VIP relationship maintained and strengthened!',
      timestamp: '08:32',
      attachments: []
    }
  ]
};

// 模拟复杂会话数据 - 包括工具调用、图片和动态数据卡片
export const complexChatSessions: ComplexSession[] = [
  {
    id: 'c1',
    title: 'Data Analysis Project',
    messages: [
      {
        id: 1,
        type: 'user',
        content: 'Can you analyze this sales data and create a visualization?',
        timestamp: '10:15 AM',
        attachments: [
          {
            type: 'file',
            url: '#',
            name: 'sales_data_q2.xlsx',
            size: '245 KB'
          }
        ]
      },
      {
        id: 2,
        type: 'ai',
        content: [
          { type: 'text', content: 'I\'ll analyze the sales data for you. Let me use some tools to process this and create a visualization.' },
          { 
            type: 'tool-call', 
            content: 'Calling data_analysis_tool with parameters: { file: "sales_data_q2.xlsx", analysis_type: "trend" }',
            result: 'Analysis complete. Found increasing trend in North region, declining trend in South.'
          },
          { 
            type: 'tool-call',
            content: 'Calling visualization_tool with parameters: { data: "analyzed_sales_data", chart_type: "bar", dimensions: ["region", "revenue"] }',
            result: 'Chart generated successfully.'
          },
          { type: 'image', content: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop' },
          { type: 'text', content: 'Based on the Q2 sales data, I\'ve identified the following insights:' },
          { type: 'text', content: '1. The North region shows a 23% increase in sales compared to Q1' },
          { type: 'text', content: '2. The South region has experienced a 12% decline' },
          { type: 'text', content: '3. Product A continues to be the top performer across all regions' },
          { 
            type: 'dynamic-card',
            title: 'Sales Performance',
            content: {
              type: 'data-table',
              columns: ['Region', 'Q1 Sales', 'Q2 Sales', 'Change'],
              rows: [
                ['North', '$1.2M', '$1.5M', '+23%'],
                ['South', '$980K', '$864K', '-12%'],
                ['East', '$1.1M', '$1.2M', '+9%'],
                ['West', '$1.4M', '$1.5M', '+7%']
              ],
              actions: [
                { label: 'Download Full Report', action: 'download' },
                { label: 'Share Results', action: 'share' }
              ]
            }
          }
        ],
        timestamp: '10:20 AM'
      }
    ]
  },
  {
    id: 'c2',
    title: 'Project Planning',
    messages: [
      {
        id: 1,
        type: 'user',
        content: 'I need to create a timeline for the new product launch',
        timestamp: '2:30 PM',
        attachments: []
      },
      {
        id: 2,
        type: 'ai',
        content: [
          { type: 'text', content: 'I can help you create a timeline for the product launch. Let me use a planning tool to assist with this.' },
          { 
            type: 'tool-call', 
            content: 'Calling project_planning_tool with parameters: { project_type: "product_launch", duration: "3_months" }',
            result: 'Timeline template created with standard product launch phases.'
          },
          { 
            type: 'dynamic-card',
            title: 'Product Launch Timeline',
            content: {
              type: 'timeline',
              phases: [
                { name: 'Planning', duration: '2 weeks', startDate: '2025-06-01', endDate: '2025-06-14', status: 'upcoming' },
                { name: 'Development', duration: '4 weeks', startDate: '2025-06-15', endDate: '2025-07-12', status: 'upcoming' },
                { name: 'Testing', duration: '2 weeks', startDate: '2025-07-13', endDate: '2025-07-26', status: 'upcoming' },
                { name: 'Marketing Prep', duration: '3 weeks', startDate: '2025-07-13', endDate: '2025-08-02', status: 'upcoming' },
                { name: 'Launch', duration: '1 week', startDate: '2025-08-03', endDate: '2025-08-09', status: 'upcoming' }
              ],
              actions: [
                { label: 'Edit Timeline', action: 'edit' },
                { label: 'Add to Calendar', action: 'calendar' },
                { label: 'Share with Team', action: 'share' }
              ]
            }
          },
          { type: 'text', content: 'Here\'s a suggested timeline for your product launch. It spans approximately 3 months with overlapping phases where appropriate. You can edit this timeline or add it directly to your calendar.' }
        ],
        timestamp: '2:35 PM'
      }
    ]
  }
];

// 团队成员权限数据
export const teamMembers: TeamMember[] = [
  {
    id: 'user-current',
    username: 'Alex Chen',
    avatar: '',
    permissions: ['reply', 'evaluate', 'knowledge'],
    isCurrentUser: true,
    capabilities: 'Team lead, full-stack development, system architecture'
  },
  {
    id: 'user-sarah',
    username: 'Sarah Johnson',
    avatar: '',
    permissions: ['reply', 'evaluate', 'knowledge'],
    capabilities: 'Frontend specialist, UI/UX design, React expertise'
  },
  {
    id: 'user-mike',
    username: 'Mike Wang',
    avatar: '',
    permissions: ['reply', 'evaluate'],
    capabilities: 'Backend development, API design, database optimization'
  },
  {
    id: 'user-lisa',
    username: 'Lisa Zhang',
    avatar: '',
    permissions: ['reply'],
    capabilities: 'QA testing, automated testing, bug tracking'
  },
  {
    id: 'user-tom',
    username: 'Tom Wilson',
    avatar: '',
    permissions: ['reply'],
    capabilities: 'DevOps, CI/CD, cloud infrastructure'
  },
  {
    id: 'user-jane',
    username: 'Jane Brown',
    avatar: '',
    permissions: ['reply', 'knowledge'],
    capabilities: 'Technical writing, documentation, knowledge management'
  },
  {
    id: 'user-david',
    username: 'David Lee',
    avatar: '',
    permissions: ['reply'],
    capabilities: 'Mobile development, iOS/Android, cross-platform solutions'
  },
  {
    id: 'user-anna',
    username: 'Anna Smith',
    avatar: '',
    permissions: ['reply'],
    capabilities: 'Data analysis, machine learning, statistical modeling'
  }
];

// Knowledge Tasks Mock Data
export const knowledgeTasks: KnowledgeTask[] = [
  {
    id: '1',
    taskId: '667d4bd6-4...',
    appCode: 'kumo',
    general: false,
    question: '如何在kumo游戏中将装备升级?',
    status: 'Completed',
    confidence: 72,
    assignedTo: 'Jichen Ma',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15'
  },
  {
    id: '2',
    taskId: '887e5cd7-5...',
    appCode: '',
    general: true,
    question: 'How to reset account password?',
    status: 'Reviewing',
    confidence: 85,
    assignedTo: 'Sarah Johnson',
    createdAt: '2024-01-14',
    updatedAt: '2024-01-14'
  },
  {
    id: '3',
    taskId: '998f6de8-6...',
    appCode: 'puzzle',
    general: false,
    question: 'puzzle游戏关卡通关条件说明',
    status: 'Revision',
    confidence: 64,
    assignedTo: 'Mike Wang',
    createdAt: '2024-01-13',
    updatedAt: '2024-01-14'
  },
  {
    id: '4',
    taskId: 'aa9g7ef9-7...',
    appCode: '',
    general: true,
    question: 'VIP membership benefits explanation',
    status: 'Completed',
    confidence: 91,
    assignedTo: 'Lisa Zhang',
    createdAt: '2024-01-12',
    updatedAt: '2024-01-13'
  },
  {
    id: '5',
    taskId: 'bb0h8fg0-8...',
    appCode: 'strategy',
    general: false,
    question: '策略游戏建筑升级优先级建议',
    status: 'Draft',
    confidence: 78,
    assignedTo: 'Tom Wilson',
    createdAt: '2024-01-11',
    updatedAt: '2024-01-11'
  }
];

// i18n Knowledge Tasks Mock Data - 专注于多语言本地化知识库
export const i18nKnowledgeTasks: KnowledgeTask[] = [
  {
    id: '1',
    taskId: 'i18n-001-loc...',
    appCode: 'yugioh',
    general: false,
    question: 'How to handle Japanese text encoding issues in mobile apps?',
    status: 'Completed',
    confidence: 89,
    assignedTo: 'Yuki Tanaka',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-15'
  },
  {
    id: '2',
    taskId: 'i18n-002-gen...',
    appCode: '',
    general: true,
    question: 'Best practices for right-to-left language support',
    status: 'Reviewing',
    confidence: 92,
    assignedTo: 'Ahmed Hassan',
    createdAt: '2024-01-14',
    updatedAt: '2024-01-14'
  },
  {
    id: '3',
    taskId: 'i18n-003-cur...',
    appCode: 'gate',
    general: false,
    question: 'Currency formatting standards for European markets',
    status: 'Revision',
    confidence: 76,
    assignedTo: 'Marie Dubois',
    createdAt: '2024-01-13',
    updatedAt: '2024-01-14'
  },
  {
    id: '4',
    taskId: 'i18n-004-fnt...',
    appCode: '',
    general: true,
    question: 'Font fallback strategies for multilingual UI',
    status: 'Completed',
    confidence: 88,
    assignedTo: 'Li Wei',
    createdAt: '2024-01-12',
    updatedAt: '2024-01-13'
  },
  {
    id: '5',
    taskId: 'i18n-005-aud...',
    appCode: 'doraemon',
    general: false,
    question: 'Audio localization workflow for voice-over content',
    status: 'Draft',
    confidence: 71,
    assignedTo: 'Carlos Rodriguez',
    createdAt: '2024-01-11',
    updatedAt: '2024-01-11'
  }
];

// Knowledge Detail Mock Data
export const knowledgeDetailData: { [taskId: string]: KnowledgeDetailData } = {
  '1': {
    task: knowledgeTasks[0],
    qaPairs: [
      {
        id: 'qa-1-1',
        question: '如何在kumo游戏中将装备升级?',
        answer: '进入游戏后，点击背包图标，选择要升级的装备，点击升级按钮，消耗相应的材料和金币即可完成升级。'
      }
    ],
    suggestions: [
      // 重复知识建议 - 针对同一个新问答对的多个重复项
      {
        id: 'sug-1-1',
        type: 'duplicate',
        existingQA: {
          id: 'existing-1-1',
          question: '怎么升级装备？',
          answer: '打开背包，选择装备，点击升级，需要材料和金币。'
        },
        newQA: {
          id: 'qa-1-1',
          question: '如何在kumo游戏中将装备升级?',
          answer: '进入游戏后，点击背包图标，选择要升级的装备，点击升级按钮，消耗相应的材料和金币即可完成升级。'
        },
        isSelected: true
      },
      {
        id: 'sug-1-2',
        type: 'duplicate',
        existingQA: {
          id: 'existing-1-2',
          question: 'kumo装备如何提升等级？',
          answer: '在装备界面选择装备，使用升级石和金币进行升级。'
        },
        newQA: {
          id: 'qa-1-1',
          question: '如何在kumo游戏中将装备升级?',
          answer: '进入游戏后，点击背包图标，选择要升级的装备，点击升级按钮，消耗相应的材料和金币即可完成升级。'
        },
        isSelected: false
      },
      {
        id: 'sug-1-3',
        type: 'duplicate',
        existingQA: {
          id: 'existing-1-3',
          question: '装备升级步骤是什么？',
          answer: '装备升级：背包→选择装备→升级→确认消耗材料。'
        },
        newQA: {
          id: 'qa-1-1',
          question: '如何在kumo游戏中将装备升级?',
          answer: '进入游戏后，点击背包图标，选择要升级的装备，点击升级按钮，消耗相应的材料和金币即可完成升级。'
        },
        isSelected: true
      }
    ]
  },
  '2': {
    task: knowledgeTasks[1],
    qaPairs: [
      {
        id: 'qa-2-1',
        question: 'How to reset account password?',
        answer: 'Go to login page, click "Forgot Password", enter your email address, and follow the instructions in the email sent to you.'
      }
    ],
    suggestions: [
      // 矛盾知识建议 - 针对同一个新问答对的多个矛盾项
      {
        id: 'sug-2-1',
        type: 'conflict',
        existingQA: {
          id: 'existing-2-1',
          question: 'How to reset password?',
          answer: 'Contact customer service to reset your password.'
        },
        newQA: {
          id: 'qa-2-1',
          question: 'How to reset account password?',
          answer: 'Go to login page, click "Forgot Password", enter your email address, and follow the instructions in the email sent to you.'
        },
        isSelected: true
      },
      {
        id: 'sug-2-2',
        type: 'conflict',
        existingQA: {
          id: 'existing-2-2',
          question: 'What to do if forgot password?',
          answer: 'Call our hotline 400-123-4567 for manual password reset.'
        },
        newQA: {
          id: 'qa-2-1',
          question: 'How to reset account password?',
          answer: 'Go to login page, click "Forgot Password", enter your email address, and follow the instructions in the email sent to you.'
        },
        isSelected: false
      },
      {
        id: 'sug-2-3',
        type: 'conflict',
        existingQA: {
          id: 'existing-2-3',
          question: 'Password recovery method?',
          answer: 'Password can only be reset by providing ID verification documents to support team.'
        },
        newQA: {
          id: 'qa-2-1',
          question: 'How to reset account password?',
          answer: 'Go to login page, click "Forgot Password", enter your email address, and follow the instructions in the email sent to you.'
        },
        isSelected: true
      },
      // 重复知识建议
      {
        id: 'sug-2-4',
        type: 'duplicate',
        existingQA: {
          id: 'existing-2-4',
          question: 'Password reset steps?',
          answer: 'Use forgot password feature on login page.'
        },
        newQA: {
          id: 'qa-2-1',
          question: 'How to reset account password?',
          answer: 'Go to login page, click "Forgot Password", enter your email address, and follow the instructions in the email sent to you.'
        },
        isSelected: false
      },
      {
        id: 'sug-2-5',
        type: 'duplicate',
        existingQA: {
          id: 'existing-2-5',
          question: 'How do I change my forgotten password?',
          answer: 'Click forgot password link, enter email, check inbox for reset instructions.'
        },
        newQA: {
          id: 'qa-2-1',
          question: 'How to reset account password?',
          answer: 'Go to login page, click "Forgot Password", enter your email address, and follow the instructions in the email sent to you.'
        },
        isSelected: true
      }
    ]
  },
  '3': {
    task: knowledgeTasks[2],
    rejectionReason: '问答内容需要更加详细和准确。建议：1. 通关条件说明需要包含具体的分数要求；2. 高分技巧需要提供更多实用的操作建议；3. 建议添加常见问题的解决方案。',
    qaPairs: [
      {
        id: 'qa-3-1',
        question: 'puzzle游戏关卡通关条件说明',
        answer: '每个关卡都有特定的目标，如收集指定数量的星星、在限定步数内完成、达到目标分数等。查看关卡开始前的提示了解具体要求。'
      },
      {
        id: 'qa-3-2',
        question: 'puzzle游戏中如何获得更高分数？',
        answer: '通过连续消除、使用特殊道具、完成额外挑战可以获得更高分数。建议先观察棋盘布局，制定最佳消除策略。'
      }
    ],
    suggestions: [
      // 第一个问答对的重复建议
      {
        id: 'sug-3-1',
        type: 'duplicate',
        existingQA: {
          id: 'existing-3-1',
          question: 'puzzle关卡怎么过？',
          answer: '完成关卡目标即可通关，比如收集星星、达到分数等。'
        },
        newQA: {
          id: 'qa-3-1',
          question: 'puzzle游戏关卡通关条件说明',
          answer: '每个关卡都有特定的目标，如收集指定数量的星星、在限定步数内完成、达到目标分数等。查看关卡开始前的提示了解具体要求。'
        },
        isSelected: true
      },
      {
        id: 'sug-3-2',
        type: 'duplicate',
        existingQA: {
          id: 'existing-3-2',
          question: '通关条件是什么？',
          answer: '根据关卡要求完成任务，如收集道具、达到分数线等。'
        },
        newQA: {
          id: 'qa-3-1',
          question: 'puzzle游戏关卡通关条件说明',
          answer: '每个关卡都有特定的目标，如收集指定数量的星星、在限定步数内完成、达到目标分数等。查看关卡开始前的提示了解具体要求。'
        },
        isSelected: false
      },
      // 第二个问答对的重复建议
      {
        id: 'sug-3-3',
        type: 'duplicate',
        existingQA: {
          id: 'existing-3-3',
          question: '如何在puzzle中得高分？',
          answer: '多连击、用道具可以获得高分。'
        },
        newQA: {
          id: 'qa-3-2',
          question: 'puzzle游戏中如何获得更高分数？',
          answer: '通过连续消除、使用特殊道具、完成额外挑战可以获得更高分数。建议先观察棋盘布局，制定最佳消除策略。'
        },
        isSelected: true
      },
      // 第二个问答对的矛盾建议
      {
        id: 'sug-3-4',
        type: 'conflict',
        existingQA: {
          id: 'existing-3-4',
          question: 'puzzle游戏高分技巧？',
          answer: '快速消除是关键，不需要考虑策略，速度越快分数越高。'
        },
        newQA: {
          id: 'qa-3-2',
          question: 'puzzle游戏中如何获得更高分数？',
          answer: '通过连续消除、使用特殊道具、完成额外挑战可以获得更高分数。建议先观察棋盘布局，制定最佳消除策略。'
        },
        isSelected: true
      }
    ]
  },
  '4': {
    task: knowledgeTasks[3],
    qaPairs: [
      {
        id: 'qa-4-1',
        question: 'VIP membership benefits explanation',
        answer: 'VIP members enjoy exclusive privileges including: priority customer support, special discounts, early access to new features, and bonus rewards.'
      }
    ],
    suggestions: [
      // 重复知识建议
      {
        id: 'sug-4-1',
        type: 'duplicate',
        existingQA: {
          id: 'existing-4-1',
          question: 'What are VIP benefits?',
          answer: 'VIP users get priority support and special offers.'
        },
        newQA: {
          id: 'qa-4-1',
          question: 'VIP membership benefits explanation',
          answer: 'VIP members enjoy exclusive privileges including: priority customer support, special discounts, early access to new features, and bonus rewards.'
        },
        isSelected: true
      },
      {
        id: 'sug-4-2',
        type: 'duplicate',
        existingQA: {
          id: 'existing-4-2',
          question: 'VIP member privileges?',
          answer: 'Priority service, exclusive discounts, early feature access.'
        },
        newQA: {
          id: 'qa-4-1',
          question: 'VIP membership benefits explanation',
          answer: 'VIP members enjoy exclusive privileges including: priority customer support, special discounts, early access to new features, and bonus rewards.'
        },
        isSelected: false
      },
      {
        id: 'sug-4-3',
        type: 'duplicate',
        existingQA: {
          id: 'existing-4-3',
          question: 'What do VIP members get?',
          answer: 'Special customer service, discounts, beta features, extra rewards.'
        },
        newQA: {
          id: 'qa-4-1',
          question: 'VIP membership benefits explanation',
          answer: 'VIP members enjoy exclusive privileges including: priority customer support, special discounts, early access to new features, and bonus rewards.'
        },
        isSelected: true
      },
      // 矛盾知识建议
      {
        id: 'sug-4-4',
        type: 'conflict',
        existingQA: {
          id: 'existing-4-4',
          question: 'VIP membership benefits?',
          answer: 'VIP membership only provides cosmetic upgrades, no functional benefits.'
        },
        newQA: {
          id: 'qa-4-1',
          question: 'VIP membership benefits explanation',
          answer: 'VIP members enjoy exclusive privileges including: priority customer support, special discounts, early access to new features, and bonus rewards.'
        },
        isSelected: true
      },
      {
        id: 'sug-4-5',
        type: 'conflict',
        existingQA: {
          id: 'existing-4-5',
          question: 'What does VIP include?',
          answer: 'VIP status is purely symbolic and provides no actual benefits to users.'
        },
        newQA: {
          id: 'qa-4-1',
          question: 'VIP membership benefits explanation',
          answer: 'VIP members enjoy exclusive privileges including: priority customer support, special discounts, early access to new features, and bonus rewards.'
        },
        isSelected: false
      }
    ]
  },
  '5': {
    task: knowledgeTasks[4],
    qaPairs: [
      {
        id: 'qa-5-1',
        question: '策略游戏建筑升级优先级建议',
        answer: '建议优先升级资源建筑（如农场、矿场），然后是防御建筑，最后是装饰建筑。根据当前游戏阶段和资源情况调整顺序。'
      },
      {
        id: 'qa-5-2',
        question: '策略游戏新手入门建议',
        answer: '新手建议先熟悉游戏界面，完成新手教程，重点发展经济建筑，避免过早参与战斗。建议加入新手联盟获得保护和指导。'
      }
    ],
    suggestions: [
      // 第一个问答对的重复建议
      {
        id: 'sug-5-1',
        type: 'duplicate',
        existingQA: {
          id: 'existing-5-1',
          question: '建筑升级顺序？',
          answer: '先升资源建筑，再升防御建筑。'
        },
        newQA: {
          id: 'qa-5-1',
          question: '策略游戏建筑升级优先级建议',
          answer: '建议优先升级资源建筑（如农场、矿场），然后是防御建筑，最后是装饰建筑。根据当前游戏阶段和资源情况调整顺序。'
        },
        isSelected: true
      },
      {
        id: 'sug-5-2',
        type: 'duplicate',
        existingQA: {
          id: 'existing-5-2',
          question: '哪些建筑优先升级？',
          answer: '优先级：资源建筑>防御建筑>其他建筑。'
        },
        newQA: {
          id: 'qa-5-1',
          question: '策略游戏建筑升级优先级建议',
          answer: '建议优先升级资源建筑（如农场、矿场），然后是防御建筑，最后是装饰建筑。根据当前游戏阶段和资源情况调整顺序。'
        },
        isSelected: false
      },
      // 第一个问答对的矛盾建议
      {
        id: 'sug-5-3',
        type: 'conflict',
        existingQA: {
          id: 'existing-5-3',
          question: '建筑升级策略？',
          answer: '应该优先升级防御建筑保护城市，资源建筑不重要。'
        },
        newQA: {
          id: 'qa-5-1',
          question: '策略游戏建筑升级优先级建议',
          answer: '建议优先升级资源建筑（如农场、矿场），然后是防御建筑，最后是装饰建筑。根据当前游戏阶段和资源情况调整顺序。'
        },
        isSelected: true
      },
      // 第二个问答对的重复建议
      {
        id: 'sug-5-4',
        type: 'duplicate',
        existingQA: {
          id: 'existing-5-4',
          question: '新手怎么玩策略游戏？',
          answer: '先做教程，发展经济，避免战斗。'
        },
        newQA: {
          id: 'qa-5-2',
          question: '策略游戏新手入门建议',
          answer: '新手建议先熟悉游戏界面，完成新手教程，重点发展经济建筑，避免过早参与战斗。建议加入新手联盟获得保护和指导。'
        },
        isSelected: true
      },
      {
        id: 'sug-5-5',
        type: 'duplicate',
        existingQA: {
          id: 'existing-5-5',
          question: '策略游戏初学者指南？',
          answer: '完成教程，专注经济发展，找联盟加入。'
        },
        newQA: {
          id: 'qa-5-2',
          question: '策略游戏新手入门建议',
          answer: '新手建议先熟悉游戏界面，完成新手教程，重点发展经济建筑，避免过早参与战斗。建议加入新手联盟获得保护和指导。'
        },
        isSelected: false
      },
      // 第二个问答对的矛盾建议
      {
        id: 'sug-5-6',
        type: 'conflict',
        existingQA: {
          id: 'existing-5-6',
          question: '新手策略游戏攻略？',
          answer: '新手应该立即开始攻击其他玩家来快速获得资源和经验。'
        },
        newQA: {
          id: 'qa-5-2',
          question: '策略游戏新手入门建议',
          answer: '新手建议先熟悉游戏界面，完成新手教程，重点发展经济建筑，避免过早参与战斗。建议加入新手联盟获得保护和指导。'
        },
        isSelected: true
      }
    ]
  }
};

// Knowledge Base mock data
// Evaluation System mock data
export const evaluationTasks: EvaluationTask[] = [
  {
    id: 'eval_1',
    taskId: '7f5f73d4-7967-401...',
    appCode: 'vividarmy',
    general: false,
    type: 'Response Quality',
    subject: 'VividArmy server issue evaluation',
    status: 'Pending',
    assignedTo: 'Chen Wei',
    createdAt: '2025-06-26 14:51:37',
    vip: true,
    query: 'しばらく離れていたサーバーだと戦力差で大変ということですが...',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:51:37',
    conversationData: [
      { type: 'user', content: '7字以上対応していただきありがとうございます！', timestamp: '2025-06-26 14:49:35', language: 'JA', category: 'appreciation' },
      { type: 'user', content: 'ちなみに新しいサーバーで始めるとかはあるのでしょうか？', timestamp: '2025-06-26 14:49:35', language: 'JA', category: 'inquiry' },
      { type: 'ai', content: '新しいサーバーで始めることは、他のプレイヤーとの関係やスター達できるでしょう', timestamp: '2025-06-26 14:50:20', language: 'JA', category: 'response' }
    ]
  },
  {
    id: 'eval_2',
    taskId: '8a6b84e5-8078-512...',
    appCode: '',
    general: true,
    type: 'User Satisfaction',
    subject: 'General weekly reward setting',
    status: 'Pending',
    assignedTo: 'Li Ming',
    createdAt: '2025-06-26 14:49:40',
    vip: false,
    query: '週間の設定方法',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:49:40',
    conversationData: [
      { type: 'user', content: '週間の設定方法を教えてください', timestamp: '2025-06-26 14:49:40', language: 'JA', category: 'question' },
      { type: 'ai', content: '週間設定については、メニューから設定画面にアクセスしてください', timestamp: '2025-06-26 14:50:15', language: 'JA', category: 'instruction' }
    ]
  },
  {
    id: 'eval_3',
    taskId: '9c7d95f6-9189-623...',
    appCode: '',
    general: true,
    type: 'Knowledge Accuracy',
    subject: 'Thank you message response',
    status: 'Pending',
    assignedTo: 'Zhang San',
    createdAt: '2025-06-26 14:41:21',
    vip: true,
    query: 'ありがとうございます',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:41:21',
    conversationData: [
      { type: 'user', content: 'ありがとうございます', timestamp: '2025-06-26 14:41:21', language: 'JA', category: 'gratitude' },
      { type: 'ai', content: 'どういたしまして！他にご質問がございましたらお気軽にお声かけください', timestamp: '2025-06-26 14:41:35', language: 'JA', category: 'courtesy' }
    ]
  },
  {
    id: 'eval_4',
    taskId: 'ad8e06g7-a29a-734...',
    appCode: '',
    general: true,
    type: 'Response Quality',
    subject: 'Korean game question evaluation',
    status: 'Pending',
    assignedTo: 'Wang Lei',
    createdAt: '2025-06-26 14:32:52',
    vip: true,
    query: '끝러의 꿈던세나 찬진청 젓어는넝자 지쟈기 몰재조.',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:32:52',
    conversationData: [
      { type: 'user', content: '끝러의 꿈던세나 찬진청 젓어는넝자 지쟈기 몰재조.', timestamp: '2025-06-26 14:32:52', language: 'KO', category: 'inquiry' },
      { type: 'ai', content: '죄송합니다. 한국어 지원에 대해서는 현재 확인 중입니다', timestamp: '2025-06-26 14:33:15', language: 'KO', category: 'apology' }
    ]
  },
  {
    id: 'eval_5',
    taskId: 'be9f17h8-b3ab-845...',
    appCode: '',
    general: true,
    type: 'User Satisfaction',
    subject: 'English apology message evaluation',
    status: 'Pending',
    assignedTo: 'Liu Yu',
    createdAt: '2025-06-26 14:28:24',
    vip: false,
    query: 'You have already mentioned "we apologize of the text being ...',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:28:24',
    conversationData: [
      { type: 'user', content: 'You have already mentioned "we apologize of the text being in English"', timestamp: '2025-06-26 14:28:24', language: 'EN', category: 'feedback' },
      { type: 'ai', content: 'I understand your concern. We are working to improve our multilingual support', timestamp: '2025-06-26 14:28:45', language: 'EN', category: 'acknowledgment' }
    ]
  },
  {
    id: 'eval_6',
    taskId: 'cf0g28i9-c4bc-956...',
    appCode: '',
    general: true,
    type: 'Response Quality',
    subject: 'Japanese premium feature inquiry',
    status: 'Pending',
    assignedTo: 'Zhao Liu',
    createdAt: '2025-06-26 14:22:45',
    vip: true,
    query: '課金だちずて課金を与える文章を送ってしまい申し訳ありませ...',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:22:45',
    conversationData: [
      { type: 'user', content: '課金だちずて課金を与える文章を送ってしまい申し訳ありません', timestamp: '2025-06-26 14:22:45', language: 'JA', category: 'apology' },
      { type: 'ai', content: 'ご心配いただきありがとうございます。課金に関するご質問はお気軽にどうぞ', timestamp: '2025-06-26 14:23:10', language: 'JA', category: 'reassurance' }
    ]
  }
];

// i18n Evaluation Tasks Mock Data - 专注于多语言本地化评估
export const i18nEvaluationTasks: EvaluationTask[] = [
  {
    id: 'i18n_eval_1',
    taskId: 'i18n-7f5f73d4-loc...',
    appCode: 'yugioh',
    general: false,
    type: 'Translation Quality',
    subject: 'Japanese card description localization',
    status: 'Pending',
    assignedTo: 'Yuki Tanaka',
    createdAt: '2025-06-26 15:20:12',
    vip: true,
    query: 'このカードの効果テキストが正しく翻訳されていません',
    rating: 'Pending',
    sessionTime: '2025-06-26 15:20:12',
    conversationData: [
      { type: 'user', content: 'このカードの効果テキストが正しく翻訳されていません', timestamp: '2025-06-26 15:20:12', language: 'JA', category: 'translation_issue' },
      { type: 'ai', content: 'カード効果の翻訳についてご報告いただき、ありがとうございます。翻訳チームに確認いたします', timestamp: '2025-06-26 15:20:35', language: 'JA', category: 'acknowledgment' }
    ]
  },
  {
    id: 'i18n_eval_2',
    taskId: 'i18n-8a6b84e5-rtl...',
    appCode: '',
    general: true,
    type: 'UI Layout Review',
    subject: 'Arabic text alignment issues',
    status: 'Pending',
    assignedTo: 'Ahmed Hassan',
    createdAt: '2025-06-26 15:15:30',
    vip: true,
    query: 'النص العربي لا يظهر بشكل صحيح في واجهة المستخدم',
    rating: 'Pending',
    sessionTime: '2025-06-26 15:15:30',
    conversationData: [
      { type: 'user', content: 'النص العربي لا يظهر بشكل صحيح في واجهة المستخدم', timestamp: '2025-06-26 15:15:30', language: 'AR', category: 'ui_issue' },
      { type: 'ai', content: 'نشكرك على تقريرك. سيقوم فريق التطوير بمراجعة مشكلة محاذاة النص العربي', timestamp: '2025-06-26 15:16:05', language: 'AR', category: 'response' }
    ]
  },
  {
    id: 'i18n_eval_3',
    taskId: 'i18n-9c7d95f6-cur...',
    appCode: 'gate',
    general: false,
    type: 'Currency Format Review',
    subject: 'European currency display error',
    status: 'Pending',
    assignedTo: 'Marie Dubois',
    createdAt: '2025-06-26 15:10:45',
    vip: false,
    query: 'Les prix ne s\'affichent pas correctement en euros',
    rating: 'Pending',
    sessionTime: '2025-06-26 15:10:45',
    conversationData: [
      { type: 'user', content: 'Les prix ne s\'affichent pas correctement en euros', timestamp: '2025-06-26 15:10:45', language: 'FR', category: 'currency_issue' },
      { type: 'ai', content: 'Merci de nous avoir signalé ce problème. Nous allons vérifier l\'affichage des prix en euros', timestamp: '2025-06-26 15:11:20', language: 'FR', category: 'confirmation' }
    ]
  },
  {
    id: 'i18n_eval_4',
    taskId: 'i18n-ad8e06g7-fnt...',
    appCode: '',
    general: true,
    type: 'Font Rendering Review',
    subject: 'Chinese character display issues',
    status: 'Pending',
    assignedTo: 'Li Wei',
    createdAt: '2025-06-26 15:05:18',
    vip: true,
    query: '繁體中文字符顯示為方塊',
    rating: 'Pending',
    sessionTime: '2025-06-26 15:05:18',
    conversationData: [
      { type: 'user', content: '繁體中文字符顯示為方塊', timestamp: '2025-06-26 15:05:18', language: 'ZH-TW', category: 'font_issue' },
      { type: 'ai', content: '感謝您的回報，我們會立即檢查繁體中文字型的顯示問題', timestamp: '2025-06-26 15:05:45', language: 'ZH-TW', category: 'acknowledgment' }
    ]
  },
  {
    id: 'i18n_eval_5',
    taskId: 'i18n-be9f17h8-aud...',
    appCode: 'doraemon',
    general: false,
    type: 'Audio Localization Review',
    subject: 'Spanish voice-over missing',
    status: 'Pending',
    assignedTo: 'Carlos Rodriguez',
    createdAt: '2025-06-26 14:58:32',
    vip: true,
    query: 'No hay audio en español en las cinemáticas',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:58:32',
    conversationData: [
      { type: 'user', content: 'No hay audio en español en las cinemáticas', timestamp: '2025-06-26 14:58:32', language: 'ES', category: 'audio_issue' },
      { type: 'ai', content: 'Lamentamos este inconveniente. Verificaremos el estado de la localización de audio en español', timestamp: '2025-06-26 14:59:10', language: 'ES', category: 'apology' }
    ]
  },
  {
    id: 'i18n_eval_6',
    taskId: 'i18n-cf0g28i9-inp...',
    appCode: '',
    general: true,
    type: 'Input Method Review',
    subject: 'Korean keyboard input problems',
    status: 'Pending',
    assignedTo: 'Kim Min-jun',
    createdAt: '2025-06-26 14:52:28',
    vip: false,
    query: '한글 입력이 제대로 작동하지 않습니다',
    rating: 'Pending',
    sessionTime: '2025-06-26 14:52:28',
    conversationData: [
      { type: 'user', content: '한글 입력이 제대로 작동하지 않습니다', timestamp: '2025-06-26 14:52:28', language: 'KO', category: 'input_issue' },
      { type: 'ai', content: '한글 입력 문제를 신고해 주셔서 감사합니다. 기술팀에서 확인하겠습니다', timestamp: '2025-06-26 14:53:05', language: 'KO', category: 'response' }
    ]
  }
];

export const evaluationDetailData: { [key: string]: EvaluationDetailData } = {
  'eval_1': {
    task: evaluationTasks[0],
    detail: {
      id: 'detail_1',
      criteria: [
        { id: 'c1', name: 'Clarity', weight: 30, maxScore: 10, description: 'Response clarity and understandability' },
        { id: 'c2', name: 'Accuracy', weight: 40, maxScore: 10, description: 'Technical accuracy of information' },
        { id: 'c3', name: 'Helpfulness', weight: 30, maxScore: 10, description: 'How helpful the response is' }
      ],
      scores: { c1: 8, c2: 7, c3: 9 },
      comments: 'Good response but could improve technical accuracy',
      overallScore: 80,
      recommendation: 'Pass'
    },
    conversationId: 'EC-2023-001',
    originalContent: 'User asked about equipment upgrade mechanics...'
  },
  'eval_2': {
    task: evaluationTasks[1],
    detail: {
      id: 'detail_2',
      criteria: [
        { id: 'c1', name: 'Responsiveness', weight: 25, maxScore: 10, description: 'Speed of response' },
        { id: 'c2', name: 'Politeness', weight: 25, maxScore: 10, description: 'Professional and polite communication' },
        { id: 'c3', name: 'Problem Resolution', weight: 50, maxScore: 10, description: 'Effectiveness in solving user issues' }
      ],
      scores: { c1: 9, c2: 8, c3: 8 },
      comments: 'Excellent customer service, user was very satisfied',
      overallScore: 85,
      recommendation: 'Pass'
    },
    conversationId: 'EC-2023-002'
  }
};

export const knowledgeBaseItems: KnowledgeBaseItem[] = [
  {
    id: 'kb_1',
    appCode: 'goblinslayer',
    general: false,
    question: 'What happens to the valuable return spin in Goblin Slayer if I don\'t have enough gems to use it? Does it expire or disappear?',
    answer: 'We are currently investigating the detail...',
    name: '木田賢, 佐藤健',
    version: 'v1.2',
    operation: 'edit'
  },
  {
    id: 'kb_2', 
    appCode: 'tsukimichi',
    general: false,
    question: 'ゲーム内で補填アイテムはどこで受け取れますか？',
    answer: '本ゲームにおいて、ログアウト中の補填...',
    name: '田中花子',
    version: 'v2.0',
    operation: 'delete'
  },
  {
    id: 'kb_3',
    appCode: 'vividarmy',
    general: false,
    question: 'ラッキーダイヤイベントで適用降雨により「ライン増加+1（30日）」を交換できなかった場合、我物や補填は可能ですか？',
    answer: 'ご指摘いただいた件につきましては、影...',
    name: '中野良太, 山田太郎, 鈴木一郎',
    version: 'v1.5',
    operation: 'edit'
  },
  {
    id: 'kb_4',
    appCode: '',
    general: true,
    question: 'How can I contact customer support for urgent issues?',
    answer: 'You can contact our 24/7 customer support through the in-game help center, email <EMAIL>, or use the live chat feature in the app.',
    name: 'Chen Wei, Li Ming',
    version: 'v3.1',
    operation: 'edit'
  },
  {
    id: 'kb_5',
    appCode: 'kumo',
    general: false,
    question: 'Spider game equipment upgrade failure compensation policy?',
    answer: 'If equipment upgrade fails due to system error, we will restore the consumed materials and provide additional compensation gems.',
    name: 'Wang Lei, Zhang San, Liu Yu, Zhao Liu',
    version: 'v1.0',
    operation: 'delete'
  },
  {
    id: 'kb_6',
    appCode: '',
    general: true,
    question: 'Account security best practices for all games',
    answer: 'Always enable two-factor authentication, use strong passwords, never share account details, and regularly check login history.',
    name: 'Security Team, Kim Min-jun',
    version: 'v2.3',
    operation: 'edit'
  }
]; 