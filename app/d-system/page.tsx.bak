'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import Image from 'next/image';
import { useTheme } from './theme-context';
import AIInputBox from '../components/AIInputBox';
import AIMessage from '../components/AIMessage';
import UserMessage from '../components/UserMessage';
import MyDPalPage from './MyDPalPage';
import NotificationCard from '../components/NotificationCard';
import MockSessionChat from '../components/MockSessionChat';

// 添加内联样式使页面可见
const styles = {
  container: {
    display: 'flex',
    minHeight: '100vh',
    backgroundColor: '#181c20',
    color: '#ffffff',
  } as React.CSSProperties,
  sidebar: {
    width: '16rem',
    backgroundColor: '#181c20',
    borderRight: '1px solid #23272e',
    display: 'flex',
    flexDirection: 'column' as 'column',
  } as React.CSSProperties,
  content: {
    flex: 1,
  } as React.CSSProperties,
};

// Mock data
// 示例聊天历史
const sampleChatHistory = [
  {
    id: 1,
    type: 'ai',
    content: [{ type: 'text', content: 'Hello! I\'m your AI assistant. How can I help you today?' }],
    timestamp: '09:30 AM'
  },
  {
    id: 2,
    type: 'user',
    content: 'I need to create a new project plan for the Q3 marketing campaign.',
    timestamp: '09:31 AM',
    attachments: []
  },
  {
    id: 3,
    type: 'ai',
    content: [{ type: 'text', content: 'I\'d be happy to help you create a project plan for the Q3 marketing campaign. Let\'s start with defining the key objectives, timeline, and resources needed. What are the main goals for this campaign?' }],
    timestamp: '09:31 AM'
  }
];

// 工作流模板
const workflowTemplates = [
  {
    id: 1,
    title: 'New Document Request',
    icon: 'document',
    category: 'Document',
    description: 'Create and collaborate on new documents with team members'
  },
  {
    id: 2,
    title: 'Leave Application',
    icon: 'calendar',
    category: 'HR',
    description: 'Apply for vacation, sick leave or other time off'
  },
  {
    id: 3,
    title: 'Expense Reimbursement',
    icon: 'dollar',
    category: 'Finance',
    description: 'Submit expenses for reimbursement approval'
  },
  {
    id: 4,
    title: 'Project Planning',
    icon: 'chart',
    category: 'Work',
    description: 'Create and manage project plans and timelines'
  },
  {
    id: 5,
    title: 'Task Assignment',
    icon: 'list',
    category: 'Work',
    description: 'Create and assign tasks to team members'
  },
  {
    id: 6,
    title: 'Performance Review',
    icon: 'check',
    category: 'HR',
    description: 'Conduct or request performance evaluations'
  }
];

// Mock project & session data
const mockProjects = [
  {
    id: 'p1',
    name: 'AI Research',
    sessions: [
      { id: 's1', name: 'Prompt Engineering' },
      { id: 's2', name: 'Model Evaluation' },
    ],
  },
  {
    id: 'p2',
    name: 'Infra Upgrade',
    sessions: [
      { id: 's3', name: 'Server Migration' },
      { id: 's4', name: 'CI/CD Pipeline' },
    ],
  },
  {
    id: 'p3',
    name: 'Product Launch',
    sessions: [
      { id: 's5', name: 'Go-to-Market' },
      { id: 's6', name: 'Feedback Loop' },
    ],
  },
];

const NAV_GROUPS = [
  { key: 'home', label: 'Home', icon: 'home' },
  { key: 'projects', label: 'Projects', icon: 'folder' },
  { key: 'knowledge', label: 'Knowledge', icon: 'book' },
];

// Mock tasks
const today = new Date().toISOString().slice(0, 10);
const mockTasks = [
  { id: 1, title: 'Submit leave application', due: today, ddl: '15:00', status: 'pending' },
  { id: 2, title: 'Book meeting room for 2pm', due: today, ddl: '14:00', status: 'pending' },
  { id: 3, title: 'Request Figma access', due: today, ddl: '17:30', status: 'pending' },
  { id: 4, title: 'Check project progress', due: '2024-06-01', ddl: '18:00', status: 'pending' },
  { id: 5, title: 'Plan marketing campaign', due: '2024-06-02', ddl: '10:00', status: 'pending' },
  { id: 6, title: 'Onboarding new member', due: '2024-06-03', ddl: '09:30', status: 'pending' },
];

// 模拟早会日报数据
const dailyReportData = {
  date: '2025-05-19',
  team: 'AI 助手团队',
  participants: ['张三', '李四', '王五', '赵六'],
  agenda: [
    {
      id: '1',
      type: '进展汇报',
      content: '完成了用户反馈分析系统的主体功能开发',
      owner: '张三',
      status: 'completed'
    },
    {
      id: '2',
      type: '问题反馈',
      content: '模型响应时间偶尔超过预期，需要优化',
      owner: '李四',
      status: 'pending'
    },
    {
      id: '3',
      type: '计划安排',
      content: '本周将开始新的知识库优化项目',
      owner: '王五',
      status: 'upcoming'
    }
  ],
  nextSteps: [
    '优化模型响应时间',
    '扩展知识库覆盖范围',
    '提升用户体验满意度'
  ]
};

// 模拟订阅任务数据
const subscriptionTasks = [
  {
    id: 'TASK-001',
    title: '早会日报',
    type: 'daily-report',
    time: '2025-05-19 09:30',
    status: 'new',
    priority: 'high',
    content: dailyReportData,
  },
  {
    id: 'TASK-002',
    title: '周报提醒',
    type: 'weekly-report',
    time: '2025-05-19 18:00',
    status: 'upcoming',
    priority: 'medium',
    content: {
      deadline: '2025-05-19 20:00',
      template: '标准周报模板'
    }
  },
  {
    id: 'TASK-003',
    title: '项目里程碑',
    type: 'milestone',
    time: '2025-05-20 10:00',
    status: 'upcoming',
    priority: 'high',
    content: {
      project: 'AI 助手优化',
      milestone: 'V2.0 发布'
    }
  }
];

// Icon component to replace Ant Design icons
function Icon({ name, className = "" }: { name: string, className?: string }) {
  switch (name) {
    case 'home':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path></svg>;
    case 'folder':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>;
    case 'book':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path></svg>;
    case 'search':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"></path></svg>;
    case 'calendar':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path></svg>;
    case 'team':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg>;
    case 'file':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd"></path></svg>;
    case 'send':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path></svg>;
    case 'robot':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd"></path></svg>;
    case 'user':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path></svg>;
    case 'attachment':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clipRule="evenodd"></path></svg>;
    case 'picture':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path></svg>;
    case 'close':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>;
    default:
      return null;
  }
}

// 图标组件
function SunIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
    </svg>
  );
}

function MoonIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
    </svg>
  );
}

function HomeIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
    </svg>
  );
}

function FolderIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
    </svg>
  );
}

function ChartIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
    </svg>
  );
}

function SendIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
    </svg>
  );
}

// 添加mock时间分组的聊天数据
const chatHistoryByDate = [
  {
    label: 'Today',
    chats: [
      { id: 'c1', title: 'Marketing Plan Q3', hasNewMessage: true },
      { id: 'c2', title: 'Product Roadmap Discussion', hasNewMessage: true },
      { id: 'c3', title: 'User Research Findings', hasNewMessage: false },
      { id: 'c4', title: 'Weekly Team Sync', hasNewMessage: true }
    ]
  },
  {
    label: 'Yesterday',
    chats: [
      { id: 'c5', title: 'Team Performance Review', hasNewMessage: false },
      { id: 'c6', title: 'Design System Updates', hasNewMessage: false }
    ]
  },
  {
    label: '3 days ago',
    chats: [
      { id: 'c7', title: 'Customer Feedback Analysis', hasNewMessage: false },
      { id: 'c8', title: 'Content Strategy Meeting', hasNewMessage: false }
    ]
  },
  {
    label: '1 week ago',
    chats: [
      { id: 'c9', title: 'Project Timeline Review', hasNewMessage: false },
      { id: 'c10', title: 'Budget Planning 2024', hasNewMessage: false },
      { id: 'c11', title: 'Sales Team Coordination', hasNewMessage: false }
    ]
  },
  {
    label: '2 weeks ago',
    chats: [
      { id: 'c12', title: 'UI/UX Improvements', hasNewMessage: false },
      { id: 'c13', title: 'Data Analytics Workshop', hasNewMessage: false }
    ]
  },
  {
    label: '1 month ago',
    chats: [
      { id: 'c14', title: 'Quarterly Strategy Planning', hasNewMessage: false },
      { id: 'c15', title: 'New Feature Brainstorming', hasNewMessage: false },
      { id: 'c16', title: 'Customer Success Stories', hasNewMessage: false }
    ]
  }
];

// Mock notifications data
const mockNotifications = [
  {
    id: 'n1',
    title: 'Weekly Progress Report',
    description: 'Your team has completed 85% of the planned tasks for this week, which is 10% higher than last week. Great work!',
    timestamp: 'Today, 09:15 AM',
    type: 'summary' as 'summary',
    status: 'new' as 'new',
    priority: 'medium' as 'medium',
    actionLabel: 'View Details'
  },
  {
    id: 'n2',
    title: 'Project Deadline Approaching',
    description: 'The "AI Integration" project deadline is in 3 days. Current completion status is at 78%.',
    timestamp: 'Today, 08:30 AM',
    type: 'milestone' as 'milestone',
    status: 'pending' as 'pending',
    priority: 'high' as 'high',
    actionLabel: 'See Project'
  },
  {
    id: 'n3',
    title: 'Task Assignment Completed',
    description: 'All tasks for the "Q3 Marketing Campaign" have been assigned to team members.',
    timestamp: 'Yesterday, 18:45 PM',
    type: 'task' as 'task',
    status: 'completed' as 'completed',
    priority: 'low' as 'low'
  },
  {
    id: 'n4',
    title: 'Performance Analytics',
    description: 'AI has analyzed your productivity patterns. You complete most tasks between 10AM-12PM. Consider scheduling important work during this period.',
    timestamp: 'Yesterday, 15:20 PM',
    type: 'report' as 'report',
    status: 'new' as 'new',
    priority: 'medium' as 'medium',
    actionLabel: 'See Analytics'
  },
  {
    id: 'n5',
    title: 'Document Review Required',
    description: 'The "Product Specification" document needs your review and approval before proceeding to development.',
    timestamp: '2 days ago',
    type: 'task' as 'task',
    status: 'pending' as 'pending',
    priority: 'high' as 'high',
    actionLabel: 'Review Now'
  }
];

// Mock chat sessions data
const mockChatSessions = [
  { id: 'chat1', title: 'Project Planning', lastActive: 'Today', hasNewMessage: true },
  { id: 'chat2', title: 'Marketing Campaign', lastActive: 'Yesterday', hasNewMessage: false },
  { id: 'chat3', title: 'Product Development', lastActive: '2 days ago', hasNewMessage: true },
  { id: 'chat4', title: 'Research Analysis', lastActive: '3 days ago', hasNewMessage: false },
  { id: 'chat5', title: 'Team Coordination', lastActive: 'Last week', hasNewMessage: false }
];

// 创建一个客户端组件包装器
export default function DSystem() {
  return <DSystemClient />;
}

// 定义消息接口
interface Message {
  id: number;
  type: 'ai' | 'user';
  content: any;
  timestamp: string;
  attachments?: Array<{
    type: 'image' | 'file';
    url: string;
    name?: string;
    size?: string;
  }>;
}

// 实际的客户端组件
function DSystemClient() {
  // 使用状态钩子
  const [activeNav, setActiveNav] = useState('home');
  const [inputValue, setInputValue] = useState('');
  // 移除搜索框相关状态
  const [mounted, setMounted] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showMyDPal, setShowMyDPal] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isMouseOverMenu, setIsMouseOverMenu] = useState(false);
  const [isMouseOverProfile, setIsMouseOverProfile] = useState(false);
  const [expandedProject, setExpandedProject] = useState<string | null>(null);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  
  // Doraemon头像配置状态
  const [dpalsAvatar, setDpalsAvatar] = useState({
    headgear: 'default',
    eyes: 'normal',
    mouth: 'smile',
    necklace: 'bell',
    pattern: 'classic',
    color: 'blue',
    experience: 0
  });
  
  // 点击页面其他区域关闭用户菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 主题相关
  let theme: 'dark' | 'light' = 'dark';
  let toggleTheme = () => {};
  
  try {
    // 尝试使用主题钩子，但只在客户端
    const themeContext = useTheme();
    theme = themeContext.theme;
    toggleTheme = themeContext.toggleTheme;
  } catch (e) {
    console.error('Theme context not available');
  }
  
  useEffect(() => {
    setMounted(true);
    
    // 初始化一条丰富的AI欢迎消息
    setMessages([
      {
        id: Date.now(),
        type: 'ai',
        content: [
          { 
            type: 'text', 
            content: `Good morning, David! Welcome back to Doraemon. Here's your daily briefing for ${new Date().toLocaleDateString('en-US', {weekday: 'long', month: 'long', day: 'numeric'})}.` 
          },
          {
            type: 'text',
            content: '**Today\'s Priority Tasks:**'
          },
          {
            type: 'text',
            content: '1. 📌 Submit Q3 marketing campaign proposal (Due: 3:00 PM)\n2. 📅 Prepare for product team meeting (2:00 PM - 3:30 PM)\n3. 📝 Review design updates for mobile app (High priority)'
          },
          {
            type: 'text',
            content: '**Upcoming Meetings:**'
          },
          {
            type: 'text',
            content: '• 10:30 AM - Weekly team standup (Meeting Room A)\n• 2:00 PM - Product strategy discussion (Virtual)\n• 4:30 PM - Client call with TechSolutions Inc.'
          },
          {
            type: 'text',
            content: '**Recommendations:**'
          },
          {
            type: 'text',
            content: 'Based on your recent activities, you might want to:\n1. Follow up on the feedback for your latest project proposal\n2. Review the analytics dashboard for Q2 marketing results\n3. Allocate time to finalize the content strategy document'
          },
          {
            type: 'text',
            content: 'Is there anything specific you\'d like to work on today?'
          }
        ],
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      }
    ]);
  }, []);

  // 滚动到最新消息
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // 根据鼠标状态决定是否显示菜单
  useEffect(() => {
    if (isMouseOverMenu || isMouseOverProfile) {
      setShowUserMenu(true);
    } else {
      const timer = setTimeout(() => {
        setShowUserMenu(false);
      }, 200); // 添加短暂延迟，避免鼠标移动过程中菜单闪烁
      return () => clearTimeout(timer);
    }
  }, [isMouseOverMenu, isMouseOverProfile]);

  // 处理发送消息
  const handleSendMessage = (message: string, files: File[]) => {
    if (!message.trim() && files.length === 0) return;

    // 添加用户消息
    const attachments = files.map(file => ({
      type: file.type.startsWith('image/') ? 'image' as const : 'file' as const,
      url: URL.createObjectURL(file),
      name: file.name,
      size: formatFileSize(file.size)
    }));

    const userMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: message,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      attachments
    };

    setMessages(prev => [...prev, userMessage]);
    
    // 显示AI正在输入状态
    setIsTyping(true);

    // 模拟AI回复延迟
    setTimeout(() => {
      const aiResponse = {
        id: Date.now() + 1,
        type: 'ai' as const,
        content: generateAIResponse(message, files),
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // 生成AI回复内容
  const generateAIResponse = (message: string, files: File[]) => {
    // 简单的回复逻辑，可以根据实际需求扩展
    if (message.toLowerCase().includes('leave') || message.toLowerCase().includes('vacation')) {
      return [
        { type: 'text', content: 'I can help you apply for leave. Would you like to proceed with a leave application?' },
        { type: 'text', content: 'To get started, I need some information:' },
        { type: 'text', content: '1. Type of leave (vacation, sick, personal)' },
        { type: 'text', content: '2. Start date' },
        { type: 'text', content: '3. End date' },
        { type: 'text', content: '4. Reason (optional)' }
      ];
    } else if (message.toLowerCase().includes('meeting') || message.toLowerCase().includes('schedule')) {
      return [
        { type: 'text', content: 'I can help you schedule a meeting. Please provide the following details:' },
        { type: 'text', content: '1. Meeting title' },
        { type: 'text', content: '2. Date and time' },
        { type: 'text', content: '3. Participants (separate with commas)' },
        { type: 'text', content: '4. Meeting agenda (optional)' }
      ];
    } else if (message.toLowerCase().includes('expense') || message.toLowerCase().includes('reimbursement')) {
      return [
        { type: 'text', content: 'I can help with expense reimbursement. Please provide:' },
        { type: 'text', content: '1. Expense amount' },
        { type: 'text', content: '2. Expense date' },
        { type: 'text', content: '3. Expense category' },
        { type: 'text', content: '4. Receipt (you can upload it)' }
      ];
    } else if (files.length > 0) {
      const imageFiles = files.filter(f => f.type.startsWith('image/'));
      if (imageFiles.length > 0) {
        return [
          { type: 'text', content: `Thank you for uploading ${files.length} file(s). I've received ${imageFiles.length} image(s).` },
          { type: 'text', content: 'What would you like me to do with these files?' }
        ];
      } else {
        return [
          { type: 'text', content: `Thank you for uploading ${files.length} file(s).` },
          { type: 'text', content: 'What would you like me to do with these files?' }
        ];
      }
    } else {
      return [
        { type: 'text', content: 'I understand you said: "' + message + '". How can I assist you with this request?' }
      ];
    }
  };
  
  // 如果没有挂载，显示骨架屏
  if (!mounted) {
    return (
      <div style={{ 
        display: 'flex', 
        minHeight: '100vh', 
        backgroundColor: '#0F172A', 
        color: '#F9FAFB'
      }}>
        <div style={{ 
          width: '256px', 
          backgroundColor: '#1E293B', 
          borderRight: '1px solid #334155' 
        }}></div>
        <div style={{ flex: 1, padding: '2rem' }}>
          <div style={{ height: '36px', width: '200px', backgroundColor: '#334155', borderRadius: '8px', marginBottom: '2rem' }}></div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '1.5rem' }}>
            {[1, 2, 3].map(i => (
              <div key={i} style={{ height: '100px', backgroundColor: '#1E293B', borderRadius: '12px' }}></div>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  // 颜色常量
  const colors = {
    bgMain: theme === 'light' ? '#F9FAFB' : '#0F172A',
    bgCard: theme === 'light' ? '#FFFFFF' : '#1E293B',
    bgElevated: theme === 'light' ? '#F1F5F9' : '#334155',
    bgSubtle: theme === 'light' ? '#F3F4F6' : '#1F2937',
    textPrimary: theme === 'light' ? '#111827' : '#F9FAFB',
    textSecondary: theme === 'light' ? '#4B5563' : '#E5E7EB',
    textTertiary: theme === 'light' ? '#9CA3AF' : '#9CA3AF',
    borderSubtle: theme === 'light' ? '#E5E7EB' : '#334155',
    borderStrong: theme === 'light' ? '#D1D5DB' : '#475569',
    primary: theme === 'light' ? '#2563EB' : '#3B82F6',
    primaryLight: '#60A5FA',
    accent: '#8B5CF6',
    success: '#10B981',
    error: '#EF4444'
  };
  
  // 渲染Doraemon头像为Base64 URL
  const renderDPalAvatar = (avatarOptions: any, colors: any) => {
    // 创建一个Canvas元素来绘制头像
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return '';
    
    // 设置背景颜色 (根据颜色选项)
    const bgColor = avatarOptions.color === 'blue' ? '#1A94E6' : 
                    avatarOptions.color === 'pink' ? '#FF7EB6' : 
                    avatarOptions.color === 'green' ? '#4CAF50' : 
                    avatarOptions.color === 'yellow' ? '#FFC107' : 
                    avatarOptions.color === 'purple' ? '#9C27B0' : '#E53935';
    
    // 绘制圆形背景
    ctx.fillStyle = bgColor;
    ctx.beginPath();
    ctx.arc(100, 100, 100, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制白色腹部
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.ellipse(100, 150, 70, 50, 0, 0, Math.PI, true);
    ctx.fill();
    
    // 绘制图案 (如果不是classic)
    if (avatarOptions.pattern !== 'classic') {
      ctx.save();
      ctx.globalAlpha = 0.2;
      ctx.beginPath();
      ctx.arc(100, 100, 99, 0, Math.PI * 2);
      ctx.clip();
      
      if (avatarOptions.pattern === 'striped') {
        // 斜条纹
        for (let i = -200; i < 400; i += 20) {
          ctx.beginPath();
          ctx.moveTo(i, 0);
          ctx.lineTo(i + 200, 200);
          ctx.lineWidth = 10;
          ctx.strokeStyle = 'black';
          ctx.stroke();
        }
      } else if (avatarOptions.pattern === 'dots') {
        // 圆点
        for (let x = 0; x < 200; x += 20) {
          for (let y = 0; y < 200; y += 20) {
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();
          }
        }
      } else if (avatarOptions.pattern === 'stars') {
        // 简化的星星 (小圆点代替)
        for (let x = 10; x < 200; x += 30) {
          for (let y = 10; y < 200; y += 30) {
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();
          }
        }
      } else if (avatarOptions.pattern === 'hearts') {
        // 简化的心形 (小圆点代替)
        for (let x = 10; x < 200; x += 40) {
          for (let y = 10; y < 200; y += 40) {
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();
          }
        }
      }
      
      ctx.restore();
    }
    
    // 绘制眼睛
    ctx.fillStyle = 'white';
    // 左眼
    ctx.beginPath();
    if (avatarOptions.eyes === 'closed') {
      // 闭眼只画一条线
      ctx.fillRect(60, 80, 20, 2);
    } else {
      // 正常眼睛是圆形
      ctx.arc(70, 80, 15, 0, Math.PI * 2);
      ctx.fill();
      
      // 眼球
      ctx.fillStyle = 'black';
      ctx.beginPath();
      
      let eyeXOffset = 0;
      let eyeYOffset = 0;
      
      if (avatarOptions.eyes === 'sleepy') {
        eyeYOffset = 5;
      } else if (avatarOptions.eyes === 'angry') {
        eyeYOffset = -3;
      } else if (avatarOptions.eyes === 'excited') {
        eyeXOffset = 3;
      }
      
      ctx.arc(70 + eyeXOffset, 80 + eyeYOffset, 7, 0, Math.PI * 2);
      ctx.fill();
    }
    
    // 右眼
    ctx.fillStyle = 'white';
    ctx.beginPath();
    if (avatarOptions.eyes === 'closed') {
      // 闭眼只画一条线
      ctx.fillRect(120, 80, 20, 2);
    } else {
      // 正常眼睛是圆形
      ctx.arc(130, 80, 15, 0, Math.PI * 2);
      ctx.fill();
      
      // 眼球
      ctx.fillStyle = 'black';
      ctx.beginPath();
      
      let eyeXOffset = 0;
      let eyeYOffset = 0;
      
      if (avatarOptions.eyes === 'sleepy') {
        eyeYOffset = 5;
      } else if (avatarOptions.eyes === 'angry') {
        eyeYOffset = -3;
      } else if (avatarOptions.eyes === 'excited') {
        eyeXOffset = -3;
      }
      
      ctx.arc(130 + eyeXOffset, 80 + eyeYOffset, 7, 0, Math.PI * 2);
      ctx.fill();
    }
    
    // 绘制鼻子
    ctx.fillStyle = 'red';
    ctx.beginPath();
    ctx.arc(100, 110, 10, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制嘴巴
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 2;
    
    if (avatarOptions.mouth === 'smile') {
      ctx.beginPath();
      ctx.arc(100, 130, 30, 0, Math.PI);
      ctx.stroke();
    } else if (avatarOptions.mouth === 'laugh') {
      ctx.beginPath();
      ctx.arc(100, 130, 30, 0, Math.PI);
      ctx.stroke();
      // 填充笑嘴
      ctx.fillStyle = 'rgba(0,0,0,0.1)';
      ctx.fill();
    } else if (avatarOptions.mouth === 'surprised') {
      ctx.beginPath();
      ctx.arc(100, 140, 15, 0, Math.PI * 2);
      ctx.stroke();
    } else if (avatarOptions.mouth === 'neutral') {
      ctx.beginPath();
      ctx.moveTo(70, 140);
      ctx.lineTo(130, 140);
      ctx.stroke();
    } else if (avatarOptions.mouth === 'sad') {
      ctx.beginPath();
      ctx.arc(100, 170, 30, Math.PI, Math.PI * 2);
      ctx.stroke();
    }
    
    // 绘制胡须
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 2;
    
    // 左上胡须
    ctx.beginPath();
    ctx.moveTo(65, 120);
    ctx.lineTo(15, 110);
    ctx.stroke();
    
    // 左中胡须
    ctx.beginPath();
    ctx.moveTo(65, 130);
    ctx.lineTo(15, 130);
    ctx.stroke();
    
    // 左下胡须
    ctx.beginPath();
    ctx.moveTo(65, 140);
    ctx.lineTo(15, 150);
    ctx.stroke();
    
    // 右上胡须
    ctx.beginPath();
    ctx.moveTo(135, 120);
    ctx.lineTo(185, 110);
    ctx.stroke();
    
    // 右中胡须
    ctx.beginPath();
    ctx.moveTo(135, 130);
    ctx.lineTo(185, 130);
    ctx.stroke();
    
    // 右下胡须
    ctx.beginPath();
    ctx.moveTo(135, 140);
    ctx.lineTo(185, 150);
    ctx.stroke();
    
    // 项链/铃铛
    if (avatarOptions.necklace !== 'none') {
      if (avatarOptions.necklace === 'bell') {
        // 红色项圈
        ctx.fillStyle = 'red';
        ctx.fillRect(65, 165, 70, 8);
        
        // 铃铛
        ctx.fillStyle = 'gold';
        ctx.beginPath();
        ctx.arc(100, 180, 12, 0, Math.PI * 2);
        ctx.fill();
        
        // 铃铛中的黑点
        ctx.fillStyle = 'black';
        ctx.beginPath();
        ctx.arc(100, 185, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // 铃铛底部线条
        ctx.beginPath();
        ctx.moveTo(90, 190);
        ctx.lineTo(110, 190);
        ctx.stroke();
      } else if (avatarOptions.necklace === 'bowtie') {
        // 蝴蝶结 (简化版)
        ctx.fillStyle = 'red';
        ctx.save();
        ctx.translate(85, 170);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-10, -10, 20, 20);
        ctx.restore();
        
        ctx.save();
        ctx.translate(115, 170);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-10, -10, 20, 20);
        ctx.restore();
        
        ctx.fillStyle = 'darkred';
        ctx.beginPath();
        ctx.arc(100, 170, 5, 0, Math.PI * 2);
        ctx.fill();
      } else if (avatarOptions.necklace === 'scarf') {
        // 围巾 (简化版)
        ctx.fillStyle = 'red';
        ctx.fillRect(60, 165, 80, 15);
        
        // 围巾垂下部分
        ctx.beginPath();
        ctx.moveTo(120, 180);
        ctx.lineTo(140, 200);
        ctx.lineTo(130, 200);
        ctx.lineTo(120, 180);
        ctx.fill();
      } else if (avatarOptions.necklace === 'pendant') {
        // 项链
        ctx.strokeStyle = 'silver';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(100, 165, 35, 10, 0, 0, Math.PI * 2);
        ctx.stroke();
        
        // 挂坠
        ctx.fillStyle = 'gold';
        ctx.fillRect(90, 175, 20, 25);
        ctx.strokeStyle = 'darkgoldenrod';
        ctx.strokeRect(90, 175, 20, 25);
      }
    }
    
    // 头饰
    if (avatarOptions.headgear !== 'default') {
      if (avatarOptions.headgear === 'hat') {
        // 帽子
        ctx.fillStyle = 'red';
        ctx.beginPath();
        ctx.ellipse(100, 30, 60, 30, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // 帽檐
        ctx.fillStyle = 'darkred';
        ctx.fillRect(50, 45, 100, 6);
      } else if (avatarOptions.headgear === 'bow') {
        // 蝴蝶结 (在头上)
        ctx.fillStyle = 'red';
        ctx.save();
        ctx.translate(75, 30);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-12, -12, 24, 24);
        ctx.restore();
        
        ctx.save();
        ctx.translate(125, 30);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-12, -12, 24, 24);
        ctx.restore();
        
        ctx.fillStyle = 'darkred';
        ctx.beginPath();
        ctx.arc(100, 30, 7, 0, Math.PI * 2);
        ctx.fill();
      } else if (avatarOptions.headgear === 'crown') {
        // 皇冠
        ctx.fillStyle = 'gold';
        // 皇冠基部
        ctx.fillRect(60, 20, 80, 20);
        
        // 皇冠的尖
        ctx.beginPath();
        ctx.moveTo(70, 20);
        ctx.lineTo(70, 0);
        ctx.lineTo(80, 20);
        ctx.fill();
        
        ctx.beginPath();
        ctx.moveTo(90, 20);
        ctx.lineTo(90, 0);
        ctx.lineTo(100, 20);
        ctx.fill();
        
        ctx.beginPath();
        ctx.moveTo(110, 20);
        ctx.lineTo(110, 0);
        ctx.lineTo(120, 20);
        ctx.fill();
      } else if (avatarOptions.headgear === 'headphones') {
        // 耳机
        ctx.fillStyle = 'black';
        
        // 头带
        ctx.fillRect(40, 10, 120, 8);
        
        // 左耳罩
        ctx.fillRect(30, 10, 15, 60);
        
        // 右耳罩
        ctx.fillRect(155, 10, 15, 60);
      }
    }
    
    // 返回Canvas的base64图像数据
    return canvas.toDataURL('image/png');
  };

  // 处理关闭MyDPal页面，并保存头像配置
  const handleCloseMyDPal = (newAvatarOptions?: any) => {
    if (newAvatarOptions) {
      // 如果传入了新的头像配置，则更新状态
      setDpalsAvatar(newAvatarOptions);
    }
    setShowMyDPal(false);
  };
  
  // 如果显示MyDPal页面，直接渲染它
  if (showMyDPal) {
    return <MyDPalPage theme={theme} colors={colors} onClose={handleCloseMyDPal} initialAvatar={dpalsAvatar} />;
  }
  
  return (
    <div style={{ 
      display: 'flex', 
      minHeight: '100vh', 
      width: '100%', 
      backgroundColor: colors.bgMain,
      color: colors.textPrimary,
      transition: 'background-color 0.3s ease, color 0.3s ease'
    }}>
      {/* 侧边栏 */}
      <div style={{ 
        width: '256px', 
        backgroundColor: colors.bgCard,
        borderRight: `1px solid ${colors.borderSubtle}`,
        display: 'flex',
        flexDirection: 'column',
        transition: 'background-color 0.3s ease, border-color 0.3s ease',
        height: '100vh', // 确保侧边栏占满整个视口高度
        position: 'relative' // 为绝对定位的子元素提供参考
      }}>
        {/* Logo 区域 */}
        <div style={{ 
          padding: '1.5rem', 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          borderBottom: `1px solid ${colors.borderSubtle}` 
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ 
              width: '2.5rem', 
              height: '2.5rem', 
              borderRadius: '0.5rem', 
              background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.accent} 100%)`,
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center', 
              marginRight: '0.75rem' 
            }}>
              <span style={{ color: 'white', fontWeight: 'bold', fontSize: '1.25rem' }}>D</span>
            </div>
            <div>
              <div style={{ fontWeight: 'bold', fontSize: '1.25rem', color: colors.textPrimary }}>Doraemon</div>
              <div style={{ fontSize: '0.75rem', color: colors.textTertiary }}>AI Work Platform</div>
            </div>
          </div>
          
          {/* 主题切换按钮 */}
          <button 
            onClick={toggleTheme} 
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              background: colors.bgElevated,
              color: colors.textPrimary,
              border: `1px solid ${colors.borderSubtle}`,
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
          >
            {theme === 'light' ? <MoonIcon /> : <SunIcon />}
          </button>
        </div>
        
        {/* 导航菜单和会话列表的滚动容器 */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          flex: '1 1 auto',
          overflowY: 'auto',
          height: 'calc(100% - 130px)' // 调整高度，减去了搜索栏的高度
        }}>
        {/* 导航菜单 */}
        <div style={{ padding: '0.5rem 0' }}>
          <div style={{ 
            padding: '0.25rem 1rem', 
            color: colors.textTertiary, 
            fontSize: '0.75rem', 
            fontWeight: 'bold', 
            textTransform: 'uppercase', 
            letterSpacing: '0.05em' 
          }}>
            Main
          </div>
          
            {['home', 'projects'].map((nav, index) => (
            <button
              key={nav}
              onClick={() => setActiveNav(nav)}
              style={{ 
                width: 'calc(100% - 2rem)', 
                margin: '0.25rem 1rem',
                justifyContent: 'flex-start',
                borderRadius: '0.5rem',
                  backgroundColor: activeNav === nav ? colors.primary : 'transparent',
                color: activeNav === nav ? 'white' : colors.textPrimary,
                border: 'none',
                padding: '10px 16px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24" 
                strokeWidth={1.5} 
                stroke="currentColor" 
                style={{width: '1.25rem', height: '1.25rem', marginRight: '0.5rem'}}
              >
                {nav === 'home' && <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />}
                {nav === 'projects' && <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />}
              </svg>
              <span>{nav.charAt(0).toUpperCase() + nav.slice(1)}</span>
            </button>
          ))}
        </div>
        
          {/* 最近聊天区域 */}
          <div style={{ 
            padding: '0.5rem 0',
            display: 'flex', 
            flexDirection: 'column',
            flex: 1
          }}>
            <div style={{ 
              padding: '0.5rem 1rem 0.5rem 1rem', 
              color: colors.textTertiary, 
              fontSize: '0.75rem', 
              fontWeight: 'bold', 
              textTransform: 'uppercase', 
              letterSpacing: '0.05em',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <span>Recent Chats</span>
            </div>
            
            {/* 渲染模拟聊天会话 */}
            {mockChatSessions.map((chat) => (
              <div 
                key={chat.id}
                style={{ 
                  padding: '0.4rem 0.75rem',
                  margin: '0.125rem 0.5rem',
                  borderRadius: '0.5rem',
                  fontSize: '0.875rem',
                  color: selectedChat === chat.id ? colors.primary : colors.textSecondary,
                  fontWeight: selectedChat === chat.id ? '500' : 'normal',
                  cursor: 'pointer',
                  backgroundColor: selectedChat === chat.id ? colors.bgSubtle : 'transparent',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  transition: 'all 0.15s ease',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onClick={() => {
                  setSelectedChat(chat.id);
                  if (activeNav !== 'home') {
                    setActiveNav('home');
                  }
                }}
                onMouseEnter={(e) => {
                  if (selectedChat !== chat.id) {
                    e.currentTarget.style.backgroundColor = theme === 'light' ? '#F5F5F5' : '#1A1E23';
                    e.currentTarget.style.transform = 'translateX(2px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedChat !== chat.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.transform = 'translateX(0)';
                  }
                }}
              >
                {/* 左侧圆点指示符 (仅当前选中项显示) */}
                {selectedChat === chat.id && (
                  <div style={{
                    position: 'absolute',
                    left: '0',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    width: '3px',
                    height: '60%',
                    backgroundColor: colors.primary,
                    borderRadius: '0 2px 2px 0'
                  }}></div>
                )}
                
                <div style={{ 
                  overflow: 'hidden', 
                  textOverflow: 'ellipsis', 
                  whiteSpace: 'nowrap',
                  flex: 1,
                  paddingLeft: selectedChat !== chat.id ? '6px' : '0',
                  transition: 'padding-left 0.15s ease'
                }}>
                  {chat.title}
                </div>
                {chat.hasNewMessage && (
                  <div style={{
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    backgroundColor: colors.primary,
                    marginLeft: '8px',
                    flexShrink: 0,
                    animation: 'pulse 2s infinite'
                  }}>
                    <style jsx>{`
                      @keyframes pulse {
                        0% {
                          box-shadow: 0 0 0 0 rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0.4);
                        }
                        70% {
                          box-shadow: 0 0 0 6px rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0);
                        }
                        100% {
                          box-shadow: 0 0 0 0 rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0);
                        }
                      }
                    `}</style>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {/* 用户区域 */}
        <div style={{ 
          padding: '0.75rem 1rem', 
          borderTop: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: colors.bgCard
        }}>
          <div 
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              cursor: 'pointer' 
            }}
            onMouseEnter={() => setIsMouseOverProfile(true)}
            onMouseLeave={() => setIsMouseOverProfile(false)}
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <div 
              style={{ 
                width: '36px', 
                height: '36px', 
                borderRadius: '18px', 
                overflow: 'hidden',
                marginRight: '0.75rem',
                backgroundColor: colors.primary,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#ffffff',
                fontWeight: 'bold',
                cursor: 'pointer'
              }}
              onClick={() => setShowMyDPal(true)}
            >
              {renderDPalAvatar(dpalsAvatar, colors)}
            </div>
            <div>
              <div style={{ 
                fontSize: '0.875rem', 
                fontWeight: '500', 
                color: colors.textPrimary 
              }}>David Kim</div>
              <div style={{ 
                fontSize: '0.75rem', 
                color: colors.textTertiary 
              }}>Product Manager</div>
            </div>
          </div>
          
          <div 
            style={{ 
              width: '2rem', 
              height: '2rem', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              borderRadius: '50%',
              color: colors.textTertiary,
              cursor: 'pointer',
              transition: 'all 0.2s ease',
            }}
            onMouseEnter={() => {
              setIsMouseOverMenu(true);
            }}
            onMouseLeave={() => {
              setIsMouseOverMenu(false);
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{ width: '1.25rem', height: '1.25rem' }}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
            <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>

        {/* 用户菜单 */}
        {showUserMenu && (
          <div
            ref={userMenuRef}
            style={{
              position: 'absolute',
              bottom: '70px',
              left: '20px',
              width: '220px',
              backgroundColor: colors.bgElevated,
              borderRadius: '12px',
              boxShadow: theme === 'light' 
                ? '0 4px 20px rgba(0, 0, 0, 0.1)' 
                : '0 4px 20px rgba(0, 0, 0, 0.3)',
              padding: '0.5rem',
              zIndex: 50,
              border: `1px solid ${colors.borderSubtle}`,
            }}
            onMouseEnter={() => setIsMouseOverMenu(true)}
            onMouseLeave={() => setIsMouseOverMenu(false)}
          >
            <div style={{
              padding: '0.75rem',
              borderBottom: `1px solid ${colors.borderSubtle}`,
              marginBottom: '0.5rem'
            }}>
              <div style={{
                fontSize: '0.9rem',
                fontWeight: '600',
                color: colors.textPrimary,
                marginBottom: '0.25rem'
              }}>David Kim</div>
              <div style={{
                fontSize: '0.8rem',
                color: colors.textTertiary
              }}><EMAIL></div>
            </div>
            
            {['Edit Profile', 'Preferences', 'Help & Support', 'Sign Out'].map((item, idx) => (
              <div
                key={idx}
                style={{
                  padding: '0.6rem 0.75rem',
                  fontSize: '0.875rem',
                  color: item === 'Sign Out' ? colors.error : colors.textPrimary,
                  borderRadius: '8px',
                  cursor: 'pointer',
                  transition: 'all 0.15s ease',
                  margin: '0.1rem 0',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
                onClick={() => {
                  if (item === 'Edit Profile') {
                    setShowMyDPal(true);
                    setShowUserMenu(false);
                  }
                }}
              >
                {item}
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* 主要内容区域 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column', 
        backgroundColor: colors.bgMain,
        height: '100vh',
        overflow: 'hidden'
      }}>
        {/* 顶部导航条 */}
        <div style={{ 
          height: '64px', 
          padding: '0 1.5rem', 
          borderBottom: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: colors.bgCard
        }}>
          {/* 当前页面标题 */}
          <div style={{ 
            fontSize: '1.25rem', 
            fontWeight: 'bold', 
            color: colors.textPrimary,
          }}>
            {activeNav === 'home' ? 'Dashboard' : 'Projects'}
          </div>
          
          {/* 右侧操作区 */}
          <div style={{ 
            display: 'flex', 
            alignItems: 'center',
            gap: '12px'
          }}>
            {/* 当前日期 */}
            <div style={{
              color: colors.textTertiary,
              fontSize: '0.875rem'
            }}>
              {new Date().toLocaleDateString('en-US', {weekday: 'short', month: 'short', day: 'numeric'})}
            </div>
          </div>
        </div>
        
        {/* 内容区 - 不同的页面内容 */}
        {activeNav === 'home' ? (
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            flexDirection: 'column',
            padding: '1.5rem',
            height: 'calc(100vh - 4rem)',
            maxHeight: 'calc(100vh - 4rem)',
            overflow: 'hidden'
          }}>
            {/* 内容区分两种情况：选中聊天或未选中聊天 */}
            {selectedChat ? (
              // 选中聊天时显示模拟会话界面
              <div style={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column',
                backgroundColor: colors.bgCard,
                borderRadius: '12px',
                overflow: 'hidden',
                border: `1px solid ${colors.borderSubtle}`
              }}>
                <MockSessionChat 
                  theme={theme}
                  colors={colors}
                />
              </div>
            ) : (
              // 未选中聊天时显示通知卡片列表
              <div style={{ 
                height: '100%',
                overflow: 'auto',
                paddingRight: '8px'
              }}>
                <div style={{
                  marginBottom: '1.5rem'
                }}>
                  <h1 style={{
                    fontSize: '1.75rem',
                    fontWeight: 'bold',
                    color: colors.textPrimary,
                    marginBottom: '0.5rem'
                  }}>
                    Welcome back, David
                  </h1>
                  <p style={{
                    fontSize: '1rem',
                    color: colors.textSecondary
                  }}>
                    Here are your latest updates and notifications
                  </p>
                </div>
                
                {/* 输入框 */}
                <div style={{ 
                  marginBottom: '2rem',
                  position: 'relative'
                }}>
                  <div style={{ 
                    width: '100%',
                    boxShadow: theme === 'light' 
                      ? '0 4px 20px rgba(0, 0, 0, 0.08)' 
                      : '0 4px 20px rgba(0, 0, 0, 0.3)',
                    borderRadius: '12px',
                    overflow: 'hidden'
                  }}>
                    <AIInputBox 
                      onSend={handleSendMessage}
                      placeholder="Ask Doraemon anything..."
                      theme={theme}
                      colors={colors}
                    />
                  </div>
                </div>
                
                {/* 通知卡片标题 */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '1rem'
                }}>
                  <h2 style={{
                    fontSize: '1.25rem',
                    fontWeight: '600',
                    color: colors.textPrimary
                  }}>
                    Notifications & Updates
                  </h2>
                  <div style={{
                    fontSize: '0.875rem',
                    color: colors.primary,
                    cursor: 'pointer'
                  }}>
                    View All
                  </div>
                </div>
                
                {/* 通知卡片列表 */}
                {mockNotifications.map((notification) => (
                  <NotificationCard
                    key={notification.id}
                    title={notification.title}
                    description={notification.description}
                    timestamp={notification.timestamp}
                    type={notification.type}
                    status={notification.status}
                    priority={notification.priority}
                    actionLabel={notification.actionLabel}
                    onAction={() => console.log(`Action clicked for ${notification.id}`)}
                    theme={theme}
                    colors={colors}
                  />
                ))}
              </div>
            )}
          </div>
        ) : (
          // Projects页面内容
          <div style={{ 
            flex: 1, 
            padding: '1.5rem', 
            overflow: 'auto',
            backgroundColor: colors.bgMain
          }}>
            {/* Projects页面内容 */}
            <div style={{ 
              backgroundColor: colors.bgCard,
              padding: '1.5rem',
              borderRadius: '12px',
              border: `1px solid ${colors.borderSubtle}`,
              marginBottom: '1.5rem'
            }}>
              <h2 style={{ 
                fontSize: '1.25rem', 
                fontWeight: '600', 
                color: colors.textPrimary,
                marginBottom: '1rem' 
              }}>
                Active Projects
              </h2>
              
              <div style={{ 
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
                gap: '1rem'
              }}>
                {mockProjects.map(project => (
                  <div key={project.id} style={{ 
                    backgroundColor: colors.bgElevated,
                    borderRadius: '8px',
                    padding: '1.25rem',
                    border: `1px solid ${colors.borderSubtle}`
                  }}>
                    <div style={{ 
                      fontSize: '1rem',
                      fontWeight: '600',
                      color: colors.textPrimary,
                      marginBottom: '0.75rem'
                    }}>
                      {project.name}
                    </div>
                    
                    <div style={{ 
                      fontSize: '0.875rem',
                      color: colors.textSecondary,
                      marginBottom: '1rem'
                    }}>
                      {project.sessions.length} active sessions
                    </div>
                    
                    <div style={{ 
                      display: 'flex',
                      justifyContent: 'space-between',
                      marginTop: 'auto'
                    }}>
                      <button style={{ 
                        backgroundColor: colors.bgCard,
                        color: colors.textPrimary,
                        border: `1px solid ${colors.borderSubtle}`,
                        borderRadius: '6px',
                        padding: '6px 12px',
                        fontSize: '0.75rem',
                        cursor: 'pointer'
                      }}>
                        View Details
                      </button>
                      
                      <button style={{
                        backgroundColor: colors.primary,
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '6px 12px',
                        fontSize: '0.75rem',
                        cursor: 'pointer'
                      }}>
                        Open
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}