/* D系统主题变量 */
:root, [data-theme="light"] {
  /* 基础色彩 */
  --primary: #2563EB;
  --primary-light: #60A5FA;
  --primary-dark: #1D4ED8;
  --accent: #8B5CF6;
  --accent-light: #A78BFA;
  
  /* 背景色 */
  --bg-main: #F9FAFB;
  --bg-card: #FFFFFF;
  --bg-elevated: #F1F5F9;
  --bg-subtle: #F3F4F6;
  
  /* 文本色 */
  --text-primary: #111827;
  --text-secondary: #4B5563;
  --text-tertiary: #9CA3AF;
  --text-on-primary: #FFFFFF;
  
  /* 边框色 */
  --border-subtle: #E5E7EB;
  --border-strong: #D1D5DB;
  
  /* 状态色 */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;
  
  /* 阴影 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] {
  /* 基础色彩 */
  --primary: #3B82F6;
  --primary-light: #60A5FA;
  --primary-dark: #2563EB;
  --accent: #8B5CF6;
  --accent-light: #A78BFA;
  
  /* 背景色 */
  --bg-main: #0F172A;
  --bg-card: #1E293B;
  --bg-elevated: #334155;
  --bg-subtle: #1F2937;
  
  /* 文本色 */
  --text-primary: #F9FAFB;
  --text-secondary: #E5E7EB;
  --text-tertiary: #9CA3AF;
  --text-on-primary: #FFFFFF;
  
  /* 边框色 */
  --border-subtle: #334155;
  --border-strong: #475569;
  
  /* 状态色保持一致性 */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;
  
  /* 阴影 - 暗色模式下更暗 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
}

/* 基础样式 */
body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-main);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 通用样式 */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
}

.text-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* D系统专用样式 */
.d-system-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-main);
  transition: background-color 0.3s ease;
}

.d-system-sidebar {
  width: 16rem;
  background-color: var(--bg-card);
  border-right: 1px solid var(--border-subtle);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.d-system-content {
  flex: 1;
  transition: background-color 0.3s ease;
}

/* 输入框样式 */
.d-input {
  background-color: var(--bg-subtle);
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.d-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮样式 */
.d-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 10px 16px;
}

.d-button-primary {
  background-color: var(--primary);
  color: var(--text-on-primary);
}

.d-button-primary:hover {
  background-color: var(--primary-dark);
}

.d-button-secondary {
  background-color: var(--bg-subtle);
  color: var(--text-primary);
}

.d-button-secondary:hover {
  background-color: var(--border-subtle);
}

/* 卡片样式 */
.d-card {
  background-color: var(--bg-card);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* 主题切换按钮 */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--bg-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-subtle);
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background: var(--bg-subtle);
}

/* 辅助类 */
.shadow-hover {
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.shadow-hover:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
} 