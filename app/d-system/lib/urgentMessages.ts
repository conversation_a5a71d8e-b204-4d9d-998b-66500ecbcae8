// 统一的urgent消息配置
export interface UrgentMessage {
  id: string;
  text: string;
  title: string;
  priority: 'urgent' | 'high';
  timestamp: Date;
  chatId: string;
  actionType: 'chat';
  caseNumber?: string;
  customer?: string;
  issueType?: string;
  description?: string;
  urgencyLevel?: string;
  assignedBy?: string;
  deadline?: string;
  mentionedUsers?: string[];
  aiContext?: string;
}

// 统一的urgent消息数据源
export const urgentMessages: UrgentMessage[] = [
  {
    id: 'urgent_1',
    text: 'R15 user request received! Urgent response needed.',
    title: 'CS Agent: VIP Reward System Issue - URGENT',
    priority: 'urgent',
    timestamp: new Date('2025-05-19 08:30'),
    chatId: 'chat_r15_urgent',
    actionType: 'chat',
    caseNumber: 'EC-2025-006',
    customer: '<PERSON> (VIP R15)',
    issueType: 'Reward System',
    description: 'AI flagged: R15 VIP player missing weekly login bonus - highest tier customer priority',
    assignedBy: 'CS Agent AI',
    deadline: '2025-05-19 12:00',
    mentionedUsers: ['@game-ops', '@vip-support', '@rewards-team'],
    aiContext: 'R15 player (highest VIP tier) did not receive scheduled weekly bonus. System may have skipped high-value customer.',
    urgencyLevel: 'Critical - Top Tier VIP'
  },
  {
    id: 'urgent_2',
    text: 'CRITICAL: Database connection lost. Immediate action required!',
    title: 'System Alert: Database Connection Failure - URGENT',
    priority: 'urgent',
    timestamp: new Date('2025-05-19 07:45'),
    chatId: 'chat_database_critical',
    actionType: 'chat',
    caseNumber: 'SYS-2025-001',
    issueType: 'Infrastructure',
    description: 'Critical database connection failure affecting all user services',
    assignedBy: 'System Monitor AI',
    deadline: '2025-05-19 08:00',
    mentionedUsers: ['@infrastructure-team', '@database-admin', '@on-call-engineer'],
    aiContext: 'Primary database cluster connection lost. Backup systems activated but full service restoration needed immediately.',
    urgencyLevel: 'Critical - System Wide Impact'
  },
  {
    id: 'urgent_3',
    text: 'System A has no tickets available. Please create new ones immediately.',
    title: 'Operations Alert: Ticket System Depletion - URGENT',
    priority: 'urgent',
    timestamp: new Date('2025-05-19 09:15'),
    chatId: 'chat_system_a_tickets',
    actionType: 'chat',
    caseNumber: 'OPS-2025-003',
    issueType: 'Operations',
    description: 'System A ticket pool exhausted - business operations at risk',
    assignedBy: 'Operations Monitor AI',
    deadline: '2025-05-19 10:00',
    mentionedUsers: ['@operations-team', '@system-admin', '@business-ops'],
    aiContext: 'Ticket generation system failed overnight. Current pool empty, preventing new customer service requests.',
    urgencyLevel: 'Critical - Business Operations'
  },
  {
    id: 'urgent_4',
    text: 'URGENT: Payment system offline. Customer transactions failing!',
    title: 'Payment Alert: Transaction System Failure - URGENT',
    priority: 'urgent',
    timestamp: new Date('2025-05-19 10:30'),
    chatId: 'chat_payment_urgent',
    actionType: 'chat',
    caseNumber: 'PAY-2025-002',
    issueType: 'Payment System',
    description: 'Payment gateway offline - all customer transactions failing',
    assignedBy: 'Payment Monitor AI',
    deadline: '2025-05-19 11:00',
    mentionedUsers: ['@payment-team', '@finance-ops', '@tech-lead'],
    aiContext: 'Payment processor connection lost. 100% transaction failure rate for past 15 minutes. Revenue impact critical.',
    urgencyLevel: 'Critical - Revenue Impact'
  },
  {
    id: 'urgent_5',
    text: 'ALERT: Security breach detected in user authentication system!',
    title: 'Security Alert: Authentication Breach - URGENT',
    priority: 'urgent',
    timestamp: new Date('2025-05-19 11:45'),
    chatId: 'chat_security_alert',
    actionType: 'chat',
    caseNumber: 'SEC-2025-001',
    issueType: 'Security',
    description: 'Unauthorized access attempt detected in authentication system',
    assignedBy: 'Security Monitor AI',
    deadline: '2025-05-19 12:00',
    mentionedUsers: ['@security-team', '@incident-response', '@compliance-officer'],
    aiContext: 'Multiple failed login attempts with sophisticated attack patterns. Potential credential stuffing attack in progress.',
    urgencyLevel: 'Critical - Security Breach'
  }
];

// 获取用于气泡显示的简化消息
export const getSpeechBubbleMessages = (): Array<{
  id: string;
  text: string;
  priority: 'urgent' | 'high';
  timestamp: Date;
  chatId: string;
  actionType: 'chat';
}> => {
  return urgentMessages.map(msg => ({
    id: msg.id,
    text: msg.text,
    priority: msg.priority,
    timestamp: msg.timestamp,
    chatId: msg.chatId,
    actionType: msg.actionType
  }));
};

// 获取用于弹窗显示的详细消息
export const getUrgentAlerts = (): UrgentMessage[] => {
  return urgentMessages.filter(msg => msg.priority === 'urgent');
};

// 获取用于通知列表的消息（转换为通知格式）
export const getNotificationMessages = (): Array<{
  id: string;
  type: 'task' | 'system' | 'security';
  title: string;
  time: string;
  priority: 'urgent' | 'high';
  content: {
    caseNumber?: string;
    customer?: string;
    issueType?: string;
    description?: string;
    assignedBy?: string;
    deadline?: string;
    mentionedUsers?: string[];
    aiContext?: string;
    urgencyLevel?: string;
  };
}> => {
  return urgentMessages.map(msg => ({
    id: msg.id.replace('urgent_', 'n_urgent_'),
    type: msg.issueType === 'Security' ? 'security' as const : 
          msg.issueType === 'Infrastructure' || msg.issueType === 'Operations' ? 'system' as const :
          'task' as const,
    title: msg.title,
    time: msg.timestamp.toISOString().slice(0, 16).replace('T', ' '),
    priority: msg.priority,
    content: {
      caseNumber: msg.caseNumber,
      customer: msg.customer,
      issueType: msg.issueType,
      description: msg.description,
      assignedBy: msg.assignedBy,
      deadline: msg.deadline,
      mentionedUsers: msg.mentionedUsers,
      aiContext: msg.aiContext,
      urgencyLevel: msg.urgencyLevel
    }
  }));
}; 