'use client';

import React, { createContext, useState, useContext, useEffect } from 'react';

type Theme = 'light' | 'dark';
type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('dark');
  const [themeMode, setThemeMode] = useState<ThemeMode>('system');
  const [mounted, setMounted] = useState(false);

  // 只在客户端执行
  useEffect(() => {
    setMounted(true);
    // 检查本地存储或系统偏好
    try {
      const savedMode = localStorage.getItem('d-system-theme-mode') as ThemeMode;
      const savedTheme = localStorage.getItem('d-system-theme') as Theme;
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      if (savedMode) {
        setThemeMode(savedMode);
        if (savedMode === 'system') {
          setTheme(prefersDark ? 'dark' : 'light');
        } else {
          setTheme(savedMode as Theme);
        }
      } else if (savedTheme) {
        setTheme(savedTheme);
        setThemeMode(savedTheme as ThemeMode);
      } else {
        setThemeMode('system');
        setTheme(prefersDark ? 'dark' : 'light');
      }
    } catch (e) {
      console.error('Theme detection failed:', e);
      setThemeMode('system');
      setTheme('dark');
    }
  }, []);

  // 监听系统主题变化
  useEffect(() => {
    if (!mounted || themeMode !== 'system') return;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setTheme(e.matches ? 'dark' : 'light');
    };
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [mounted, themeMode]);

  useEffect(() => {
    if (!mounted) return;
    
    // 根据主题设置document的data-theme属性
    document.documentElement.setAttribute('data-theme', theme);
    try {
      localStorage.setItem('d-system-theme', theme);
      localStorage.setItem('d-system-theme-mode', themeMode);
    } catch (e) {
      console.error('Failed to save theme preference:', e);
    }
  }, [theme, themeMode, mounted]);

  const handleSetThemeMode = (mode: ThemeMode) => {
    setThemeMode(mode);
    if (mode === 'system') {
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      setTheme(prefersDark ? 'dark' : 'light');
    } else {
      setTheme(mode as Theme);
    }
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    setThemeMode(newTheme);
  };

  // 如果尚未挂载，使用静态渲染以避免水合错误
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider value={{ theme, themeMode, setThemeMode: handleSetThemeMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
} 