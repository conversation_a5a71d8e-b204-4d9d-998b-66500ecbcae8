/* 基础容器样式，可以被d-system.css中的样式覆盖 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-main);
  color: var(--text-primary);
}

.container {
  display: flex;
  min-height: 100vh;
  width: 100%;
}

.sidebar {
  width: 256px;
  background-color: var(--bg-card);
  border-right: 1px solid var(--border-subtle);
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  background-color: var(--bg-main);
} 