import React from 'react'

interface DoraemonIconProps {
  size?: number
  className?: string
}

export const DoraemonIcon: React.FC<DoraemonIconProps> = ({ size = 64, className = '' }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 200 200"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 头部 */}
      <circle cx="100" cy="90" r="60" fill="#4A90E2" stroke="#2E5C8A" strokeWidth="2" />
      
      {/* 脸部 */}
      <ellipse cx="100" cy="110" rx="45" ry="35" fill="#FFFFFF" />
      
      {/* 眼睛 */}
      <circle cx="85" cy="95" r="8" fill="#000000" />
      <circle cx="115" cy="95" r="8" fill="#000000" />
      <circle cx="87" cy="93" r="2" fill="#FFFFFF" />
      <circle cx="117" cy="93" r="2" fill="#FFFFFF" />
      
      {/* 鼻子 */}
      <circle cx="100" cy="105" r="4" fill="#FF6B6B" />
      
      {/* 嘴巴 */}
      <path d="M 85 120 Q 100 130 115 120" stroke="#000000" strokeWidth="2" fill="none" />
      
      {/* 胡须 */}
      <line x1="60" y1="105" x2="80" y2="105" stroke="#000000" strokeWidth="1.5" />
      <line x1="120" y1="105" x2="140" y2="105" stroke="#000000" strokeWidth="1.5" />
      <line x1="60" y1="115" x2="80" y2="115" stroke="#000000" strokeWidth="1.5" />
      <line x1="120" y1="115" x2="140" y2="115" stroke="#000000" strokeWidth="1.5" />
      
      {/* 铃铛 */}
      <circle cx="100" cy="140" r="8" fill="#FFD700" stroke="#FFA500" strokeWidth="1" />
      <line x1="100" y1="135" x2="100" y2="145" stroke="#FFA500" strokeWidth="1" />
      
      {/* 身体 */}
      <ellipse cx="100" cy="170" rx="40" ry="25" fill="#4A90E2" stroke="#2E5C8A" strokeWidth="2" />
      
      {/* 口袋 */}
      <path d="M 80 155 Q 100 175 120 155 Q 120 165 100 170 Q 80 165 80 155" fill="#FFFFFF" stroke="#CCCCCC" strokeWidth="1" />
    </svg>
  )
} 