'use client'
import React, { useEffect, useRef, useState } from 'react'

interface Particle {
  id: number
  x: number
  y: number
  vx: number
  vy: number
  life: number
  maxLife: number
  color: string
  size: number
}

interface MouseTrailProps {
  className?: string
}

export const MouseTrail: React.FC<MouseTrailProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const particlesRef = useRef<Particle[]>([])
  const mouseRef = useRef({ x: 0, y: 0 })
  const animationIdRef = useRef<number>()
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // Mouse move handler
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current = { x: e.clientX, y: e.clientY }
      setIsVisible(true)
      
      // Create new particles at mouse position
      for (let i = 0; i < 3; i++) {
        const particle: Particle = {
          id: Date.now() + Math.random(),
          x: e.clientX + (Math.random() - 0.5) * 10,
          y: e.clientY + (Math.random() - 0.5) * 10,
          vx: (Math.random() - 0.5) * 4,
          vy: (Math.random() - 0.5) * 4,
          life: 60,
          maxLife: 60,
          color: `hsl(${200 + Math.random() * 160}, 80%, 60%)`,
          size: Math.random() * 6 + 2
        }
        particlesRef.current.push(particle)
      }
    }

    // Mouse enter/leave handlers
    const handleMouseEnter = () => setIsVisible(true)
    const handleMouseLeave = () => setIsVisible(false)

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // Update and draw particles
      particlesRef.current = particlesRef.current.filter(particle => {
        // Update particle
        particle.x += particle.vx
        particle.y += particle.vy
        particle.life--
        particle.vy += 0.1 // gravity
        particle.vx *= 0.98 // air resistance
        particle.vy *= 0.98

        // Draw particle
        const alpha = particle.life / particle.maxLife
        ctx.save()
        ctx.globalAlpha = alpha
        
        // Create gradient for glow effect
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, particle.size * 2
        )
        gradient.addColorStop(0, particle.color)
        gradient.addColorStop(1, 'transparent')
        
        ctx.fillStyle = gradient
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
        ctx.fill()
        
        // Add sparkle effect
        ctx.fillStyle = 'white'
        ctx.globalAlpha = alpha * 0.8
        ctx.beginPath()
        ctx.arc(particle.x, particle.y, particle.size * 0.3, 0, Math.PI * 2)
        ctx.fill()
        
        ctx.restore()

        return particle.life > 0
      })

      // Draw mouse cursor glow
      if (isVisible) {
        const { x, y } = mouseRef.current
        
        // Outer glow
        ctx.save()
        ctx.globalAlpha = 0.3
        const outerGradient = ctx.createRadialGradient(x, y, 0, x, y, 50)
        outerGradient.addColorStop(0, 'rgba(59, 130, 246, 0.6)')
        outerGradient.addColorStop(0.5, 'rgba(147, 51, 234, 0.4)')
        outerGradient.addColorStop(1, 'transparent')
        ctx.fillStyle = outerGradient
        ctx.beginPath()
        ctx.arc(x, y, 50, 0, Math.PI * 2)
        ctx.fill()
        
        // Inner glow
        ctx.globalAlpha = 0.6
        const innerGradient = ctx.createRadialGradient(x, y, 0, x, y, 20)
        innerGradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)')
        innerGradient.addColorStop(0.5, 'rgba(59, 130, 246, 0.6)')
        innerGradient.addColorStop(1, 'transparent')
        ctx.fillStyle = innerGradient
        ctx.beginPath()
        ctx.arc(x, y, 20, 0, Math.PI * 2)
        ctx.fill()
        
        ctx.restore()
      }

      animationIdRef.current = requestAnimationFrame(animate)
    }

    // Start animation
    animate()

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseenter', handleMouseEnter)
    document.addEventListener('mouseleave', handleMouseLeave)

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseenter', handleMouseEnter)
      document.removeEventListener('mouseleave', handleMouseLeave)
      
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
    }
  }, [])

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none z-50 ${className}`}
      style={{ 
        zIndex: 50,
        mixBlendMode: 'screen'
      }}
    />
  )
} 