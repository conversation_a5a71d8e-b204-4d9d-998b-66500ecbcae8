'use client'
import React from 'react'

interface CSSTimeTunnelProps {
  className?: string
}

export const CSSTimeTunnel: React.FC<CSSTimeTunnelProps> = ({ className = '' }) => {
  return (
    <>
      {/* 全屏时间隧道背景 */}
      <div className={`fixed inset-0 w-full h-full overflow-hidden z-0 ${className}`}>
        
        {/* 静态深空背景 - 不参与动画 */}
        <div className="tunnel-bg" />
        
        {/* 高性能时间隧道 */}
        <div className="tunnel-wrapper">
          
          {/* 核心隧道 - 只有6个环，减少DOM负担 */}
          <div className="tunnel-core">
            {Array.from({ length: 6 }, (_, i) => (
              <div
                key={i}
                className="tunnel-ring"
                style={{
                  '--index': i,
                  '--delay': `${i * 0.4}s`
                } as React.CSSProperties & { [key: string]: any }}
              />
            ))}
          </div>
          
          {/* 简化粒子系统 - 只有12个粒子 */}
          <div className="particle-system">
            {Array.from({ length: 12 }, (_, i) => (
              <div
                key={i}
                className="particle"
                style={{
                  '--index': i,
                  '--delay': `${i * 0.5}s`
                } as React.CSSProperties & { [key: string]: any }}
              />
            ))}
          </div>
          
          {/* 中心能量核心 */}
          <div className="energy-core" />
          
          {/* 单一旋转光环 */}
          <div className="orbit-ring" />
          
        </div>
      </div>

      {/* 极致优化的CSS - 专为120fps设计 */}
      <style jsx>{`
        /* 静态背景 - 不参与动画，减少重绘 */
        .tunnel-bg {
          position: absolute;
          inset: 0;
          background: radial-gradient(ellipse at center,
            rgb(26, 26, 46) 0%,
            rgb(22, 33, 62) 30%,
            rgb(15, 52, 96) 60%,
            rgb(83, 52, 131) 100%);
          will-change: auto;
        }

        /* 隧道容器 - 使用transform优化 */
        .tunnel-wrapper {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100vw;
          height: 100vh;
          transform: translate(-50%, -50%);
          perspective: 600px;
          transform-style: preserve-3d;
          will-change: auto;
        }

        /* 隧道核心 - GPU加速 */
        .tunnel-core {
          position: absolute;
          width: 100%;
          height: 100%;
          transform: translateZ(0); /* 强制GPU层 */
          transform-style: preserve-3d;
        }

        /* 隧道环 - 极致优化 */
        .tunnel-ring {
          position: absolute;
          top: 50%;
          left: 50%;
          width: calc(120px + var(--index) * 200px);
          height: calc(120px + var(--index) * 200px);
          border: 1px solid hsl(calc(220 + var(--index) * 12), 60%, calc(45% + var(--index) * 8%));
          border-radius: 50%;
          transform: translate(-50%, -50%) translateZ(calc(var(--index) * -200px));
          opacity: calc(0.9 - var(--index) * 0.12);
          animation: tunnel-flow 4.8s linear infinite var(--delay);
          will-change: transform, opacity;
          backface-visibility: hidden;
          contain: layout style paint;
        }

        /* 超流畅隧道动画 - 使用最优transform */
        @keyframes tunnel-flow {
          0% {
            transform: translate(-50%, -50%) translateZ(calc(var(--index) * -200px)) scale(1);
            opacity: calc(0.9 - var(--index) * 0.12);
          }
          100% {
            transform: translate(-50%, -50%) translateZ(calc(var(--index) * -200px + 1200px)) scale(3.2);
            opacity: 0;
          }
        }

        /* 粒子系统容器 */
        .particle-system {
          position: absolute;
          width: 100%;
          height: 100%;
          transform: translateZ(0); /* GPU层 */
        }

        /* 高性能粒子 - 预计算位置 */
        .particle {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 1px;
          height: 1px;
          background: hsl(calc(200 + var(--index) * 15), 75%, 65%);
          border-radius: 50%;
          transform: translate(-50%, -50%) 
                   translateX(calc(cos(var(--index) * 0.524) * 120px))
                   translateY(calc(sin(var(--index) * 0.524) * 120px));
          animation: particle-zoom 6.4s linear infinite var(--delay);
          will-change: transform, opacity;
          backface-visibility: hidden;
          contain: layout style paint;
        }

        /* 粒子缩放动画 - 纯transform优化 */
        @keyframes particle-zoom {
          0% {
            transform: translate(-50%, -50%) 
                     translateX(calc(cos(var(--index) * 0.524) * 120px))
                     translateY(calc(sin(var(--index) * 0.524) * 120px))
                     translateZ(0px) scale(0.8);
            opacity: 0;
          }
          25% {
            opacity: 1;
          }
          75% {
            opacity: 1;
          }
          100% {
            transform: translate(-50%, -50%) 
                     translateX(calc(cos(var(--index) * 0.524) * 240px))
                     translateY(calc(sin(var(--index) * 0.524) * 240px))
                     translateZ(800px) scale(2.5);
            opacity: 0;
          }
        }

        /* 能量核心 - 简单脉冲 */
        .energy-core {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 24px;
          height: 24px;
          background: radial-gradient(circle,
            rgba(59, 130, 246, 0.95) 0%,
            rgba(139, 92, 246, 0.7) 60%,
            transparent 100%);
          border-radius: 50%;
          transform: translate(-50%, -50%) translateZ(0);
          animation: core-glow 3.6s ease-in-out infinite;
          will-change: transform;
          backface-visibility: hidden;
        }

        /* 核心发光动画 */
        @keyframes core-glow {
          0%, 100% {
            transform: translate(-50%, -50%) translateZ(0) scale(1);
          }
          50% {
            transform: translate(-50%, -50%) translateZ(0) scale(1.25);
          }
        }

        /* 轨道光环 - 简单旋转 */
        .orbit-ring {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 180px;
          height: 180px;
          border: 0.5px solid rgba(59, 130, 246, 0.25);
          border-top-color: rgba(59, 130, 246, 0.7);
          border-radius: 50%;
          transform: translate(-50%, -50%) translateZ(0);
          animation: ring-rotate 4.8s linear infinite;
          will-change: transform;
          backface-visibility: hidden;
        }

        /* 光环旋转动画 */
        @keyframes ring-rotate {
          from {
            transform: translate(-50%, -50%) translateZ(0) rotate(0deg);
          }
          to {
            transform: translate(-50%, -50%) translateZ(0) rotate(360deg);
          }
        }

        /* 全局性能优化 */
        .tunnel-core *,
        .particle-system *,
        .energy-core,
        .orbit-ring {
          /* 启用硬件加速 */
          transform: translateZ(0);
          -webkit-transform: translateZ(0);
          /* 优化重绘性能 */
          will-change: transform, opacity;
          backface-visibility: hidden;
          -webkit-backface-visibility: hidden;
          /* 减少重排重绘 */
          contain: layout style paint;
        }

        /* 移动端优化 - 减少复杂度 */
        @media (max-width: 768px) {
          .tunnel-ring {
            border-width: 0.5px;
            animation-duration: 4s;
          }
          
          .particle {
            animation-duration: 5.6s;
          }
          
          .orbit-ring {
            width: 140px;
            height: 140px;
            animation-duration: 4s;
          }
          
          .energy-core {
            width: 20px;
            height: 20px;
          }
        }

        /* 低性能设备优化 */
        @media (prefers-reduced-motion: reduce) {
          .tunnel-ring,
          .particle,
          .energy-core,
          .orbit-ring {
            animation-duration: 8s;
          }
        }

        /* 高刷新率屏幕优化 */
        @media (min-resolution: 120dpi) and (min-width: 1200px) {
          .tunnel-ring {
            animation-duration: 4s;
          }
          
          .particle {
            animation-duration: 5.6s;
          }
          
          .orbit-ring {
            animation-duration: 4s;
          }
        }
      `}</style>
    </>
  )
}

export default CSSTimeTunnel 