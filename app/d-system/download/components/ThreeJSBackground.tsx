'use client'
import React from 'react'

interface ThreeJSBackgroundProps {
  className?: string
}

export const ThreeJSBackground: React.FC<ThreeJSBackgroundProps> = ({ className = '' }) => {
  return (
    <div className={`fixed inset-0 w-full h-full overflow-hidden ${className}`}>
      {/* 时间隧道背景 */}
      <div className="time-tunnel-container">
        {/* 主隧道环 */}
        <div className="tunnel-rings">
          {Array.from({ length: 50 }, (_, i) => (
            <div
              key={i}
              className="tunnel-ring"
              style={{
                '--ring-index': i,
                '--ring-delay': `${i * 0.1}s`,
                '--ring-z': i * -20,
                '--ring-scale': 1 + i * 0.15,
                '--ring-opacity': Math.max(0.1, 1 - i * 0.018),
                '--ring-hue': 220 + i * 3, // 蓝紫色渐变
              } as React.CSSProperties}
            />
          ))}
        </div>

        {/* 内层光环 */}
        <div className="inner-rings">
          {Array.from({ length: 30 }, (_, i) => (
            <div
              key={i}
              className="inner-ring"
              style={{
                '--ring-index': i,
                '--ring-delay': `${i * 0.15}s`,
                '--ring-z': i * -30,
                '--ring-scale': 0.8 + i * 0.2,
                '--ring-opacity': Math.max(0.05, 0.8 - i * 0.025),
              } as React.CSSProperties}
            />
          ))}
        </div>

        {/* 螺旋能量流 */}
        <div className="energy-spirals">
          {Array.from({ length: 8 }, (_, i) => (
            <div
              key={i}
              className="energy-spiral"
              style={{
                '--spiral-index': i,
                '--spiral-rotation': i * 45,
                '--spiral-delay': `${i * 0.2}s`,
              } as React.CSSProperties}
            />
          ))}
        </div>

        {/* 时空粒子 */}
        <div className="time-particles">
          {Array.from({ length: 100 }, (_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                '--particle-x': `${(Math.random() - 0.5) * 100}vw`,
                '--particle-y': `${(Math.random() - 0.5) * 100}vh`,
                '--particle-delay': `${Math.random() * 3}s`,
                '--particle-duration': `${2 + Math.random() * 3}s`,
                '--particle-hue': 200 + Math.random() * 80,
              } as React.CSSProperties}
            />
          ))}
        </div>

        {/* 中心光源 */}
        <div className="center-light" />

        {/* 隧道壁光效 */}
        <div className="tunnel-glow" />
      </div>

      <style jsx>{`
        .time-tunnel-container {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          perspective: 1000px;
          perspective-origin: center center;
          background: radial-gradient(ellipse at center, #0a0a2e 0%, #000 70%);
          overflow: hidden;
        }

        .tunnel-rings {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          height: 100%;
        }

        .tunnel-ring {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 60vw;
          height: 60vw;
          border: 3px solid transparent;
          border-radius: 50%;
          transform: translate(-50%, -50%) 
                   translateZ(calc(var(--ring-z) * 1px)) 
                   scale(var(--ring-scale))
                   rotateZ(calc(var(--ring-index) * 15deg));
          background: conic-gradient(
            from 0deg,
            hsla(var(--ring-hue), 80%, 60%, var(--ring-opacity)) 0%,
            hsla(calc(var(--ring-hue) + 30), 90%, 70%, calc(var(--ring-opacity) * 0.8)) 25%,
            hsla(calc(var(--ring-hue) + 60), 80%, 50%, var(--ring-opacity)) 50%,
            hsla(calc(var(--ring-hue) + 30), 90%, 70%, calc(var(--ring-opacity) * 0.8)) 75%,
            hsla(var(--ring-hue), 80%, 60%, var(--ring-opacity)) 100%
          );
          mask: radial-gradient(circle, transparent 45%, white 48%, white 52%, transparent 55%);
          -webkit-mask: radial-gradient(circle, transparent 45%, white 48%, white 52%, transparent 55%);
          animation: 
            tunnel-forward 4s linear infinite,
            tunnel-rotate 8s linear infinite;
          animation-delay: var(--ring-delay);
          box-shadow: 
            inset 0 0 50px hsla(var(--ring-hue), 90%, 70%, 0.3),
            0 0 30px hsla(var(--ring-hue), 90%, 70%, 0.2);
        }

        .inner-rings {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          height: 100%;
        }

        .inner-ring {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 40vw;
          height: 40vw;
          border-radius: 50%;
          transform: translate(-50%, -50%) 
                   translateZ(calc(var(--ring-z) * 1px)) 
                   scale(var(--ring-scale));
          background: radial-gradient(
            circle,
            hsla(200, 100%, 80%, var(--ring-opacity)) 0%,
            hsla(220, 90%, 70%, calc(var(--ring-opacity) * 0.5)) 30%,
            transparent 60%
          );
          animation: 
            tunnel-forward 3s linear infinite,
            inner-pulse 2s ease-in-out infinite;
          animation-delay: var(--ring-delay);
        }

        .energy-spirals {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          height: 100%;
        }

        .energy-spiral {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 2px;
          height: 200vh;
          transform: translate(-50%, -50%) 
                   rotateZ(calc(var(--spiral-rotation) * 1deg))
                   translateX(25vw);
          background: linear-gradient(
            to bottom,
            hsla(240, 100%, 80%, 0.8) 0%,
            hsla(260, 90%, 70%, 0.6) 30%,
            hsla(280, 80%, 60%, 0.4) 60%,
            transparent 100%
          );
          animation: 
            spiral-move 6s linear infinite,
            spiral-glow 3s ease-in-out infinite;
          animation-delay: var(--spiral-delay);
          filter: blur(1px);
          box-shadow: 0 0 10px currentColor;
        }

        .time-particles {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
        }

        .particle {
          position: absolute;
          width: 2px;
          height: 2px;
          border-radius: 50%;
          background: hsla(var(--particle-hue), 100%, 70%, 0.8);
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          animation: particle-flow var(--particle-duration) linear infinite;
          animation-delay: var(--particle-delay);
          box-shadow: 0 0 6px currentColor;
        }

        .center-light {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: radial-gradient(
            circle,
            rgba(255, 255, 255, 1) 0%,
            rgba(135, 206, 250, 0.8) 30%,
            rgba(75, 0, 130, 0.4) 70%,
            transparent 100%
          );
          transform: translate(-50%, -50%);
          animation: center-pulse 2s ease-in-out infinite;
          filter: blur(2px);
          box-shadow: 
            0 0 50px rgba(255, 255, 255, 0.5),
            0 0 100px rgba(135, 206, 250, 0.3),
            0 0 200px rgba(75, 0, 130, 0.2);
        }

        .tunnel-glow {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: radial-gradient(
            ellipse at center,
            transparent 20%,
            rgba(72, 61, 139, 0.1) 40%,
            rgba(25, 25, 112, 0.2) 70%,
            rgba(0, 0, 0, 0.8) 100%
          );
          animation: tunnel-breathe 4s ease-in-out infinite;
        }

        @keyframes tunnel-forward {
          0% {
            transform: translate(-50%, -50%) 
                     translateZ(calc(var(--ring-z) * 1px)) 
                     scale(var(--ring-scale))
                     rotateZ(calc(var(--ring-index) * 15deg));
          }
          100% {
            transform: translate(-50%, -50%) 
                     translateZ(calc((var(--ring-z) + 1000) * 1px)) 
                     scale(calc(var(--ring-scale) + 2))
                     rotateZ(calc(var(--ring-index) * 15deg + 360deg));
          }
        }

        @keyframes tunnel-rotate {
          0% { transform: translate(-50%, -50%) rotateZ(0deg); }
          100% { transform: translate(-50%, -50%) rotateZ(360deg); }
        }

        @keyframes inner-pulse {
          0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(var(--ring-scale)); }
          50% { opacity: 0.8; transform: translate(-50%, -50%) scale(calc(var(--ring-scale) * 1.1)); }
        }

        @keyframes spiral-move {
          0% { 
            transform: translate(-50%, -50%) 
                     rotateZ(calc(var(--spiral-rotation) * 1deg))
                     translateX(25vw) 
                     rotateY(0deg); 
          }
          100% { 
            transform: translate(-50%, -50%) 
                     rotateZ(calc(var(--spiral-rotation) * 1deg + 360deg))
                     translateX(25vw) 
                     rotateY(360deg); 
          }
        }

        @keyframes spiral-glow {
          0%, 100% { filter: blur(1px) brightness(1); }
          50% { filter: blur(2px) brightness(1.5); }
        }

        @keyframes particle-flow {
          0% {
            transform: translate(-50%, -50%) 
                     translate(var(--particle-x), var(--particle-y)) 
                     translateZ(-1000px) 
                     scale(0.5);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translate(-50%, -50%) 
                     translate(calc(var(--particle-x) * 0.1), calc(var(--particle-y) * 0.1)) 
                     translateZ(100px) 
                     scale(2);
            opacity: 0;
          }
        }

        @keyframes center-pulse {
          0%, 100% { 
            transform: translate(-50%, -50%) scale(1); 
            opacity: 0.8; 
          }
          50% { 
            transform: translate(-50%, -50%) scale(1.5); 
            opacity: 1; 
          }
        }

        @keyframes tunnel-breathe {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.6; }
        }
      `}</style>
    </div>
  )
} 