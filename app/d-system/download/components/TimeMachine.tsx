'use client'
import React, { useState, useEffect } from 'react'

interface TimeMachineProps {
  className?: string
  isActive?: boolean
}

export const TimeMachine: React.FC<TimeMachineProps> = ({ 
  className = '',
  isActive = false 
}) => {
  const [energyLevel, setEnergyLevel] = useState(0)
  const [controlsActive, setControlsActive] = useState(false)
  const [timeDisplayValue, setTimeDisplayValue] = useState('2024')

  useEffect(() => {
    const interval = setInterval(() => {
      setEnergyLevel(Math.random())
      if (isActive) {
        setControlsActive(prev => !prev)
        // 随机时间显示
        const years = ['1969', '1985', '2024', '2077', '3000', '????']
        setTimeDisplayValue(years[Math.floor(Math.random() * years.length)])
      }
    }, 1500)

    return () => clearInterval(interval)
  }, [isActive])

  return (
    <div className={`relative w-96 h-80 ${className}`}>
      {/* 时光机平台 */}
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 400 320"
        className="relative z-10 drop-shadow-2xl"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          {/* 更多渐变定义 */}
          <linearGradient id="platformGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#6a6a6a" />
            <stop offset="30%" stopColor="#4a4a4a" />
            <stop offset="70%" stopColor="#2a2a2a" />
            <stop offset="100%" stopColor="#1a1a1a" />
          </linearGradient>
          
          <linearGradient id="seatGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#ff6b6b" />
            <stop offset="30%" stopColor="#e74c3c" />
            <stop offset="70%" stopColor="#c0392b" />
            <stop offset="100%" stopColor="#a93226" />
          </linearGradient>
          
          <linearGradient id="controlGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#ffffff" />
            <stop offset="30%" stopColor="#f4f4f4" />
            <stop offset="70%" stopColor="#d0d0d0" />
            <stop offset="100%" stopColor="#a0a0a0" />
          </linearGradient>
          
          <radialGradient id="glowEffect" cx="50%" cy="50%" r="60%">
            <stop offset="0%" stopColor="rgba(0,255,255,0.9)" />
            <stop offset="40%" stopColor="rgba(0,255,255,0.5)" />
            <stop offset="70%" stopColor="rgba(0,255,255,0.2)" />
            <stop offset="100%" stopColor="rgba(0,255,255,0)" />
          </radialGradient>
          
          <radialGradient id="energyGlow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="rgba(255,255,0,0.8)" />
            <stop offset="60%" stopColor="rgba(255,100,0,0.4)" />
            <stop offset="100%" stopColor="rgba(255,0,0,0)" />
          </radialGradient>
          
          <filter id="machineGlow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>

          <filter id="energyFilter" x="-100%" y="-100%" width="300%" height="300%">
            <feGaussianBlur stdDeviation="2" result="blur"/>
            <feColorMatrix values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0" result="matrix"/>
            <feMerge>
              <feMergeNode in="blur"/>
              <feMergeNode in="matrix"/>
            </feMerge>
          </filter>
        </defs>

        {/* 底部阴影 */}
        <ellipse 
          cx="200" 
          cy="310" 
          rx="190" 
          ry="8" 
          fill="rgba(0,0,0,0.6)"
          opacity="0.8"
        />

        {/* 平台底座 - 加强立体感 */}
        <ellipse 
          cx="200" 
          cy="290" 
          rx="180" 
          ry="18" 
          fill="url(#platformGradient)"
          filter="url(#machineGlow)"
          stroke="rgba(255,255,255,0.1)"
          strokeWidth="1"
        />
        
        {/* 平台主体 */}
        <rect 
          x="50" 
          y="270" 
          width="300" 
          height="25" 
          rx="12" 
          fill="url(#platformGradient)"
          stroke="rgba(255,255,255,0.2)"
          strokeWidth="1"
          filter="url(#machineGlow)"
        />

        {/* 平台装饰线条 */}
        <rect x="60" y="275" width="280" height="2" rx="1" fill="rgba(0,255,255,0.3)" className={isActive ? "animate-pulse" : ""} />
        <rect x="70" y="285" width="260" height="1" rx="0.5" fill="rgba(0,255,255,0.2)" className={isActive ? "animate-pulse" : ""} />

        {/* 红色座椅 - 更立体 */}
        {/* 座椅底座 */}
        <rect 
          x="165" 
          y="235" 
          width="70" 
          height="35" 
          rx="17" 
          fill="url(#seatGradient)"
          stroke="rgba(139,0,0,0.8)"
          strokeWidth="2"
          filter="url(#machineGlow)"
        />
        
        {/* 座椅靠背 */}
        <rect 
          x="170" 
          y="200" 
          width="60" 
          height="40" 
          rx="30" 
          fill="url(#seatGradient)"
          stroke="rgba(139,0,0,0.8)"
          strokeWidth="2"
          filter="url(#machineGlow)"
        />
        
        {/* 座椅扶手 */}
        <rect 
          x="155" 
          y="215" 
          width="15" 
          height="35" 
          rx="7" 
          fill="url(#seatGradient)"
          stroke="rgba(139,0,0,0.8)"
          strokeWidth="1"
        />
        <rect 
          x="230" 
          y="215" 
          width="15" 
          height="35" 
          rx="7" 
          fill="url(#seatGradient)"
          stroke="rgba(139,0,0,0.8)"
          strokeWidth="1"
        />

        {/* 座椅细节 */}
        <circle cx="200" cy="220" r="8" fill="rgba(139,0,0,0.5)" />
        <rect x="185" y="235" width="30" height="3" rx="1.5" fill="rgba(255,255,255,0.2)" />

        {/* 左侧控制台 */}
        <rect 
          x="110" 
          y="245" 
          width="50" 
          height="25" 
          rx="5" 
          fill="url(#controlGradient)"
          stroke="rgba(136,136,136,0.8)"
          strokeWidth="1"
          filter="url(#machineGlow)"
        />
        
        {/* 右侧控制台 */}
        <rect 
          x="240" 
          y="245" 
          width="50" 
          height="25" 
          rx="5" 
          fill="url(#controlGradient)"
          stroke="rgba(136,136,136,0.8)"
          strokeWidth="1"
          filter="url(#machineGlow)"
        />

        {/* 主控制杆 - 增强动画 */}
        <circle 
          cx="135" 
          cy="240" 
          r="10" 
          fill={controlsActive ? "#00ff88" : "#ff6b6b"}
          stroke="#333"
          strokeWidth="2"
          filter="url(#energyFilter)"
          className={isActive ? "animate-pulse" : ""}
        />
        
        <line 
          x1="135" 
          y1="240" 
          x2="135" 
          y2="215" 
          stroke="#888"
          strokeWidth="4"
          strokeLinecap="round"
          filter="url(#machineGlow)"
        />
        
        <circle 
          cx="135" 
          cy="215" 
          r="6" 
          fill="#ffd700"
          stroke="#333"
          strokeWidth="1"
          filter="url(#energyFilter)"
          className={isActive ? "animate-bounce" : ""}
        />

        {/* 控制杆顶部发光效果 */}
        {isActive && (
          <circle 
            cx="135" 
            cy="215" 
            r="12" 
            fill="url(#energyGlow)"
            className="animate-ping"
          />
        )}

        {/* 时间设定面板 - 加大 */}
        <rect 
          x="250" 
          y="235" 
          width="30" 
          height="20" 
          rx="3" 
          fill="#000"
          stroke="#666"
          strokeWidth="1"
          filter="url(#machineGlow)"
        />
        
        {/* 数字显示屏边框 */}
        <rect 
          x="252" 
          y="237" 
          width="26" 
          height="16" 
          rx="2" 
          fill="none"
          stroke={isActive ? "#00ff00" : "#333"}
          strokeWidth="0.5"
          className={isActive ? "animate-pulse" : ""}
        />
        
        {/* 数字显示 */}
        <text 
          x="265" 
          y="248" 
          fontSize="10" 
          fill={isActive ? "#00ff00" : "#666"}
          textAnchor="middle" 
          className={`font-mono ${isActive ? "animate-pulse" : ""}`}
          filter={isActive ? "url(#energyFilter)" : ""}
        >
          {timeDisplayValue}
        </text>

        {/* 能量指示灯 - 加强效果 */}
        <circle 
          cx="85" 
          cy="250" 
          r="4" 
          fill={isActive ? "#00ff00" : "#444"}
          stroke={isActive ? "#00ff00" : "#666"}
          strokeWidth="1"
          filter="url(#energyFilter)"
          className={isActive ? "animate-ping" : ""}
        />
        <circle 
          cx="100" 
          cy="250" 
          r="4" 
          fill={isActive && energyLevel > 0.3 ? "#ffff00" : "#444"}
          stroke={isActive && energyLevel > 0.3 ? "#ffff00" : "#666"}
          strokeWidth="1"
          filter="url(#energyFilter)"
          className={isActive && energyLevel > 0.3 ? "animate-ping" : ""}
        />
        <circle 
          cx="115" 
          cy="250" 
          r="4" 
          fill={isActive && energyLevel > 0.7 ? "#ff0000" : "#444"}
          stroke={isActive && energyLevel > 0.7 ? "#ff0000" : "#666"}
          strokeWidth="1"
          filter="url(#energyFilter)"
          className={isActive && energyLevel > 0.7 ? "animate-ping" : ""}
        />

        {/* 操作按钮 - 增大 */}
        <circle 
          cx="320" 
          cy="250" 
          r="8" 
          fill={controlsActive ? "#ff4444" : "#cc2222"}
          stroke="#800000"
          strokeWidth="2"
          filter="url(#energyFilter)"
          className={isActive ? "animate-pulse" : ""}
        />
        
        <circle 
          cx="340" 
          cy="250" 
          r="8" 
          fill={controlsActive ? "#44ff44" : "#22cc22"}
          stroke="#008000"
          strokeWidth="2"
          filter="url(#energyFilter)"
          className={isActive ? "animate-pulse" : ""}
        />

        {/* 按钮内部指示 */}
        <text x="320" y="253" fontSize="6" fill="white" textAnchor="middle" className="font-bold">●</text>
        <text x="340" y="253" fontSize="6" fill="white" textAnchor="middle" className="font-bold">●</text>

        {/* 时空管道连接器 - 更大更炫 */}
        <rect 
          x="185" 
          y="175" 
          width="30" 
          height="25" 
          rx="15" 
          fill="rgba(192,192,192,0.9)"
          stroke="#888"
          strokeWidth="2"
          filter="url(#machineGlow)"
        />
        
        <circle 
          cx="200" 
          cy="187" 
          r="8" 
          fill={isActive ? "#00ffff" : "#6a6a6a"}
          stroke={isActive ? "#00ffff" : "#333"}
          strokeWidth="1"
          filter="url(#energyFilter)"
          className={isActive ? "animate-spin" : ""}
        />

        {/* 连接器光环 */}
        {isActive && (
          <circle 
            cx="200" 
            cy="187" 
            r="15" 
            fill="none"
            stroke="rgba(0,255,255,0.6)"
            strokeWidth="1"
            className="animate-ping"
          />
        )}

        {/* 能量场效果 - 更强烈 */}
        {isActive && (
          <g opacity="0.8">
            <circle 
              cx="200" 
              cy="230" 
              r="140" 
              fill="url(#glowEffect)"
              className="animate-pulse"
              style={{ animationDuration: '2s' }}
            />
            <circle 
              cx="200" 
              cy="230" 
              r="160" 
              fill="none"
              stroke="rgba(0,255,255,0.4)"
              strokeWidth="2"
              className="animate-ping"
              style={{ animationDuration: '3s' }}
            />
            <circle 
              cx="200" 
              cy="230" 
              r="180" 
              fill="none"
              stroke="rgba(0,255,255,0.2)"
              strokeWidth="1"
              className="animate-ping"
              style={{ animationDuration: '4s', animationDelay: '0.5s' }}
            />
          </g>
        )}

        {/* 时空涟漪 - 更多层次 */}
        {isActive && Array.from({ length: 6 }).map((_, i) => (
          <circle 
            key={i}
            cx="200" 
            cy="230" 
            r={100 + i * 25} 
            fill="none" 
            stroke={`rgba(0,255,255,${0.3 - i * 0.04})`}
            strokeWidth="1"
            className="animate-ping"
            style={{ 
              animationDelay: `${i * 0.3}s`,
              animationDuration: '4s'
            }}
          />
        ))}

        {/* 状态文字 - 更醒目 */}
        {isActive && (
          <>
            <text 
              x="200" 
              y="140" 
              fontSize="16" 
              fill="#00ffff" 
              textAnchor="middle" 
              className="font-mono font-bold animate-pulse"
              filter="url(#energyFilter)"
            >
              TIME MACHINE ACTIVE
            </text>
            <text 
              x="200" 
              y="155" 
              fontSize="10" 
              fill="#88ffff" 
              textAnchor="middle" 
              className="font-mono animate-pulse"
              style={{ animationDelay: '0.5s' }}
            >
              TEMPORAL COORDINATES LOCKED
            </text>
          </>
        )}

        {/* 装饰性管道 */}
        <rect x="50" y="260" width="8" height="15" rx="4" fill="#666" />
        <rect x="342" y="260" width="8" height="15" rx="4" fill="#666" />
        <circle cx="54" cy="267" r="2" fill={isActive ? "#00ff00" : "#333"} className={isActive ? "animate-pulse" : ""} />
        <circle cx="346" cy="267" r="2" fill={isActive ? "#00ff00" : "#333"} className={isActive ? "animate-pulse" : ""} />
      </svg>

      {/* 悬浮粒子效果 - 增强 */}
      {isActive && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute rounded-full"
              style={{
                width: `${2 + Math.random() * 3}px`,
                height: `${2 + Math.random() * 3}px`,
                left: `${20 + Math.random() * 60}%`,
                top: `${20 + Math.random() * 60}%`,
                background: `hsl(${180 + Math.random() * 60}, 100%, ${50 + Math.random() * 50}%)`,
                animation: `float ${1.5 + Math.random() * 3}s ease-in-out infinite`,
                animationDelay: `${i * 0.1}s`,
                filter: 'blur(0.5px)',
                boxShadow: `0 0 ${4 + Math.random() * 6}px currentColor`
              }}
            />
          ))}
        </div>
      )}

      {/* CSS 动画增强 */}
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
            opacity: 0.3;
          }
          25% {
            transform: translateY(-12px) translateX(6px) scale(1.3) rotate(90deg);
            opacity: 0.9;
          }
          50% {
            transform: translateY(-20px) translateX(-3px) scale(0.8) rotate(180deg);
            opacity: 1;
          }
          75% {
            transform: translateY(-10px) translateX(-6px) scale(1.2) rotate(270deg);
            opacity: 0.7;
          }
        }
      `}</style>
    </div>
  )
} 