'use client'
import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Button } from 'antd'
import { DownloadOutlined, AppleOutlined, WindowsOutlined } from '@ant-design/icons'

interface CyberDownloadCardProps {
  platform: 'mac' | 'windows'
  title: string
  subtitle: string
  description: string
  icon: React.ReactNode
  onDownload: () => void
  isLoading: boolean
  className?: string
}

export const CyberDownloadCard: React.FC<CyberDownloadCardProps> = ({
  platform,
  title,
  subtitle,
  description,
  icon,
  onDownload,
  isLoading,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false)
  const [mousePosition, setMousePosition] = useState({ x: 50, y: 50 })
  const cardRef = useRef<HTMLDivElement>(null)
  const animationFrameRef = useRef<number>()

  // 节流的鼠标移动处理，减少状态更新频率
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current || !isHovered) return
    
    // 取消之前的动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
    
    // 使用requestAnimationFrame节流更新
    animationFrameRef.current = requestAnimationFrame(() => {
      const rect = cardRef.current!.getBoundingClientRect()
      const x = ((e.clientX - rect.left) / rect.width) * 100
      const y = ((e.clientY - rect.top) / rect.height) * 100
      
      setMousePosition({ x: Math.min(Math.max(x, 0), 100), y: Math.min(Math.max(y, 0), 100) })
    })
  }, [isHovered])

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
    setMousePosition({ x: 50, y: 50 })
    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
    }
  }, [])

  // 清理effect
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // 预计算平台相关的样式类名，避免重复计算
  const platformStyles = {
    mac: {
      gradient: 'from-blue-600 via-cyan-500 to-purple-600',
      hoverGradient: 'from-blue-500 via-cyan-400 to-purple-500',
      hoverColor: 'text-cyan-300',
      shadowColor: 'shadow-blue-500/50',
      particleColor: 'bg-cyan-400',
      lineColor: '#00bcd4'
    },
    windows: {
      gradient: 'from-purple-600 via-pink-500 to-red-600',
      hoverGradient: 'from-purple-500 via-pink-400 to-red-500',
      hoverColor: 'text-pink-300',
      shadowColor: 'shadow-purple-500/50',
      particleColor: 'bg-pink-400',
      lineColor: '#e91e63'
    }
  }

  const currentStyle = platformStyles[platform]

  return (
    <div
      ref={cardRef}
      className={`
        relative group cursor-pointer
        ${isHovered ? 'cyber-card-hovered' : 'cyber-card-normal'}
        ${className}
      `}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        // 使用CSS变量避免inline style重复计算
        '--mouse-x': `${mousePosition.x}%`,
        '--mouse-y': `${mousePosition.y}%`,
        '--rotation-x': `${isHovered ? (mousePosition.y - 50) * 0.15 : 0}deg`,
        '--rotation-y': `${isHovered ? (mousePosition.x - 50) * 0.15 : 0}deg`,
        // 优化GPU加速
        willChange: isHovered ? 'transform' : 'auto',
        transform: 'translateZ(0)', // 强制GPU加速
      } as React.CSSProperties}
    >
      {/* 全息效果背景 - 优化blur性能 */}
      <div 
        className={`
          absolute inset-0 rounded-2xl bg-gradient-to-br ${currentStyle.gradient}
          transition-opacity duration-300 ease-out
          ${isHovered ? 'cyber-hologram-active' : 'cyber-hologram-inactive'}
        `}
        style={{
          willChange: 'opacity, transform',
          filter: 'blur(12px)', // 固定blur值，不在动画中改变
        }}
      />
      
      {/* 主卡片 */}
      <div className={`
        relative bg-black/40 border rounded-2xl p-4 lg:p-6
        overflow-hidden cyber-main-card
        ${isHovered 
          ? `border-white/40 shadow-2xl ${currentStyle.shadowColor}`
          : 'border-white/20 shadow-lg'
        }
      `}
        style={{
          // 避免使用backdrop-blur在动画过程中，性能开销太大
          backdropFilter: 'blur(20px)',
          willChange: isHovered ? 'border-color, box-shadow' : 'auto',
        }}
      >
        
        {/* 动画边框渐变 */}
        <div 
          className={`
            absolute inset-0 rounded-2xl pointer-events-none
            transition-opacity duration-300 ease-out
            ${isHovered ? 'opacity-30' : 'opacity-0'}
          `}
          style={{
            background: isHovered 
              ? `radial-gradient(circle at var(--mouse-x) var(--mouse-y), rgba(255,255,255,0.3) 0%, transparent 50%)`
              : undefined,
            willChange: 'opacity',
          }}
        />
        
        {/* 内容 */}
        <div className="relative z-10">
          {/* 图标 */}
          <div className={`
            text-4xl lg:text-5xl mb-3 flex justify-center
            transition-colors duration-300 ease-out
            ${isHovered 
              ? `${currentStyle.hoverColor} drop-shadow-lg` 
              : 'text-gray-300'
            }
          `}
            style={{ willChange: isHovered ? 'color' : 'auto' }}
          >
            {icon}
          </div>
          
          {/* 标题 */}
          <h3 className={`
            text-lg lg:text-xl font-bold mb-1 text-center
            transition-colors duration-300 ease-out
            ${isHovered ? 'text-white drop-shadow-md' : 'text-gray-200'}
          `}>
            {title}
          </h3>
          
          {/* 副标题 */}
          <p className={`
            text-xs lg:text-sm text-center mb-2
            transition-colors duration-300 ease-out
            ${isHovered ? 'text-gray-200' : 'text-gray-400'}
          `}>
            {subtitle}
          </p>
          
          {/* 描述 */}
          <p className={`
            text-xs text-center mb-3 lg:mb-4 leading-relaxed
            transition-colors duration-300 ease-out
            ${isHovered ? 'text-gray-300' : 'text-gray-500'}
          `}>
            {description}
          </p>
          
          {/* 下载按钮 */}
          <Button
            type="primary"
            size="middle"
            loading={isLoading}
            onClick={onDownload}
            icon={<DownloadOutlined />}
            className={`
              w-full border-none font-semibold text-sm lg:text-base py-3 lg:py-4 h-auto
              relative overflow-hidden cyber-download-btn
              ${isHovered 
                ? `bg-gradient-to-r ${currentStyle.hoverGradient} shadow-lg ${currentStyle.shadowColor}`
                : `bg-gradient-to-r ${currentStyle.gradient}`
              }
            `}
            style={{
              willChange: isHovered ? 'transform, box-shadow' : 'auto',
              transition: 'all 0.3s ease-out',
            }}
          >
            <span className="relative z-10">
              {isLoading ? 'Downloading...' : 'Download Now'}
            </span>
            
            {/* 按钮发光效果 */}
            {isHovered && (
              <div 
                className="absolute inset-0 rounded-md transition-opacity duration-300 ease-out"
                style={{
                  background: `radial-gradient(circle at var(--mouse-x) var(--mouse-y), rgba(255,255,255,0.2) 0%, transparent 70%)`,
                  willChange: 'opacity',
                }}
              />
            )}
          </Button>
        </div>
        
        {/* 粒子效果 - 减少数量提升性能 */}
        {isHovered && (
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 6 }).map((_, i) => (
              <div
                key={i}
                className={`
                  absolute w-1 h-1 rounded-full animate-ping opacity-60
                  ${currentStyle.particleColor}
                `}
                style={{
                  left: `${20 + (i * 12)}%`,
                  top: `${20 + (i * 10)}%`,
                  animationDelay: `${i * 0.3}s`,
                  animationDuration: '1.5s',
                  willChange: 'transform, opacity',
                }}
              />
            ))}
          </div>
        )}
        
        {/* 能量线 - 简化SVG提升性能 */}
        {isHovered && (
          <div className="absolute inset-0 pointer-events-none opacity-20">
            <svg className="w-full h-full" style={{ willChange: 'opacity' }}>
              <defs>
                <linearGradient id={`gradient-${platform}`} x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor={currentStyle.lineColor} />
                  <stop offset="100%" stopColor="transparent" />
                </linearGradient>
              </defs>
              {Array.from({ length: 3 }).map((_, i) => (
                <line
                  key={i}
                  x1={`${10 + i * 30}%`}
                  y1="0%"
                  x2={`${20 + i * 30}%`}
                  y2="100%"
                  stroke={`url(#gradient-${platform})`}
                  strokeWidth="1"
                  className="animate-pulse"
                  style={{ 
                    animationDelay: `${i * 0.4}s`,
                    animationDuration: '2s',
                  }}
                />
              ))}
            </svg>
          </div>
        )}
      </div>

      {/* 性能优化的CSS样式 */}
      <style jsx>{`
        .cyber-card-normal {
          transform: perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1) translateZ(0);
          transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          z-index: 10;
        }
        
        .cyber-card-hovered {
          transform: perspective(1000px) 
                   rotateX(var(--rotation-x)) 
                   rotateY(var(--rotation-y)) 
                   scale(1.05) translateZ(0);
          transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          z-index: 20;
        }
        
        .cyber-hologram-inactive {
          opacity: 0.2;
          transform: scale(1) translateZ(0);
        }
        
        .cyber-hologram-active {
          opacity: 0.4;
          transform: scale(1.08) translateZ(0);
        }
        
        .cyber-main-card {
          transition: border-color 0.3s ease-out, box-shadow 0.3s ease-out;
        }
        
        .cyber-download-btn:hover {
          transform: scale(1.02) translateZ(0);
        }
        
        /* 减少重绘 */
        .cyber-card-normal *,
        .cyber-card-hovered * {
          backface-visibility: hidden;
        }
      `}</style>
    </div>
  )
} 