'use client'
import React, { useState, useEffect, useRef } from 'react'
import { Button, Typography, message } from 'antd'
import { 
  DownloadOutlined, 
  <PERSON>Outlined, 
  <PERSON>Outlined,
  ArrowLeftOutlined,
  <PERSON>boltOutlined,
  <PERSON>Outlined,
  StarOutlined
} from '@ant-design/icons'
import Link from 'next/link'
import { CSSTimeTunnel } from './components/CSSTimeTunnel'
import { MouseTrail } from './components/MouseTrail'
import { CyberDownloadCard } from './components/CyberDownloadCard'
import { TimeMachine } from './components/TimeMachine'

const { Title, Text } = Typography

interface FeatureCard {
  icon: React.ReactNode
  title: string
  description: string
  color: string
}

export default function DSystemDownload() {
  const [downloadingMac, setDownloadingMac] = useState(false)
  const [downloadingWin, setDownloadingWin] = useState(false)
  const [portalActive, setPortalActive] = useState(false)
  const [floatingElements, setFloatingElements] = useState<Array<{id: number, x: number, y: number, delay: number}>>([])
  const audioContextRef = useRef<AudioContext>()

  // Initialize portal and floating elements
  useEffect(() => {
    setTimeout(() => setPortalActive(true), 1000)
    
    // Generate floating elements
    const elements = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
      delay: Math.random() * 5
    }))
    setFloatingElements(elements)

    // Initialize audio context for sound effects
    try {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
    } catch (e) {
      console.log('Audio context not supported')
    }
  }, [])

  // Sound effect helper
  const playSound = (frequency: number, duration: number) => {
    if (!audioContextRef.current) return
    
    const oscillator = audioContextRef.current.createOscillator()
    const gainNode = audioContextRef.current.createGain()
    
    oscillator.connect(gainNode)
    gainNode.connect(audioContextRef.current.destination)
    
    oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime)
    oscillator.type = 'sine'
    
    gainNode.gain.setValueAtTime(0, audioContextRef.current.currentTime)
    gainNode.gain.linearRampToValueAtTime(0.1, audioContextRef.current.currentTime + 0.01)
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + duration)
    
    oscillator.start(audioContextRef.current.currentTime)
    oscillator.stop(audioContextRef.current.currentTime + duration)
  }

  const handleDownload = async (platform: 'mac' | 'windows') => {
    // Play download sound
    playSound(800, 0.2)
    
    if (platform === 'mac') {
      setDownloadingMac(true)
      setTimeout(() => {
        setDownloadingMac(false)
        message.success({
          content: 'D System for Mac download initiated! 🚀',
          style: { backdropFilter: 'blur(10px)' }
        })
        playSound(1200, 0.3)
      }, 2000)
    } else {
      setDownloadingWin(true)
      setTimeout(() => {
        setDownloadingWin(false)
        message.success({
          content: 'D System for Windows download initiated! 🚀',
          style: { backdropFilter: 'blur(10px)' }
        })
        playSound(1200, 0.3)
      }, 2000)
    }
  }

  const features: FeatureCard[] = [
    {
      icon: <ThunderboltOutlined />,
      title: 'Quantum Speed',
      description: 'Experience lightning-fast AI responses powered by quantum processing algorithms',
      color: 'from-yellow-400 to-orange-500'
    },
    {
      icon: <RocketOutlined />,
      title: 'Neural Networks',
      description: 'Advanced machine learning that adapts to your workflow and preferences',
      color: 'from-cyan-400 to-blue-500'
    },
    {
      icon: <StarOutlined />,
      title: 'Dimensional Interface',
      description: 'Intuitive UI that transcends traditional computing boundaries',
      color: 'from-purple-400 to-pink-500'
    }
  ]

  return (
    <div className="h-screen w-full relative overflow-hidden bg-black doraemon-cursor">
      {/* CSS Time Tunnel Background - Full Screen */}
      <CSSTimeTunnel />
      
      {/* Mouse Trail Effect */}
      <MouseTrail />
      
      {/* Floating geometric elements */}
      <div className="fixed inset-0 pointer-events-none z-0">
        {floatingElements.map((element) => (
          <div
            key={element.id}
            className="absolute w-1 h-1 bg-cyan-400 opacity-30"
            style={{
              left: `${element.x}%`,
              top: `${element.y}%`,
              animation: `float ${8 + Math.random() * 4}s ease-in-out infinite`,
              animationDelay: `${element.delay}s`
            }}
          />
        ))}
      </div>

      {/* Navigation */}
      <div className="absolute top-8 left-8 z-30">
        <Link href="/d-system">
          <Button 
            type="default" 
            icon={<ArrowLeftOutlined />}
            className="bg-black/30 backdrop-blur-md border-cyan-500/50 text-cyan-300 hover:bg-cyan-500/20 hover:border-cyan-400 transition-all duration-300"
            size="large"
            onMouseEnter={() => playSound(600, 0.1)}
          >
            Back to D System
          </Button>
        </Link>
      </div>

      {/* Main Content */}
      <div className="relative z-20 h-full w-full flex items-center justify-center">
        
        {/* Centered Content Container */}
        <div className="w-full max-w-6xl px-4 py-2">
          <div className="flex flex-col items-center justify-center space-y-4">
            
            {/* Header Section */}
            <div className="text-center">
              {/* Time Machine */}
              <div className="flex items-center justify-center mb-2">
                <div className="relative scale-50 md:scale-65 lg:scale-75">
                  <TimeMachine isActive={portalActive} />
              </div>
            </div>

              {/* Title */}
              <Title 
                level={1} 
                className="text-xl md:text-3xl lg:text-4xl font-bold mb-1 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent animate-pulse"
                style={{
                  textShadow: '0 0 30px rgba(59, 130, 246, 0.8), 0 0 60px rgba(147, 51, 234, 0.6), 2px 2px 4px rgba(0, 0, 0, 0.8)',
                  fontFamily: 'monospace',
                  WebkitTextStroke: '1px rgba(59, 130, 246, 0.3)'
                }}
              >
                TIME MACHINE
              </Title>
              
              {/* Subtitle */}
              <div className="text-xs md:text-sm text-cyan-300 mb-2 font-mono">
                <div className="animate-pulse">{'> DORAEMON DIMENSIONAL PORTAL <'}</div>
              </div>
            </div>

            {/* Download Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 lg:gap-4 max-w-3xl mx-auto">
              <CyberDownloadCard
                platform="mac"
                title="macOS Edition"
                subtitle="Universal Binary"
                description="Optimized for Apple Silicon and Intel Macs with advanced neural processing."
                icon={<AppleOutlined />}
                onDownload={() => handleDownload('mac')}
                isLoading={downloadingMac}
              />
              
              <CyberDownloadCard
                platform="windows"
                title="Windows Edition"
                subtitle="64-bit Architecture"
                description="Built for Windows 10/11 with DirectX acceleration and quantum computing."
                icon={<WindowsOutlined />}
                onDownload={() => handleDownload('windows')}
                isLoading={downloadingWin}
              />
          </div>



            {/* Footer */}
            <div className="text-center mt-2">
              <div className="text-xs text-gray-500 font-mono animate-pulse">
                {'<'} POWERED BY QUANTUM COMPUTING TECHNOLOGY {'>'}
              </div>
            </div>

            </div>
        </div>
      </div>

      {/* Custom animations */}
      <style jsx global>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
          }
          25% {
            transform: translateY(-10px) rotate(90deg);
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
          }
          75% {
            transform: translateY(-10px) rotate(270deg);
          }
        }

        @keyframes glitch {
          0% {
            text-shadow: 0.05em 0 0 rgba(255, 0, 0, 0.75),
                        -0.05em -0.025em 0 rgba(0, 255, 0, 0.75),
                        -0.025em 0.05em 0 rgba(0, 0, 255, 0.75);
          }
          15% {
            text-shadow: 0.05em 0 0 rgba(255, 0, 0, 0.75),
                        -0.05em -0.025em 0 rgba(0, 255, 0, 0.75),
                        -0.025em 0.05em 0 rgba(0, 0, 255, 0.75);
          }
          16% {
            text-shadow: -0.05em -0.025em 0 rgba(255, 0, 0, 0.75),
                        0.025em 0.025em 0 rgba(0, 255, 0, 0.75),
                        -0.05em -0.05em 0 rgba(0, 0, 255, 0.75);
          }
          49% {
            text-shadow: -0.05em -0.025em 0 rgba(255, 0, 0, 0.75),
                        0.025em 0.025em 0 rgba(0, 255, 0, 0.75),
                        -0.05em -0.05em 0 rgba(0, 0, 255, 0.75);
          }
          50% {
            text-shadow: 0.025em 0.05em 0 rgba(255, 0, 0, 0.75),
                        0.05em 0 0 rgba(0, 255, 0, 0.75),
                        0 -0.05em 0 rgba(0, 0, 255, 0.75);
          }
          99% {
            text-shadow: 0.025em 0.05em 0 rgba(255, 0, 0, 0.75),
                        0.05em 0 0 rgba(0, 255, 0, 0.75),
                        0 -0.05em 0 rgba(0, 0, 255, 0.75);
          }
          100% {
            text-shadow: -0.025em 0 0 rgba(255, 0, 0, 0.75),
                        -0.025em -0.025em 0 rgba(0, 255, 0, 0.75),
                        -0.025em -0.05em 0 rgba(0, 0, 255, 0.75);
          }
        }

        .glitch {
          animation: glitch 2s infinite;
        }

        /* 哆啦A梦胳膊鼠标指针 - 经典蓝色圆柱+白色圆球设计 */
        .doraemon-cursor {
          cursor: url("data:image/svg+xml;base64,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"), auto;
        }

        /* 悬停按钮时的特殊指针 - 哆啦A梦胳膊发光效果 */
        .doraemon-cursor button:hover,
        .doraemon-cursor .ant-btn:hover,
        .doraemon-cursor [role="button"]:hover {
          cursor: url("data:image/svg+xml;base64,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"), auto;
        }

        /* 拖拽时的指针 - 哆啦A梦胳膊拖拽状态 */
        .doraemon-cursor.dragging {
          cursor: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYXJtRHJhZyIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM4QjVDRjYiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSI1MCUiIHN0b3AtY29sb3I9IiNBNzhCRkEiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjQzA4NEZDIi8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPHJhZGlhbEdyYWRpZW50IGlkPSJoYW5kRHJhZyIgY3g9IjMwJSIgY3k9IjMwJSIgcj0iNzAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0ZGRkZGRiIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNFNUU1RTUiLz4KICAgIDwvcmFkaWFsR3JhZGllbnQ+CiAgPC9kZWZzPgogIAogIDxyZWN0IHg9IjQiIHk9IjYiIHdpZHRoPSIxNCIgaGVpZ2h0PSI0IiByeD0iMiIgZmlsbD0idXJsKCNhcm1EcmFnKSIgc3Ryb2tlPSIjN0M0RERCIiBzdHJva2Utd2lkdGg9IjEiLz4KICA8ZWxsaXBzZSBjeD0iMTEiIGN5PSI4IiByeD0iNiIgcnk9IjEuNSIgZmlsbD0iI0E3OEJGQSIgb3BhY2l0eT0iMC44Ii8+CiAgCiAgPGNpcmNsZSBjeD0iMjMiIGN5PSIxOCIgcj0iNyIgZmlsbD0idXJsKCNoYW5kRHJhZykiIHN0cm9rZT0iI0JCQkJCQiIgc3Ryb2tlLXdpZHRoPSIxLjIiLz4KICA8ZWxsaXBzZSBjeD0iMjEiIGN5PSIxNiIgcng9IjMiIHJ5PSIxLjUiIGZpbGw9IiNGOEY4RjgiIG9wYWNpdHk9IjAuOSIvPgogIAogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTUiIHI9IjEuMiIgZmlsbD0iI0ZGRkZGRiIgb3BhY2l0eT0iMC45Ii8+CiAgPGNpcmNsZSBjeD0iMjUiIGN5PSIyMCIgcj0iMSIgZmlsbD0iI0ZGRkZGRiIgb3BhY2l0eT0iMC44Ii8+Cjwvc3ZnPg=="), auto;
        }
      `}</style>
    </div>
  )
} 