'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import Image from 'next/image';
import { useTheme } from './theme-context';
import AIInputBox from '../components/AIInputBox';
import AIMessage from '../components/AIMessage';
import UserMessage from '../components/UserMessage';
import MyDPalPage from './MyDPalPage';
import Live2DCharacterWidget from './components/Live2DCharacterWidget';
import UserInfoPanel from './components/UserInfoPanel';
import { getNotificationMessages } from './lib/urgentMessages';
import {
  sampleChatHistory,
  workflowTemplates,
  csAgentConsultations,
  i18nAgentConsultations,
  mockUserInfo,
  mockRelatedConversations,
  mockProjects,
  NAV_GROUPS,
  mockTasks,
  dailyReportData,
  subscriptionTasks,
  notificationData,
  csAgentChatData,
  i18nAgentChatData,
  complexChatSessions,
  knowledgeTasks
} from './data/mockData';
import { 
  Message, 
  Chat, 
  ChatHistoryGroup, 
  CSConsultation, 
  ComplexSession 
} from './types';
import { 
  getUserRankLevel, 
  getTypeColor, 
  getTypeIcon, 
  filterCSConsultations, 
  sortNotificationsByPriority, 
  formatFileSize 
} from './utils';
import { 
  PAGE_STYLES, 
  DEFAULT_CHAT_HISTORY, 
  getThemeColors, 
  DEFAULT_AVATAR_CONFIG, 
  BREAKPOINTS, 
  ANIMATION_DURATION, 
  MENU_POSITION 
} from './constants';
import Sidebar from './components/Sidebar';
import NotificationWidget from './components/Notification';
import ChatInterface from './components/Chat';
import SystemProjectInterface from './components/SystemProject';
import ProjectManager from './components/Project';
import HomeInterface from './components/Home';
import Knowledge from './components/Knowledge';
// 页面样式常量已迁移到 ./constants/index.ts

// Mock数据已迁移到 ./data/mockData.ts

// 工具函数已迁移到 ./utils/index.ts

// Mock数据已迁移到 ./data/mockData.ts，工具函数已迁移到 ./utils/index.ts

// 通知排序函数已迁移到 ./utils/index.ts

// CS Agent 聊天数据和复杂会话数据已迁移到 ./data/mockData.ts

// Icon component to replace Ant Design icons
function Icon({ name, className = "" }: { name: string, className?: string }) {
  switch (name) {
    case 'home':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path></svg>;
    case 'folder':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>;
    case 'book':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path></svg>;
    case 'search':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd"></path></svg>;
    case 'calendar':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 24" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path></svg>;
    case 'team':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg>;
    case 'file':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd"></path></svg>;
    case 'send':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path></svg>;
    case 'robot':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd"></path></svg>;
    case 'user':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path></svg>;
    case 'attachment':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clipRule="evenodd"></path></svg>;
    case 'picture':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"></path></svg>;
    case 'close':
      return <svg className={`w-5 h-5 ${className}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path></svg>;
    default:
      return null;
  }
}

// 图标组件
function SunIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z" />
    </svg>
  );
}

function MoonIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
    </svg>
  );
}

function HomeIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
    </svg>
  );
}

function FolderIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
    </svg>
  );
}

function ChartIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
    </svg>
  );
}

function SendIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1.25rem', height: '1.25rem'}}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
    </svg>
  );
}

// 聊天历史数据常量已迁移到 ./constants/index.ts

// 创建一个客户端组件包装器
export default function DSystem() {
  return <DSystemClient />;
}

// Interface定义已迁移到 ./types/index.ts

// 实际的客户端组件
function DSystemClient() {
  // 使用状态钩子
  const [activeNav, setActiveNav] = useState('home');
  const [inputValue, setInputValue] = useState('');
  // 移除搜索框相关状态
  const [mounted, setMounted] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showMyD_Pal, setShowMyD_Pal] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  const [isMouseOverMenu, setIsMouseOverMenu] = useState(false);
  const [isMouseOverProfile, setIsMouseOverProfile] = useState(false);
  const [expandedProject, setExpandedProject] = useState<string | null>(null);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  // 添加选中项目状态
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  // 添加项目提示词状态
  const [projectPrompt, setProjectPrompt] = useState<string>("");
  // 添加项目新消息状态
  const [projectMessage, setProjectMessage] = useState<string>("");
  // 添加当前项目的会话列表状态
  const [projectSessions, setProjectSessions] = useState<Array<{id: string, name: string, createdAt: string}>>([]);
  // 添加新的状态钩子
  const [activeNotification, setActiveNotification] = useState<string | null>(null);
  const [complexSessions, setComplexSessions] = useState(complexChatSessions);
  // 添加聊天历史状态
  const [chatHistoryByDate, setChatHistoryByDate] = useState<ChatHistoryGroup[]>(DEFAULT_CHAT_HISTORY);
  // 添加项目导航展开状态
  const [projectNavExpanded, setProjectNavExpanded] = useState(false);
  // 添加项目菜单相关状态
  const [activeProjectMenu, setActiveProjectMenu] = useState<string | null>(null);
  const [projectMenuPosition, setProjectMenuPosition] = useState({ top: 0, left: 0 });
  
  // Doraemon头像配置状态
  const [dpalsAvatar, setDpalsAvatar] = useState(DEFAULT_AVATAR_CONFIG);
  
  // 通知状态管理
  const [notifications, setNotifications] = useState(notificationData);
  
  // 删除通知函数
  const handleDeleteNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
  };
  
  // 新增状态管理悬浮菜单显示
  const [hoverChatId, setHoverChatId] = useState<string | null>(null);
  const [showChatMenu, setShowChatMenu] = useState<string | null>(null);
  const [showProjectSubmenu, setShowProjectSubmenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  // 添加项目hover状态
  const [hoverProjectId, setHoverProjectId] = useState<string | null>(null);
  // 移动端适配状态
  const [isMobile, setIsMobile] = useState(false);
  // Knowledge相关状态
  const [showKnowledge, setShowKnowledge] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  // CS Agent 项目相关状态

  // 用户信息抽屉状态（移动端）
  const [userInfoVisible, setUserInfoVisible] = useState(false);
  // 项目配置展开状态 - 移动端默认收起
  const [projectConfigExpanded, setProjectConfigExpanded] = useState(false);
  
  // 检测移动端并设置初始状态
  useEffect(() => {
    if (isMobile) {
      setProjectConfigExpanded(false);
    } else {
      setProjectConfigExpanded(true);
    }
  }, [isMobile]);

  // 渲染项目图标
  const renderProjectIcon = (project: any) => {
    if (project.id === 'cs-agent') {
      return (
        <div style={{
          width: '24px',
          height: '24px',
          backgroundColor: '#52c41a',
          borderRadius: '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '0.5rem',
          color: 'white'
        }}>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '14px', height: '14px'}}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
            <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6" />
            <path strokeLinecap="round" strokeLinejoin="round" d="M15 9l3 3-3 3" />
          </svg>
        </div>
      );
    }
    return (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '1rem', height: '1rem', marginRight: '0.5rem'}}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
      </svg>
    );
  };
  
  // 点击页面其他区域关闭用户菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  

  

  
  // 主题相关
  let theme: 'dark' | 'light' = 'dark';
  let themeMode: 'light' | 'dark' | 'system' = 'system';
  let toggleTheme = () => {};
  let setThemeMode = (mode: 'light' | 'dark' | 'system') => {};
  
  try {
    // 尝试使用主题钩子，但只在客户端
    const themeContext = useTheme();
    theme = themeContext.theme;
    themeMode = themeContext.themeMode;
    toggleTheme = themeContext.toggleTheme;
    setThemeMode = themeContext.setThemeMode;
  } catch (e) {
    console.error('Theme context not available');
  }
  
  // 移动端检测
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= BREAKPOINTS.MOBILE);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    setMounted(true);
    
    // 初始化一条丰富的AI欢迎消息
    setMessages([
      {
        id: Date.now(),
        type: 'ai',
        content: [
          { 
            type: 'text', 
            content: `Good morning, David! Welcome back to Doraemon. Here's your daily briefing for ${new Date().toLocaleDateString('en-US', {weekday: 'long', month: 'long', day: 'numeric'})}.` 
          },
          {
            type: 'text',
            content: '**Today\'s Priority Tasks:**'
          },
          {
            type: 'text',
            content: '1. 📌 Submit Q3 marketing campaign proposal (Due: 3:00 PM)\n2. 📅 Prepare for product team meeting (2:00 PM - 3:30 PM)\n3. 📝 Review design updates for mobile app (High priority)'
          },
          {
            type: 'text',
            content: '**Upcoming Meetings:**'
          },
          {
            type: 'text',
            content: '• 10:30 AM - Weekly team standup (Meeting Room A)\n• 2:00 PM - Product strategy discussion (Virtual)\n• 4:30 PM - Client call with TechSolutions Inc.'
          },
          {
            type: 'text',
            content: '**Recommendations:**'
          },
          {
            type: 'text',
            content: 'Based on your recent activities, you might want to:\n1. Follow up on the feedback for your latest project proposal\n2. Review the analytics dashboard for Q2 marketing results\n3. Allocate time to finalize the content strategy document'
          },
          {
            type: 'text',
            content: 'Is there anything specific you would like to work on today?'
          }
        ],
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      }
    ]);
  }, []);



  // 根据鼠标状态决定是否显示菜单
  useEffect(() => {
    if (isMouseOverMenu || isMouseOverProfile) {
      setShowUserMenu(true);
    } else {
      const timer = setTimeout(() => {
        setShowUserMenu(false);
      }, ANIMATION_DURATION.FAST); // 添加短暂延迟，避免鼠标移动过程中菜单闪烁
      return () => clearTimeout(timer);
    }
  }, [isMouseOverMenu, isMouseOverProfile]);



  // 格式化文件大小函数已迁移到 ./utils/index.ts
  
  // 处理发送消息 - 简化版本，主要处理逻辑已移到ChatInterface组件
  const handleSendMessage = (message: string, files: File[]) => {
    if (!message.trim() && files.length === 0) return;

    // 添加用户消息
    const attachments = files.map(file => ({
      type: file.type.startsWith('image/') ? 'image' as const : 'file' as const,
      url: URL.createObjectURL(file),
      name: file.name,
      size: formatFileSize(file.size)
    }));

    const userMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: message,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      attachments
    };

    setMessages(prev => [...prev, userMessage]);
    
    // 切换到聊天视图
    setSelectedChat('new-chat');
  };


  
  // 如果没有挂载，显示骨架屏
  if (!mounted) {
    return (
      <div style={{ 
        display: 'flex', 
        minHeight: '100vh', 
        backgroundColor: '#0F172A', 
        color: '#F9FAFB'
      }}>
        <div style={{ 
          width: '256px', 
          backgroundColor: '#1E293B', 
          borderRight: '1px solid #334155' 
        }}></div>
        <div style={{ flex: 1, padding: '2rem' }}>
          <div style={{ height: '36px', width: '200px', backgroundColor: '#334155', borderRadius: '8px', marginBottom: '2rem' }}></div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '1.5rem' }}>
            {[1, 2, 3].map(i => (
              <div key={i} style={{ height: '100px', backgroundColor: '#1E293B', borderRadius: '12px' }}></div>
            ))}
                  </div>
      </div>

    </div>
  );
}
  
  // 使用主题颜色生成函数
  const colors = getThemeColors(theme);
  
  // 渲染Doraemon头像为Base64 URL
  const renderD_PalAvatar = (avatarOptions: any, colors: any) => {
    // 创建一个Canvas元素来绘制头像
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return '';
    
    // 设置背景颜色 (根据颜色选项)
    const bgColor = avatarOptions.color === 'blue' ? '#1A94E6' : 
                    avatarOptions.color === 'pink' ? '#FF7EB6' : 
                    avatarOptions.color === 'green' ? '#4CAF50' : 
                    avatarOptions.color === 'yellow' ? '#FFC107' : 
                    avatarOptions.color === 'purple' ? '#9C27B0' : '#E53935';
    
    // 绘制圆形背景
    ctx.fillStyle = bgColor;
    ctx.beginPath();
    ctx.arc(100, 100, 100, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制白色腹部
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.ellipse(100, 150, 70, 50, 0, 0, Math.PI, true);
    ctx.fill();
    
    // 绘制图案 (如果不是classic)
    if (avatarOptions.pattern !== 'classic') {
      ctx.save();
      ctx.globalAlpha = 0.2;
      ctx.beginPath();
      ctx.arc(100, 100, 99, 0, Math.PI * 2);
      ctx.clip();
      
      if (avatarOptions.pattern === 'striped') {
        // 斜条纹
        for (let i = -200; i < 400; i += 20) {
          ctx.beginPath();
          ctx.moveTo(i, 0);
          ctx.lineTo(i + 200, 200);
          ctx.lineWidth = 10;
          ctx.strokeStyle = 'black';
          ctx.stroke();
        }
      } else if (avatarOptions.pattern === 'dots') {
        // 圆点
        for (let x = 0; x < 200; x += 20) {
          for (let y = 0; y < 200; y += 20) {
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();
          }
        }
      } else if (avatarOptions.pattern === 'stars') {
        // 简化的星星 (小圆点代替)
        for (let x = 10; x < 200; x += 30) {
          for (let y = 10; y < 200; y += 30) {
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();
          }
        }
      } else if (avatarOptions.pattern === 'hearts') {
        // 简化的心形 (小圆点代替)
        for (let x = 10; x < 200; x += 40) {
          for (let y = 10; y < 200; y += 40) {
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();
          }
        }
      }
      
      ctx.restore();
    }
    
    // 绘制眼睛
    ctx.fillStyle = 'white';
    // 左眼
    ctx.beginPath();
    if (avatarOptions.eyes === 'closed') {
      // 闭眼只画一条线
      ctx.fillRect(60, 80, 20, 2);
    } else {
      // 正常眼睛是圆形
      ctx.arc(70, 80, 15, 0, Math.PI * 2);
      ctx.fill();
      
      // 眼球
      ctx.fillStyle = 'black';
      ctx.beginPath();
      
      let eyeXOffset = 0;
      let eyeYOffset = 0;
      
      if (avatarOptions.eyes === 'sleepy') {
        eyeYOffset = 5;
      } else if (avatarOptions.eyes === 'angry') {
        eyeYOffset = -3;
      } else if (avatarOptions.eyes === 'excited') {
        eyeXOffset = 3;
      }
      
      ctx.arc(70 + eyeXOffset, 80 + eyeYOffset, 7, 0, Math.PI * 2);
      ctx.fill();
    }
    
    // 右眼
    ctx.fillStyle = 'white';
    ctx.beginPath();
    if (avatarOptions.eyes === 'closed') {
      // 闭眼只画一条线
      ctx.fillRect(120, 80, 20, 2);
    } else {
      // 正常眼睛是圆形
      ctx.arc(130, 80, 15, 0, Math.PI * 2);
      ctx.fill();
      
      // 眼球
      ctx.fillStyle = 'black';
      ctx.beginPath();
      
      let eyeXOffset = 0;
      let eyeYOffset = 0;
      
      if (avatarOptions.eyes === 'sleepy') {
        eyeYOffset = 5;
      } else if (avatarOptions.eyes === 'angry') {
        eyeYOffset = -3;
      } else if (avatarOptions.eyes === 'excited') {
        eyeXOffset = -3;
      }
      
      ctx.arc(130 + eyeXOffset, 80 + eyeYOffset, 7, 0, Math.PI * 2);
      ctx.fill();
    }
    
    // 绘制鼻子
    ctx.fillStyle = 'red';
    ctx.beginPath();
    ctx.arc(100, 110, 10, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制嘴巴
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 2;
    
    if (avatarOptions.mouth === 'smile') {
      ctx.beginPath();
      ctx.arc(100, 130, 30, 0, Math.PI);
      ctx.stroke();
    } else if (avatarOptions.mouth === 'laugh') {
      ctx.beginPath();
      ctx.arc(100, 130, 30, 0, Math.PI);
      ctx.stroke();
      // 填充笑嘴
      ctx.fillStyle = 'rgba(0,0,0,0.1)';
      ctx.fill();
    } else if (avatarOptions.mouth === 'surprised') {
      ctx.beginPath();
      ctx.arc(100, 140, 15, 0, Math.PI * 2);
      ctx.stroke();
    } else if (avatarOptions.mouth === 'neutral') {
      ctx.beginPath();
      ctx.moveTo(70, 140);
      ctx.lineTo(130, 140);
      ctx.stroke();
    } else if (avatarOptions.mouth === 'sad') {
      ctx.beginPath();
      ctx.arc(100, 170, 30, Math.PI, Math.PI * 2);
      ctx.stroke();
    }
    
    // 绘制胡须
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 2;
    
    // 左上胡须
    ctx.beginPath();
    ctx.moveTo(65, 120);
    ctx.lineTo(15, 110);
    ctx.stroke();
    
    // 左中胡须
    ctx.beginPath();
    ctx.moveTo(65, 130);
    ctx.lineTo(15, 130);
    ctx.stroke();
    
    // 左下胡须
    ctx.beginPath();
    ctx.moveTo(65, 140);
    ctx.lineTo(15, 150);
    ctx.stroke();
    
    // 右上胡须
    ctx.beginPath();
    ctx.moveTo(135, 120);
    ctx.lineTo(185, 110);
    ctx.stroke();
    
    // 右中胡须
    ctx.beginPath();
    ctx.moveTo(135, 130);
    ctx.lineTo(185, 130);
    ctx.stroke();
    
    // 右下胡须
    ctx.beginPath();
    ctx.moveTo(135, 140);
    ctx.lineTo(185, 150);
    ctx.stroke();
    
    // 项链/铃铛
    if (avatarOptions.necklace !== 'none') {
      if (avatarOptions.necklace === 'bell') {
        // 红色项圈
        ctx.fillStyle = 'red';
        ctx.fillRect(65, 165, 70, 8);
        
        // 铃铛
        ctx.fillStyle = 'gold';
        ctx.beginPath();
        ctx.arc(100, 180, 12, 0, Math.PI * 2);
        ctx.fill();
        
        // 铃铛中的黑点
        ctx.fillStyle = 'black';
        ctx.beginPath();
        ctx.arc(100, 185, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // 铃铛底部线条
        ctx.beginPath();
        ctx.moveTo(90, 190);
        ctx.lineTo(110, 190);
        ctx.stroke();
      } else if (avatarOptions.necklace === 'bowtie') {
        // 蝴蝶结 (简化版)
        ctx.fillStyle = 'red';
        ctx.save();
        ctx.translate(85, 170);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-10, -10, 20, 20);
        ctx.restore();
        
        ctx.save();
        ctx.translate(115, 170);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-10, -10, 20, 20);
        ctx.restore();
        
        ctx.fillStyle = 'darkred';
        ctx.beginPath();
        ctx.arc(100, 170, 5, 0, Math.PI * 2);
        ctx.fill();
      } else if (avatarOptions.necklace === 'scarf') {
        // 围巾 (简化版)
        ctx.fillStyle = 'red';
        ctx.fillRect(60, 165, 80, 15);
        
        // 围巾垂下部分
        ctx.beginPath();
        ctx.moveTo(120, 180);
        ctx.lineTo(140, 200);
        ctx.lineTo(130, 200);
        ctx.lineTo(120, 180);
        ctx.fill();
      } else if (avatarOptions.necklace === 'pendant') {
        // 项链
        ctx.strokeStyle = 'silver';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.ellipse(100, 165, 35, 10, 0, 0, Math.PI * 2);
        ctx.stroke();
        
        // 挂坠
        ctx.fillStyle = 'gold';
        ctx.fillRect(90, 175, 20, 25);
        ctx.strokeStyle = 'darkgoldenrod';
        ctx.strokeRect(90, 175, 20, 25);
      }
    }
    
    // 头饰
    if (avatarOptions.headgear !== 'default') {
      if (avatarOptions.headgear === 'hat') {
        // 帽子
        ctx.fillStyle = 'red';
        ctx.beginPath();
        ctx.ellipse(100, 30, 60, 30, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // 帽檐
        ctx.fillStyle = 'darkred';
        ctx.fillRect(50, 45, 100, 6);
      } else if (avatarOptions.headgear === 'bow') {
        // 蝴蝶结 (在头上)
        ctx.fillStyle = 'red';
        ctx.save();
        ctx.translate(75, 30);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-12, -12, 24, 24);
        ctx.restore();
        
        ctx.save();
        ctx.translate(125, 30);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-12, -12, 24, 24);
        ctx.restore();
        
        ctx.fillStyle = 'darkred';
        ctx.beginPath();
        ctx.arc(100, 30, 7, 0, Math.PI * 2);
        ctx.fill();
      } else if (avatarOptions.headgear === 'crown') {
        // 皇冠
        ctx.fillStyle = 'gold';
        // 皇冠基部
        ctx.fillRect(60, 20, 80, 20);
        
        // 皇冠的尖
        ctx.beginPath();
        ctx.moveTo(70, 20);
        ctx.lineTo(70, 0);
        ctx.lineTo(80, 20);
        ctx.fill();
        
        ctx.beginPath();
        ctx.moveTo(90, 20);
        ctx.lineTo(90, 0);
        ctx.lineTo(100, 20);
        ctx.fill();
        
        ctx.beginPath();
        ctx.moveTo(110, 20);
        ctx.lineTo(110, 0);
        ctx.lineTo(120, 20);
        ctx.fill();
      } else if (avatarOptions.headgear === 'headphones') {
        // 耳机
        ctx.fillStyle = 'black';
        
        // 头带
        ctx.fillRect(40, 10, 120, 8);
        
        // 左耳罩
        ctx.fillRect(30, 10, 15, 60);
        
        // 右耳罩
        ctx.fillRect(155, 10, 15, 60);
      }
    }
    
    // 返回Canvas的base64图像数据
    return canvas.toDataURL('image/png');
  };

  // 处理关闭MyD-Pal页面，并保存头像配置
  const handleCloseMyD_Pal = (newAvatarOptions?: any) => {
    if (newAvatarOptions) {
      // 如果传入了新的头像配置，则更新状态
      setDpalsAvatar(newAvatarOptions);
    }
    setShowMyD_Pal(false);
  };
  
  // 如果显示MyD-Pal页面，直接渲染它
  if (showMyD_Pal) {
    return <MyDPalPage theme={theme} colors={colors} onClose={handleCloseMyD_Pal} initialAvatar={dpalsAvatar} />;
  }
  

  
  // NewChat按钮点击处理函数
  const handleNewChatClick = () => {
    setActiveNav('home');
    // 当点击newchat导航按钮时，重置selectedChat状态
    setSelectedChat(null);
    // 移动端自动关闭抽屉
    if (isMobile) {
      setSidebarVisible(false);
    }
  };

  // 创建项目处理函数
  const handleCreateProject = () => {
    // 这里可以添加创建项目的逻辑
    alert('创建新项目');
  };

  // 项目会话发送处理函数
  const handleProjectSessionSend = (message: string, files: File[]) => {
    // This function is passed to ProjectManager and doesn't need implementation here
    // ProjectDetail component handles this internally
  };

  // 项目悬停处理函数
  const handleProjectHover = (projectId: string) => {
    setHoverProjectId(projectId);
  };

  // 项目离开处理函数
  const handleProjectLeave = (projectId: string | null) => {
    setHoverProjectId(projectId);
  };

  // 项目菜单点击处理函数
  const handleProjectMenuClick = (projectId: string, rect: DOMRect) => {
    if (activeProjectMenu === projectId) {
      setActiveProjectMenu(null);
        } else {
      setProjectMenuPosition({
        top: rect.top,
        left: rect.right + 5
      });
      setActiveProjectMenu(projectId);
    }
  };

  // 项目菜单关闭处理函数
  const handleProjectMenuClose = () => {
    setActiveProjectMenu(null);
  };
  
  // 处理会话点击事件
  // 处理返回到项目详情的函数
  const handleBackToProject = () => {
    setSelectedChat(null);
    
    // 如果当前有选中的项目，确保切换到项目页面
    if (selectedProject) {
      setActiveNav('projects');
    }
  };
  
  const handleChatClick = (chatId: string, projectId?: string) => {
    setSelectedChat(chatId);
    
    // 如果提供了项目ID，保持项目选中状态
    if (projectId) {
      setSelectedProject(projectId);
    } else {
      // 清除项目选中状态（仅当不是从项目中选择会话时）
      setSelectedProject(null);
    }
    
    // 判断是否为 Expert Agent 会话 (CS 或 i18n)
    if (projectId === 'cs-agent' && csAgentChatData[chatId]) {
      // 如果是 CS Agent 会话，使用 CS Agent 聊天数据
      setMessages(csAgentChatData[chatId]);
    } else if (projectId === 'i18n-agent' && i18nAgentChatData[chatId]) {
      // 如果是 i18n Agent 会话，使用 i18n Agent 聊天数据
      setMessages(i18nAgentChatData[chatId]);
    } else {
      // 判断是否为复杂会话
      const complexSession = complexSessions.find(session => session.id === chatId);
      if (complexSession) {
        // 如果是复杂会话，使用其消息
        setMessages(complexSession.messages);
      } else {
        // 否则使用普通的聊天历史
        setMessages(sampleChatHistory);
      }
    }
    
    // 对于 Expert Agent 项目，保持在项目页面；其他情况切换到首页
    if (projectId === 'cs-agent' || projectId === 'i18n-agent') {
      if (activeNav !== 'projects') {
        setActiveNav('projects');
      }
    } else {
      if (activeNav !== 'home') {
        setActiveNav('home');
      }
    }
    
    // 移动端自动关闭抽屉
    if (isMobile) {
      setSidebarVisible(false);
    }
    
    // 清除新消息标记
    let foundChat: Chat | null = null;
    let foundGroupIndex = -1;
    let foundChatIndex = -1;
    
    for (let i = 0; i < chatHistoryByDate.length; i++) {
      const chatIndex = chatHistoryByDate[i].chats.findIndex(chat => chat.id === chatId);
      if (chatIndex !== -1) {
        foundChat = chatHistoryByDate[i].chats[chatIndex];
        foundGroupIndex = i;
        foundChatIndex = chatIndex;
        break;
      }
    }
    
    if (foundChat && foundChat.hasNewMessage) {
      // 复制聊天历史数据
      const updatedChatHistory: ChatHistoryGroup[] = [...chatHistoryByDate];
      
      // 只更新hasNewMessage标记，不改变位置
      updatedChatHistory[foundGroupIndex].chats[foundChatIndex] = {
        ...foundChat,
        hasNewMessage: false // 当用户查看后，清除"新消息"标记
      };
      
      // 更新状态
      setChatHistoryByDate(updatedChatHistory);
    }
  };
  
  // 我们不需要这个额外的useEffect，因为在前面已经定义了类似的函数
  
  // 新增：处理项目点击事件，显示项目详情页
  const handleProjectClick = (projectId: string) => {
    setSelectedProject(projectId);
    setActiveNav('projects');
    
    // 查找当前项目
    const currentProject = mockProjects.find(p => p.id === projectId);
    if (currentProject) {
      // 设置项目会话
      setProjectSessions(currentProject.sessions.map(s => ({
        id: s.id,
        name: s.name,
        createdAt: new Date().toISOString().slice(0, 10) // 模拟创建日期
      })));
      
      // 清空项目提示词和消息
      setProjectPrompt("");
      setProjectMessage("");
    }
    
    // 重置会话选择
    setSelectedChat(null);
    
    // 移动端自动关闭抽屉
    if (isMobile) {
      setSidebarVisible(false);
    }
  };

  // 处理Knowledge按钮点击
  const handleKnowledgeClick = () => {
    setShowKnowledge(true);
    setActiveNav('knowledge');
  };

  // 处理从Knowledge返回CS Agent
  const handleBackToCSAgent = () => {
    setShowKnowledge(false);
    setActiveNav('projects');
    setSelectedProject('cs-agent');
  };

  // 处理通知卡片点击展开/收起
  const handleNotificationCardClick = (notificationId: string) => {
    // 如果当前卡片已经是激活状态，则关闭它
    if (activeNotification === notificationId) {
      setActiveNotification(null);
    } else {
      // 只打开当前点击的卡片
      setActiveNotification(notificationId);
    }
  };

  // 处理通知开始聊天
  const handleNotificationStartChat = (notification: any, typeInfo: any) => {
    // 检查是否已存在该通知对应的会话
    const existingSessionId = `chat-${notification.id}`;
    const existingSession = complexSessions.find(session => session.id === existingSessionId);
    
    if (existingSession) {
      // 如果已存在会话，直接激活该会话
      handleChatClick(existingSessionId);
    } else {
      // 创建一个新的聊天会话
      const newSession = {
        id: existingSessionId,
        title: notification.title,
        messages: [
          {
            id: Date.now(),
            type: 'ai' as const,
            content: [
              { 
                type: 'text', 
                content: `I'm here to help you with this ${typeInfo.title}: "${notification.title}"`
              },
              { 
                type: 'text', 
                content: `How would you like to proceed with handling this ${notification.type.replace('-', ' ')}?`
              }
            ],
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          }
        ]
      };
      
      // 添加到复杂会话中
      setComplexSessions(prev => [newSession, ...prev]);
      
      // 将会话添加到历史记录中，但不调整顺序
      let existingChatIndex = -1;
      let existingGroupIndex = -1;
      
      // 检查会话是否已存在于历史记录中
      for (let i = 0; i < chatHistoryByDate.length; i++) {
        const chatIndex = chatHistoryByDate[i].chats.findIndex(chat => chat.id === existingSessionId);
        if (chatIndex !== -1) {
          existingChatIndex = chatIndex;
          existingGroupIndex = i;
          break;
        }
      }
      
      // 更新聊天历史状态
      const updatedChatHistory = [...chatHistoryByDate];
      
      if (existingGroupIndex !== -1 && existingChatIndex !== -1) {
        // 如果会话已存在，只更新hasNewMessage属性
        updatedChatHistory[existingGroupIndex].chats[existingChatIndex].hasNewMessage = true;
      } else {
        // 如果会话不存在，添加到今天分组中，但不放在第一位
        updatedChatHistory[0].chats.push({
          id: existingSessionId,
          title: notification.title,
          hasNewMessage: true
        });
      }
      
      setChatHistoryByDate(updatedChatHistory);
      
      // 切换到聊天界面
      handleChatClick(newSession.id);
    }
  };

  // 处理通知点击 - 进入对应会话
  const handleNotificationClick = (notification: any) => {
    // 根据通知类型创建或导航到对应的会话
    const chatId = `chat-${notification.id}`;
    
    // 如果是任务类型，可以跳转到CS系统
    if (notification.type === 'task' && notification.content.caseNumber) {
      // 这里可以添加跳转到CS系统的逻辑
      window.open('/cs-system', '_blank');
      return;
    }
    
    // 创建新的会话消息
    const newMessage: Message = {
      id: Date.now(),
      type: 'ai',
      content: [
        {
          type: 'text',
          content: `I see you clicked on "${notification.title}". Let me help you with this.`
        },
        {
          type: 'text', 
          content: notification.type === 'schedule' 
            ? `This is a ${notification.content.eventType} scheduled for ${notification.content.startTime} in ${notification.content.location}.`
            : notification.type === 'auto-plan'
            ? `This is an automated plan with ${notification.content.completedTasks || 0} completed tasks.`
            : notification.type === 'reminder'
            ? `This is a ${notification.content.taskType} from ${notification.content.department} department.`
            : `This is a ${notification.content.alertType || notification.content.issueType} that requires attention.`
        }
      ],
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };

    // 清空当前消息并添加新消息
    setMessages([newMessage]);
    setSelectedChat(chatId);
    setActiveNav('home');
    
    // 移动端自动关闭抽屉
    if (isMobile) {
      setSidebarVisible(false);
    }
  };
  
  return (
    <div style={{ 
      display: 'flex', 
      minHeight: '100vh', 
      width: '100%', 
      backgroundColor: colors.bgMain,
      color: colors.textPrimary,
      transition: 'background-color 0.3s ease, color 0.3s ease',
      position: 'relative'
    }}>
      {/* 移动端遮罩层 */}
      {isMobile && sidebarVisible && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 998,
          }}
          onClick={() => setSidebarVisible(false)}
        />
      )}
      
      {/* 侧边栏容器组件 */}
      <Sidebar
        colors={colors}
        theme={theme}
        themeMode={themeMode}
        isMobile={isMobile}
        sidebarVisible={sidebarVisible}
        activeNav={activeNav}
        selectedChat={selectedChat}
        selectedProject={selectedProject}
        chatHistoryByDate={chatHistoryByDate}
        hoverChatId={hoverChatId}
        showChatMenu={showChatMenu}
        showProjectSubmenu={showProjectSubmenu}
        menuPosition={menuPosition}
        mockProjects={mockProjects}
        expandedProject={expandedProject}
        hoverProjectId={hoverProjectId}
        activeProjectMenu={activeProjectMenu}
        projectMenuPosition={projectMenuPosition}
                    csAgentConsultations={selectedProject === 'i18n-agent' ? i18nAgentConsultations : csAgentConsultations}
        showUserMenu={showUserMenu}
        isMouseOverProfile={isMouseOverProfile}
        isMouseOverMenu={isMouseOverMenu}
        onNewChatClick={handleNewChatClick}
        onCreateProject={handleCreateProject}
        onProjectClick={handleProjectClick}
        onProjectHover={handleProjectHover}
        onProjectLeave={handleProjectLeave}
        onProjectMenuClick={handleProjectMenuClick}
        onChatClick={handleChatClick}
        onChatHover={(chatId: string) => setHoverChatId(chatId)}
        onChatLeave={() => setHoverChatId(null)}
        onMenuClick={(chatId: string, position: { top: number; left: number }) => {
          setMenuPosition(position);
          setShowChatMenu(chatId);
                          setShowProjectSubmenu(false);
                        }}
        onMenuClose={() => setShowChatMenu(null)}
        onProjectSubmenuToggle={setShowProjectSubmenu}
        onDeleteChat={(chatId: string) => {
          console.log('删除聊天', chatId);
        }}
        onAddChatToProject={(chatId: string, projectId: string) => {
          console.log('添加聊天到项目', chatId, projectId);
        }}
        onProfileMouseEnter={() => setIsMouseOverProfile(true)}
        onProfileMouseLeave={() => setIsMouseOverProfile(false)}
        onMenuMouseEnter={() => setIsMouseOverMenu(true)}
        onMenuMouseLeave={() => setIsMouseOverMenu(false)}
        onThemeModeChange={setThemeMode}
        onMyDPalClick={() => {
                    setShowUserMenu(false);
                    setShowMyD_Pal(true);
                  }}
        onProjectMenuClose={handleProjectMenuClose}
        renderProjectIcon={renderProjectIcon}
      />
      
      {/* 主内容区 */}
      <div style={{ 
        flex: 1, 
        backgroundColor: colors.bgMain,
        transition: 'background-color 0.3s ease',
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        maxHeight: '100vh',
        overflow: 'hidden',
        marginLeft: isMobile ? '0' : '0',
      }}>
        {/* 移动端顶部栏 */}
        {isMobile && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '1rem 1.5rem',
            backgroundColor: colors.bgCard,
            borderBottom: `1px solid ${colors.borderSubtle}`,
            position: 'sticky',
            top: 0,
            zIndex: 100,
          }}>
            {/* 汉堡菜单按钮 */}
            <button
              onClick={() => setSidebarVisible(!sidebarVisible)}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                backgroundColor: 'transparent',
                border: 'none',
                borderRadius: '8px',
                color: colors.textPrimary,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.bgSubtle;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '24px', height: '24px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
              </svg>
            </button>
            
            {/* 移动端Logo */}
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ 
                width: '2rem', 
                height: '2rem', 
                borderRadius: '0.375rem', 
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.accent} 100%)`,
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                marginRight: '0.5rem' 
              }}>
                <span style={{ color: 'white', fontWeight: 'bold', fontSize: '1rem' }}>D</span>
              </div>
              <div>
                <div style={{ fontWeight: 'bold', fontSize: '1rem', color: colors.textPrimary }}>Doraemon</div>
              </div>
            </div>
            
            {/* 占位元素，保持平衡 */}
            <div style={{ width: '40px' }}></div>
          </div>
        )}
        
        {/* 内容区 - 不同的页面内容 */}
        {activeNav === 'home' ? (
          <div style={{ 
            flex: 1, 
            display: 'flex', 
            flexDirection: 'column',
            padding: isMobile ? '1rem' : '1.5rem',
            height: isMobile ? 'calc(100vh - 72px)' : '100vh',
            maxHeight: isMobile ? 'calc(100vh - 72px)' : '100vh',
            overflow: 'hidden'
          }}>
            {/* Home Interface Component */}
            <HomeInterface
              selectedChat={selectedChat}
              selectedProject={selectedProject}
              messages={messages}
              setMessages={setMessages}
              isTyping={isTyping}
              setIsTyping={setIsTyping}
              chatHistoryByDate={chatHistoryByDate}
              setChatHistoryByDate={setChatHistoryByDate}
              mockProjects={mockProjects}
              projectSessions={projectSessions}
              notifications={notifications}
              activeNotification={activeNotification}
              complexSessions={complexSessions}
              isMobile={isMobile}
              userInfoVisible={userInfoVisible}
              setUserInfoVisible={setUserInfoVisible}
                            colors={colors}
                            theme={theme}
              dpalsAvatar={dpalsAvatar}
              renderD_PalAvatar={renderD_PalAvatar}
              mockUserInfo={mockUserInfo}
              mockRelatedConversations={mockRelatedConversations}
              onSendMessage={handleSendMessage}
              onBackToProject={handleBackToProject}
              onNotificationCardClick={handleNotificationCardClick}
              onNotificationStartChat={handleNotificationStartChat}
            />
                    </div>
        ) : activeNav === 'projects' ? (
          // 项目管理组件
          <ProjectManager
            selectedProject={selectedProject}
            expandedProject={expandedProject}
            projectSessions={projectSessions}
            selectedChat={selectedChat}
            mockProjects={mockProjects}
            csAgentConsultations={selectedProject === 'i18n-agent' ? i18nAgentConsultations : csAgentConsultations}
            colors={colors}
            theme={theme}
            onProjectClick={handleProjectClick}
            onCreateProject={handleCreateProject}
            onChatClick={handleChatClick}
            onProjectSessionSend={handleProjectSessionSend}
            setProjectSessions={setProjectSessions}
            setComplexSessions={setComplexSessions}
            systemData={{
              csAgent: {
                consultations: selectedProject === 'i18n-agent' ? i18nAgentConsultations : csAgentConsultations
              }
            }}
            dpalsAvatar={dpalsAvatar}
            renderD_PalAvatar={renderD_PalAvatar}
            onKnowledgeClick={handleKnowledgeClick}
          />
        ) : activeNav === 'knowledge' || showKnowledge ? (
          // Knowledge组件
          <Knowledge
            colors={colors}
            theme={theme}
            tasks={knowledgeTasks}
            onBackToCSAgent={handleBackToCSAgent}
          />
        ) : (
          <div style={{ padding: '2rem', height: 'calc(100vh - 4rem)', overflowY: 'auto' }}>
            <div style={{ marginBottom: '1.5rem' }}>
              <h2 style={{ fontSize: '1.25rem', marginBottom: '1rem', color: colors.textPrimary }}>Coming Soon</h2>
              <p style={{ color: colors.textSecondary }}>This section is under development.</p>

                      </div>
          </div>
        )}
      </div>
      
      {/* Live2D 通知小人儿 */}
      <Live2DCharacterWidget
        notifications={sortNotificationsByPriority(notifications)}
        onNotificationClick={handleNotificationClick}
        onDeleteNotification={handleDeleteNotification}
        theme={theme}
        colors={colors}
        avatarOptions={dpalsAvatar}
        renderAvatar={renderD_PalAvatar}
        isHomePage={activeNav === 'home' && !selectedChat}
        onChatNavigation={handleChatClick}
      />


    </div>
  );
} 