export interface Message {
  id: number;
  type: 'ai' | 'user';
  content: any;
  timestamp: string;
  attachments?: Array<{
    type: 'image' | 'file';
    url: string;
    name?: string;
    size?: string;
  }>;
}

export interface Chat {
  id: string;
  title: string;
  hasNewMessage: boolean;
}

export interface ChatHistoryGroup {
  label: string;
  chats: Chat[];
}

export interface CSConsultation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: string;
  unread: boolean;
  status: 'active' | 'pending' | 'closed';
  userRank: number;
  game: string;
  type: string;
  userName: string;
  isVip: boolean;
}

export interface Project {
  id: string;
  name: string;
  sessions: ProjectSession[];
  isSpecial?: boolean;
}

export interface ProjectSession {
  id: string;
  name: string;
  createdAt?: string;
}

export interface WorkflowTemplate {
  id: number;
  title: string;
  icon: string;
  category: string;
  description: string;
}

export interface UserInfo {
  game: {
    name: string;
    description: string;
    team: string;
  };
  user: {
    g123Id: string;
    serverId: string;
    gameUid: string;
    roleId: string;
    im: string;
    rank: string;
    amount: string;
    vipLevel: string;
    system: string;
  };
}

export interface RelatedConversation {
  id: string;
  firstUserMessage: string;
  endTime: string;
}

export interface Task {
  id: number;
  title: string;
  due: string;
  ddl: string;
  status: string;
}

export interface DailyReportData {
  date: string;
  team: string;
  participants: string[];
  agenda: Array<{
    id: string;
    type: string;
    content: string;
    owner: string;
    status: string;
  }>;
  nextSteps: string[];
}

export interface SubscriptionTask {
  id: string;
  title: string;
  type: string;
  time: string;
  status: string;
  priority: string;
  content: any;
}

export interface Notification {
  id: string;
  type: string;
  title: string;
  time: string;
  priority: string;
  isDeletable?: boolean;
  content?: any;
}

export interface ComplexSession {
  id: string;
  title: string;
  messages: Message[];
}

export interface Colors {
  bgMain: string;
  bgCard: string;
  bgElevated: string;
  bgSubtle: string;
  textPrimary: string;
  textSecondary: string;
  textTertiary: string;
  borderSubtle: string;
  borderStrong: string;
  primary: string;
  primaryLight: string;
  accent: string;
  success: string;
  error: string;
}

export interface NavGroup {
  key: string;
  label: string;
  icon: string;
}

export type Permission = 'reply' | 'evaluate' | 'knowledge';

export interface TeamMember {
  id: string;
  username: string;
  avatar: string;
  permissions: Permission[];
  isCurrentUser?: boolean;
  capabilities?: string;
}

// Knowledge Base types
export interface KnowledgeTask {
  id: string;
  taskId: string;
  appCode: string;
  general: boolean;
  question: string;
  status: 'Reviewing' | 'Completed' | 'Revision' | 'Draft';
  confidence: number;
  assignedTo: string;
  createdAt: string;
  updatedAt: string;
}

export interface KnowledgeFilter {
  appCode: string;
  general: string;
  status: string;
  keyword: string;
}

// Knowledge Detail Modal types
export interface QAPair {
  id: string;
  question: string;
  answer: string;
}

export interface KnowledgeSuggestion {
  id: string;
  type: 'duplicate' | 'conflict';
  existingQA: QAPair;
  newQA: QAPair;
  isSelected: boolean;
}

export interface KnowledgeDetailData {
  task: KnowledgeTask;
  qaPairs: QAPair[];
  suggestions: KnowledgeSuggestion[];
  rejectionReason?: string; // 驳回原因，只在状态为Revision时存在
}

// Knowledge Base types
export interface KnowledgeBaseItem {
  id: string;
  appCode: string;
  general: boolean;
  question: string;
  answer: string;
  name: string;
  version: string;
  operation: string;
}

export interface KnowledgeBaseFilter {
  appCode: string;
  general: string;
  keyword: string;
}

// Evaluation System types
export interface ConversationMessage {
  type: 'user' | 'ai';
  content: string;
  timestamp: string;
  language?: 'EN' | 'JA' | 'KO' | 'AR' | 'FR' | 'ZH-TW' | 'ES';
  category?: string;
}

export interface EvaluationTask {
  id: string;
  taskId: string;
  appCode: string;
  general: boolean;
  type: 'Response Quality' | 'Knowledge Accuracy' | 'Resolution Time' | 'User Satisfaction' | 'Translation Quality' | 'UI Layout Review' | 'Currency Format Review' | 'Font Rendering Review' | 'Audio Localization Review' | 'Input Method Review';
  subject: string;
  status: 'Pending' | 'In Review' | 'Completed' | 'Failed';
  score?: number;
  assignedTo: string;
  evaluator?: string;
  createdAt: string;
  completedAt?: string;
  // 新增字段以匹配图片中的表格结构
  vip: boolean;
  query: string;
  rating: 'Pending' | 'Good' | 'Poor';
  sessionTime: string;
  evaluatedTime?: string;
  conversationData: ConversationMessage[]; // 会话数据用于右侧栏显示
}

export interface EvaluationFilter {
  status: string;
  vipOnly: boolean;
}

export interface EvaluationCriteria {
  id: string;
  name: string;
  weight: number;
  maxScore: number;
  description: string;
}

export interface EvaluationDetail {
  id: string;
  criteria: EvaluationCriteria[];
  scores: { [criteriaId: string]: number };
  comments: string;
  overallScore: number;
  recommendation: 'Pass' | 'Fail' | 'Needs Improvement';
}

export interface EvaluationDetailData {
  task: EvaluationTask;
  detail: EvaluationDetail;
  conversationId?: string;
  originalContent?: string;
} 