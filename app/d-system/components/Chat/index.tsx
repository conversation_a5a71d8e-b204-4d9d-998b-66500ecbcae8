'use client'
import React, { useState, useEffect } from 'react'
import { Colors, Message, Chat, ChatHistoryGroup } from '../../types'
import { formatFileSize } from '../../utils'
import { ANIMATION_DURATION } from '../../constants'
import MessageList from './MessageList'
import AIInputBox from '../../../components/AIInputBox'

interface ChatInterfaceProps {
  messages: Message[]
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>
  isTyping: boolean
  setIsTyping: React.Dispatch<React.SetStateAction<boolean>>
  selectedChat: string | null
  chatHistoryByDate: ChatHistoryGroup[]
  setChatHistoryByDate: React.Dispatch<React.SetStateAction<ChatHistoryGroup[]>>
  colors: Colors
  theme: 'light' | 'dark'
  dpalsAvatar: any
  renderD_PalAvatar: (avatarOptions: any, colors: any) => string
  isMobile: boolean
  selectedProject: string | null
  
  // 事件处理函数
  onSendMessage?: (message: string, files: File[]) => void
}

export default function ChatInterface({
  messages,
  setMessages,
  isTyping,
  setIsTyping,
  selectedChat,
  chatHistoryByDate,
  setChatHistoryByDate,
  colors,
  theme,
  dpalsAvatar,
  renderD_PalAvatar,
  isMobile,
  selectedProject,
  onSendMessage
}: ChatInterfaceProps) {

  // 生成AI回复内容
  const generateAIResponse = (message: string, files: File[]) => {
    // 简单的回复逻辑，可以根据实际需求扩展
    if (message.toLowerCase().includes('leave') || message.toLowerCase().includes('vacation')) {
      return [
        { type: 'text', content: 'I can help you apply for leave. Would you like to proceed with a leave application?' },
        { type: 'text', content: 'To get started, I need some information:' },
        { type: 'text', content: '1. Type of leave (vacation, sick, personal)' },
        { type: 'text', content: '2. Start date' },
        { type: 'text', content: '3. End date' },
        { type: 'text', content: '4. Reason (optional)' }
      ];
    } else if (message.toLowerCase().includes('meeting') || message.toLowerCase().includes('schedule')) {
      return [
        { type: 'text', content: 'I can help you schedule a meeting. Please provide the following details:' },
        { type: 'text', content: '1. Meeting title' },
        { type: 'text', content: '2. Date and time' },
        { type: 'text', content: '3. Participants (separate with commas)' },
        { type: 'text', content: '4. Meeting agenda (optional)' }
      ];
    } else if (message.toLowerCase().includes('expense') || message.toLowerCase().includes('reimbursement')) {
      return [
        { type: 'text', content: 'I can help with expense reimbursement. Please provide:' },
        { type: 'text', content: '1. Expense amount' },
        { type: 'text', content: '2. Expense date' },
        { type: 'text', content: '3. Expense category' },
        { type: 'text', content: '4. Receipt (you can upload it)' }
      ];
    } else if (files.length > 0) {
      const imageFiles = files.filter(f => f.type.startsWith('image/'));
      if (imageFiles.length > 0) {
        return [
          { type: 'text', content: `Thank you for uploading ${files.length} file(s). I've received ${imageFiles.length} image(s).` },
          { type: 'text', content: 'What would you like me to do with these files?' }
        ];
      } else {
        return [
          { type: 'text', content: `Thank you for uploading ${files.length} file(s).` },
          { type: 'text', content: 'What would you like me to do with these files?' }
        ];
      }
    } else {
      return [
        { type: 'text', content: 'I understand you said: "' + message + '". How can I assist you with this request?' }
      ];
    }
  };

  // 处理发送消息
  const handleSendMessage = (message: string, files: File[]) => {
    if (!message.trim() && files.length === 0) return;

    // 如果父组件提供了自定义处理函数，则使用它
    if (onSendMessage) {
      onSendMessage(message, files);
      return;
    }

    // 添加用户消息
    const attachments = files.map(file => ({
      type: file.type.startsWith('image/') ? 'image' as const : 'file' as const,
      url: URL.createObjectURL(file),
      name: file.name,
      size: formatFileSize(file.size)
    }));

    const userMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: message,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      attachments
    };

    setMessages(prev => [...prev, userMessage]);
    
    // 显示AI正在输入状态
    setIsTyping(true);

    // 模拟AI回复延迟
    setTimeout(() => {
      const aiResponse = {
        id: Date.now() + 1,
        type: 'ai' as const,
        content: generateAIResponse(message, files),
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
      
      // 在用户发送消息后，更新会话顺序
      if (selectedChat) {
        // 查找当前会话在哪个分组
        let foundChat: Chat | null = null;
        let foundGroupIndex = -1;
        let foundChatIndex = -1;
        
        for (let i = 0; i < chatHistoryByDate.length; i++) {
          const chatIndex = chatHistoryByDate[i].chats.findIndex(chat => chat.id === selectedChat);
          if (chatIndex !== -1) {
            foundChat = chatHistoryByDate[i].chats[chatIndex];
            foundGroupIndex = i;
            foundChatIndex = chatIndex;
            break;
          }
        }
        
        if (foundChat) {
          // 复制聊天历史数据
          const updatedChatHistory: ChatHistoryGroup[] = [...chatHistoryByDate];
          
          // 从原有位置移除
          updatedChatHistory[foundGroupIndex].chats.splice(foundChatIndex, 1);
          
          // 添加到今天分组的第一位
          const updatedChat: Chat = {
            ...foundChat,
            hasNewMessage: false
          };
          
          // 如果不在"今天"分组，则移到今天分组
          if (foundGroupIndex !== 0) {
            updatedChatHistory[0].chats.unshift(updatedChat);
          } else {
            // 已经在"今天"分组，只需要重新排序
            updatedChatHistory[0].chats.unshift(updatedChat);
          }
          
          // 更新状态
          setChatHistoryByDate(updatedChatHistory);
        }
      }
    }, ANIMATION_DURATION.MESSAGE_DELAY);
  };

  return (
    <div style={{
      flex: selectedProject === 'cs-agent' ? '1' : '1',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 消息列表 */}
      <MessageList
        messages={messages}
        isTyping={isTyping}
        colors={colors}
        theme={theme}
        dpalsAvatar={dpalsAvatar}
        renderD_PalAvatar={renderD_PalAvatar}
      />
      
      {/* 输入区域 */}
      <div>
        <AIInputBox 
          onSend={handleSendMessage}
          placeholder="Message Doraemon..."
          theme={theme}
          showModeSwitch={false}
          colors={colors}
        />
      </div>
    </div>
  )
} 