'use client'
import React, { useRef, useEffect } from 'react'
import { Colors, Message } from '../../types'
import AIMessage from '../../../components/AIMessage'
import UserMessage from '../../../components/UserMessage'

interface MessageListProps {
  messages: Message[]
  isTyping: boolean
  colors: Colors
  theme: 'light' | 'dark'
  dpalsAvatar: any
  renderD_PalAvatar: (avatarOptions: any, colors: any) => string
}

export default function MessageList({
  messages,
  isTyping,
  colors,
  theme,
  dpalsAvatar,
  renderD_PalAvatar
}: MessageListProps) {
  const chatContainerRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
    }
  }, [messages])

  return (
    <div 
      ref={chatContainerRef}
      style={{ 
        flex: 1, 
        overflowY: 'auto',
        paddingRight: '0.5rem',
        marginBottom: '1rem'
      }}
    >
      {/* 渲染消息历史 */}
      {messages.map((message) => (
        message.type === 'ai' ? (
          <AIMessage 
            key={message.id}
            content={Array.isArray(message.content) ? message.content : [{ type: 'text', content: message.content }]}
            timestamp={message.timestamp}
            theme={theme}
            colors={colors}
            avatar={renderD_PalAvatar(dpalsAvatar, colors)}
          />
        ) : (
          <UserMessage 
            key={message.id}
            content={message.content}
            timestamp={message.timestamp}
            attachments={message.attachments}
            theme={theme}
            colors={colors}
          />
        )
      ))}
      
      {/* 显示AI正在输入的状态 */}
      {isTyping && (
        <AIMessage 
          content={[]}
          isLoading={true}
          theme={theme}
          colors={colors}
          avatar={renderD_PalAvatar(dpalsAvatar, colors)}
        />
      )}
    </div>
  )
} 