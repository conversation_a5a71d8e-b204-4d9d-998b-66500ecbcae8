'use client'
import React from 'react';

interface ProjectsHeaderProps {
  colors: {
    textTertiary: string;
    bgElevated: string;
    textSecondary: string;
  };
  onCreateProject: () => void;
}

/**
 * 项目标题区域组件
 * 包含"Projects"标题和创建项目的加号按钮
 */
export default function ProjectsHeader({ colors, onCreateProject }: ProjectsHeaderProps) {
  return (
    <div style={{ 
      padding: '0.5rem 1rem 0.25rem 1rem', 
      marginTop: '0.75rem',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      {/* Projects标题 */}
      <div style={{ 
        color: colors.textTertiary, 
        fontSize: '0.75rem', 
        fontWeight: 'bold', 
        textTransform: 'uppercase', 
        letterSpacing: '0.05em' 
      }}>
        Projects
      </div>
      
      {/* 创建项目按钮 */}
      <button 
        onClick={onCreateProject}
        style={{
          width: '20px',
          height: '20px',
          borderRadius: '50%',
          backgroundColor: colors.bgElevated,
          border: 'none',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: colors.textSecondary,
          padding: 0,
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'scale(1.1)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
        }}
      >
        {/* 加号图标 */}
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          fill="none" 
          viewBox="0 0 24 24" 
          strokeWidth={1.5} 
          stroke="currentColor" 
          style={{
            width: '1rem', 
            height: '1rem'
          }}
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            d="M12 4.5v15m7.5-7.5h-15" 
          />
        </svg>
      </button>
    </div>
  );
} 