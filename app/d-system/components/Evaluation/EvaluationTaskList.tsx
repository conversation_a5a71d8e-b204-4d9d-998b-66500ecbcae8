'use client'
import React from 'react'
import { Colors, EvaluationTask } from '../../types'

interface EvaluationTaskListProps {
  tasks: EvaluationTask[]
  colors: Colors
  onTaskClick: (taskId: string) => void
  selectedTask: string | null
}

export default function EvaluationTaskList({ tasks, colors, onTaskClick, selectedTask }: EvaluationTaskListProps) {
  const getRatingColor = (rating: string) => {
    switch (rating) {
      case 'Good': return colors.success
      case 'Poor': return colors.error
      case 'Pending': return colors.accent
      default: return colors.textSecondary
    }
  }

  return (
    <div style={{
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      border: `1px solid ${colors.borderSubtle}`,
      overflow: 'hidden'
    }}>
      {/* Table Header */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '60px 3fr 120px 150px 150px 120px',
        gap: '1rem',
        padding: '1rem',
        backgroundColor: colors.bgElevated,
        borderBottom: `1px solid ${colors.borderSubtle}`,
        fontSize: '0.875rem',
        fontWeight: '600',
        color: colors.textSecondary
      }}>
        <div>VIP</div>
        <div>Query</div>
        <div>Rating</div>
        <div>Session Time</div>
        <div>Evaluated Time</div>
        <div>Evaluator</div>
      </div>

      {/* Table Body */}
      <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
        {tasks.length === 0 ? (
          <div style={{
            padding: '3rem',
            textAlign: 'center',
            color: colors.textTertiary
          }}>
            No evaluation tasks found matching the current filters.
          </div>
        ) : (
          tasks.map((task) => (
            <div
              key={task.id}
              onClick={() => onTaskClick(task.id)}
              style={{
                display: 'grid',
                gridTemplateColumns: '60px 3fr 120px 150px 150px 120px',
                gap: '1rem',
                padding: '1rem',
                borderBottom: `1px solid ${colors.borderSubtle}`,
                backgroundColor: selectedTask === task.id ? `${colors.primary}10` : 'transparent',
                cursor: 'pointer',
                transition: 'background-color 0.15s ease'
              }}
              onMouseEnter={(e) => {
                if (selectedTask !== task.id) {
                  e.currentTarget.style.backgroundColor = colors.bgElevated
                }
              }}
              onMouseLeave={(e) => {
                if (selectedTask !== task.id) {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }
              }}
            >
              {/* VIP */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                {task.vip ? (
                  <span style={{
                    fontSize: '0.75rem',
                    color: '#FFD700',
                    backgroundColor: '#FFD70020',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '12px',
                    fontWeight: '600'
                  }}>
                    ⭐
                  </span>
                ) : null}
              </div>

              {/* Query */}
              <div style={{
                fontSize: '0.875rem',
                color: colors.textPrimary,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                lineHeight: '1.4'
              }}>
                {task.query}
              </div>

              {/* Rating */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <span style={{
                  fontSize: '0.75rem',
                  color: getRatingColor(task.rating),
                  backgroundColor: `${getRatingColor(task.rating)}15`,
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontWeight: '500'
                }}>
                  {task.rating}
                </span>
              </div>

              {/* Session Time */}
              <div style={{
                fontSize: '0.75rem',
                color: colors.textSecondary,
                fontFamily: 'monospace'
              }}>
                {task.sessionTime}
              </div>

              {/* Evaluated Time */}
              <div style={{
                fontSize: '0.75rem',
                color: colors.textSecondary,
                fontFamily: 'monospace'
              }}>
                {task.evaluatedTime || '-'}
              </div>

              {/* Evaluator */}
              <div style={{
                fontSize: '0.875rem',
                color: colors.textSecondary
              }}>
                {task.evaluator || '-'}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
} 