'use client'
import React, { useState } from 'react'
import { Colors, EvaluationTask, EvaluationFilter, EvaluationDetailData } from '../../types'
import EvaluationTaskList from './EvaluationTaskList'
import EvaluationFilters from './EvaluationFilters'
import EvaluationDetailModal from './EvaluationDetailModal'
import EvaluationSidebar from './EvaluationSidebar'

interface EvaluationProps {
  colors: Colors
  theme: 'light' | 'dark'
  tasks: EvaluationTask[]
  onBackToCSAgent?: () => void
  systemType?: 'cs' | 'i18n'  // 新增系统类型参数
}

export default function Evaluation({ colors, theme, tasks, onBackToCSAgent, systemType = 'cs' }: EvaluationProps) {
  const [filters, setFilters] = useState<EvaluationFilter>({
    status: 'Pending',
    vipOnly: true, // 默认启用VIP筛选
  })

  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalData, setModalData] = useState<EvaluationDetailData | null>(null)
  const [showSidebar, setShowSidebar] = useState(false)
  const [currentEvaluationTask, setCurrentEvaluationTask] = useState<EvaluationTask | null>(null)
  const [selectedTask, setSelectedTask] = useState<string | null>(null)

  const handleFilterChange = (newFilters: Partial<EvaluationFilter>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }

  const handleTaskClick = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId)
    if (!task) return

    if (task.status === 'Pending') {
      setCurrentEvaluationTask(task)
      setSelectedTask(taskId)
      setShowSidebar(true)
    } else {
      // 将EvaluationTask转换为EvaluationDetailData格式
      const detailData: EvaluationDetailData = {
        task: task,
        detail: {
          id: task.id,
          criteria: [], // 暂时为空，可以根据需要添加
          scores: {},
          comments: '',
          overallScore: task.score || 0,
          recommendation: task.rating === 'Good' ? 'Pass' : task.rating === 'Poor' ? 'Fail' : 'Needs Improvement'
        }
      }
      setModalData(detailData)
      setIsModalOpen(true)
    }
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setModalData(null)
  }

  const handleReset = () => {
    setFilters({
      status: 'Pending', // 重置时保持 Pending 筛选
      vipOnly: true, // 重置时保持VIP筛选
    })
  }

  const handleEvaluate = (taskId: string, rating: 'Good' | 'Poor') => {
    // 更新任务评分
    const updatedTasks = tasks.map(task => 
      task.id === taskId 
        ? { 
            ...task, 
            rating, 
            status: 'Completed' as const,
            evaluatedTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
            evaluator: 'Current User'
          }
        : task
    )
    
    // 关闭侧边栏
    setShowSidebar(false)
    setCurrentEvaluationTask(null)
    
    // 自动切换到下一个 Pending 任务
    const pendingTasks = filteredTasks.filter(t => t.status === 'Pending' && t.id !== taskId)
    if (pendingTasks.length > 0) {
      const nextTask = pendingTasks[0]
      setCurrentEvaluationTask(nextTask)
      setSelectedTask(nextTask.id)
      setShowSidebar(true)
    }
  }

  const handleCloseSidebar = () => {
    setShowSidebar(false)
    setCurrentEvaluationTask(null)
    setSelectedTask(null)
  }

  const filteredTasks = tasks.filter(task => {
    const matchesStatus = !filters.status || task.status === filters.status
    const matchesVip = !filters.vipOnly || task.vip

    return matchesStatus && matchesVip
  })

  return (
    <div style={{
      padding: '1.5rem',
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      backgroundColor: colors.bgMain,
      boxSizing: 'border-box',
      position: 'relative',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header with back button */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '1.5rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          {onBackToCSAgent && (
            <button
              onClick={onBackToCSAgent}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: `1px solid ${colors.borderSubtle}`,
                backgroundColor: colors.bgCard,
                color: colors.textSecondary,
                fontSize: '0.875rem',
                cursor: 'pointer',
                transition: 'all 0.15s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.bgElevated
                e.currentTarget.style.borderColor = colors.borderStrong
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = colors.bgCard
                e.currentTarget.style.borderColor = colors.borderSubtle
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
              </svg>
              Back to {systemType === 'cs' ? 'CS Expert' : 'i18n Expert'}
            </button>
          )}
          <h1 style={{
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600',
            color: colors.textPrimary
          }}>
            {systemType === 'cs' ? 'CS Expert' : 'i18n Expert'} Evaluation Management
          </h1>
        </div>

        <div style={{
          display: 'flex',
          gap: '0.5rem'
        }}>
          <button
            style={{
              padding: '0.5rem 1rem',
              borderRadius: '6px',
              border: 'none',
              backgroundColor: colors.primary,
              color: 'white',
              fontSize: '0.875rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            New Evaluation
          </button>
        </div>
      </div>

      {/* Filters */}
      <div style={{
        marginRight: showSidebar ? '420px' : '0',
        transition: 'margin-right 0.3s ease'
      }}>
        <EvaluationFilters
          filters={filters}
          colors={colors}
          onFilterChange={handleFilterChange}
          onReset={handleReset}
        />
      </div>

      {/* Main Content Area */}
      <div style={{
        flex: 1,
        overflow: 'hidden',
        marginRight: showSidebar ? '420px' : '0',
        transition: 'margin-right 0.3s ease'
      }}>
        {/* Task List */}
        <EvaluationTaskList
          tasks={filteredTasks}
          colors={colors}
          onTaskClick={handleTaskClick}
          selectedTask={selectedTask}
        />
      </div>

      {/* Fixed Evaluation Sidebar - Full height, right side */}
      {showSidebar && (
        <div style={{
          position: 'fixed',
          top: 0,
          right: 0,
          width: '400px',
          height: '100vh',
          zIndex: 1000,
          transform: showSidebar ? 'translateX(0)' : 'translateX(100%)',
          transition: 'transform 0.3s ease'
        }}>
          <EvaluationSidebar
            isOpen={showSidebar}
            task={currentEvaluationTask}
            colors={colors}
            onClose={handleCloseSidebar}
            onEvaluate={handleEvaluate}
          />
        </div>
      )}

      {/* Evaluation Detail Modal */}
      <EvaluationDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        data={modalData}
        colors={colors}
      />
    </div>
  )
} 