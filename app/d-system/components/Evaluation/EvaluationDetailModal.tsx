'use client'
import React, { useState } from 'react'
import { Colors, EvaluationDetailData } from '../../types'

interface EvaluationDetailModalProps {
  isOpen: boolean
  onClose: () => void
  data: EvaluationDetailData | null
  colors: Colors
}

export default function EvaluationDetailModal({ isOpen, onClose, data, colors }: EvaluationDetailModalProps) {
  const [scores, setScores] = useState<{ [criteriaId: string]: number }>({})
  const [comments, setComments] = useState('')

  React.useEffect(() => {
    if (data) {
      setScores(data.detail.scores)
      setComments(data.detail.comments)
    }
  }, [data])

  if (!isOpen || !data) return null

  const isEditable = data.task.status === 'Pending' || data.task.status === 'In Review'
  const isCompleted = data.task.status === 'Completed' || data.task.status === 'Failed'

  const updateScore = (criteriaId: string, score: number) => {
    if (!isEditable) return
    setScores(prev => ({ ...prev, [criteriaId]: score }))
  }

  const calculateOverallScore = () => {
    const totalWeightedScore = data.detail.criteria.reduce((sum, criteria) => {
      const score = scores[criteria.id] || 0
      return sum + (score * criteria.weight / 100)
    }, 0)
    return Math.round(totalWeightedScore * 10) / 10
  }

  const getRecommendation = (score: number) => {
    if (score >= 80) return 'Pass'
    if (score >= 60) return 'Needs Improvement'
    return 'Fail'
  }

  const overallScore = calculateOverallScore()
  const recommendation = getRecommendation(overallScore)

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: colors.bgCard,
        borderRadius: '12px',
        width: '90%',
        maxWidth: '800px',
        maxHeight: '90%',
        display: 'flex',
        flexDirection: 'column',
        border: `1px solid ${colors.borderSubtle}`
      }}>
        {/* Modal Header */}
        <div style={{
          padding: '1.5rem',
          borderBottom: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h2 style={{
              margin: 0,
              fontSize: '1.25rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Evaluation Details
            </h2>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: '0.25rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: colors.textSecondary,
                  fontFamily: 'monospace'
                }}>
                  Task ID: {data.task.taskId}
                </p>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(data.task.taskId)
                  }}
                  style={{
                    background: 'none',
                    border: `1px solid ${colors.borderSubtle}`,
                    borderRadius: '4px',
                    padding: '0.25rem',
                    cursor: 'pointer',
                    color: colors.textSecondary,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  title="Copy Task ID"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '14px', height: '14px'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 01-.75.75H9a.75.75 0 01-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 01-2.25 2.25H6.75A2.25 2.25 0 014.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 011.927-.184" />
                  </svg>
                </button>
              </div>
              <span style={{
                padding: '0.25rem 0.5rem',
                borderRadius: '12px',
                fontSize: '0.75rem',
                fontWeight: '500',
                color: data.task.status === 'Completed' ? colors.success : 
                       data.task.status === 'In Review' ? colors.primary :
                       data.task.status === 'Failed' ? colors.error : colors.accent,
                backgroundColor: data.task.status === 'Completed' ? `${colors.success}15` : 
                                data.task.status === 'In Review' ? `${colors.primary}15` :
                                data.task.status === 'Failed' ? `${colors.error}15` : `${colors.accent}15`
              }}>
                {data.task.status}
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: colors.textTertiary,
              padding: '0.5rem',
              borderRadius: '4px'
            }}
          >
            ✕
          </button>
        </div>

        {/* Modal Content */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '1.5rem'
        }}>
          {/* Task Subject */}
          <div style={{ marginBottom: '1.5rem' }}>
            <h3 style={{
              margin: '0 0 0.5rem 0',
              fontSize: '1rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Subject
            </h3>
            <p style={{
              margin: 0,
              fontSize: '0.875rem',
              color: colors.textSecondary,
              lineHeight: '1.5'
            }}>
              {data.task.subject}
            </p>
          </div>

          {/* Evaluation Criteria */}
          <div style={{ marginBottom: '1.5rem' }}>
            <h3 style={{
              margin: '0 0 1rem 0',
              fontSize: '1rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Evaluation Criteria
            </h3>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {data.detail.criteria.map((criteria) => (
                <div
                  key={criteria.id}
                  style={{
                    padding: '1rem',
                    border: `1px solid ${colors.borderSubtle}`,
                    borderRadius: '8px',
                    backgroundColor: colors.bgElevated
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '0.5rem'
                  }}>
                    <span style={{
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      color: colors.textPrimary
                    }}>
                      {criteria.name} (Weight: {criteria.weight}%)
                    </span>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      {isEditable ? (
                        <input
                          type="number"
                          min="0"
                          max={criteria.maxScore}
                          value={scores[criteria.id] || 0}
                          onChange={(e) => updateScore(criteria.id, Number(e.target.value))}
                          style={{
                            width: '60px',
                            padding: '0.25rem',
                            border: `1px solid ${colors.borderSubtle}`,
                            borderRadius: '4px',
                            backgroundColor: colors.bgCard,
                            color: colors.textPrimary,
                            fontSize: '0.875rem',
                            textAlign: 'center'
                          }}
                        />
                      ) : (
                        <span style={{
                          fontSize: '0.875rem',
                          fontWeight: '600',
                          color: colors.textPrimary
                        }}>
                          {scores[criteria.id] || 0}
                        </span>
                      )}
                      <span style={{
                        fontSize: '0.875rem',
                        color: colors.textSecondary
                      }}>
                        / {criteria.maxScore}
                      </span>
                    </div>
                  </div>
                  <p style={{
                    margin: 0,
                    fontSize: '0.75rem',
                    color: colors.textTertiary,
                    lineHeight: '1.4'
                  }}>
                    {criteria.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Overall Score */}
          <div style={{
            padding: '1rem',
            border: `2px solid ${overallScore >= 80 ? colors.success : overallScore >= 60 ? colors.accent : colors.error}`,
            borderRadius: '8px',
            backgroundColor: `${overallScore >= 80 ? colors.success : overallScore >= 60 ? colors.accent : colors.error}10`,
            marginBottom: '1.5rem'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <span style={{
                fontSize: '1rem',
                fontWeight: '600',
                color: colors.textPrimary
              }}>
                Overall Score
              </span>
              <span style={{
                fontSize: '1.5rem',
                fontWeight: '700',
                color: overallScore >= 80 ? colors.success : overallScore >= 60 ? colors.accent : colors.error
              }}>
                {overallScore}%
              </span>
            </div>
            <div style={{
              marginTop: '0.5rem',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <span style={{
                fontSize: '0.875rem',
                color: colors.textSecondary
              }}>
                Recommendation:
              </span>
              <span style={{
                fontSize: '0.875rem',
                fontWeight: '600',
                color: overallScore >= 80 ? colors.success : overallScore >= 60 ? colors.accent : colors.error
              }}>
                {recommendation}
              </span>
            </div>
          </div>

          {/* Comments */}
          <div>
            <h3 style={{
              margin: '0 0 0.5rem 0',
              fontSize: '1rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Comments
            </h3>
            {isEditable ? (
              <textarea
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                placeholder="Add your evaluation comments..."
                style={{
                  width: '100%',
                  minHeight: '100px',
                  padding: '0.75rem',
                  border: `1px solid ${colors.borderSubtle}`,
                  borderRadius: '6px',
                  backgroundColor: colors.bgElevated,
                  color: colors.textPrimary,
                  fontSize: '0.875rem',
                  lineHeight: '1.5',
                  resize: 'vertical',
                  fontFamily: 'inherit'
                }}
              />
            ) : (
              <div style={{
                padding: '0.75rem',
                border: `1px solid ${colors.borderSubtle}`,
                borderRadius: '6px',
                backgroundColor: colors.bgElevated,
                fontSize: '0.875rem',
                color: colors.textSecondary,
                lineHeight: '1.5',
                minHeight: '60px'
              }}>
                {comments || 'No comments provided'}
              </div>
            )}
          </div>
        </div>

        {/* Modal Footer */}
        <div style={{
          padding: '1rem 1.5rem',
          borderTop: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '0.5rem'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              border: `1px solid ${colors.borderSubtle}`,
              backgroundColor: colors.bgElevated,
              color: colors.textSecondary,
              fontSize: '0.875rem',
              cursor: 'pointer'
            }}
          >
            Close
          </button>
          
          {isEditable && (
            <button
              style={{
                padding: '0.75rem 1.5rem',
                borderRadius: '4px',
                border: 'none',
                backgroundColor: colors.primary,
                color: 'white',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}
            >
              Save Evaluation
            </button>
          )}
        </div>
      </div>
    </div>
  )
} 