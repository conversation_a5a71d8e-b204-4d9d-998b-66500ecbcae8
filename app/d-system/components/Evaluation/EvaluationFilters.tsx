'use client'
import React from 'react'
import { Colors, EvaluationFilter } from '../../types'

interface EvaluationFiltersProps {
  filters: EvaluationFilter
  colors: Colors
  onFilterChange: (filters: Partial<EvaluationFilter>) => void
  onReset: () => void
}

export default function EvaluationFilters({ filters, colors, onFilterChange, onReset }: EvaluationFiltersProps) {
  const statusOptions = ['Pending', 'Good', 'Poor']

  return (
    <div style={{
      display: 'flex',
      gap: '1rem',
      marginBottom: '1.5rem',
      padding: '1rem',
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      border: `1px solid ${colors.borderSubtle}`,
      flexWrap: 'wrap',
      alignItems: 'flex-end'
    }}>
      {/* VIP Only Filter */}
      <div style={{ minWidth: '120px' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          VIP Only:
        </label>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',
          height: '36px'
        }}>
          <button
            onClick={() => onFilterChange({ vipOnly: !filters.vipOnly })}
            style={{
              display: 'flex',
              alignItems: 'center',
              backgroundColor: filters.vipOnly ? colors.primary : colors.bgElevated,
              border: `1px solid ${filters.vipOnly ? colors.primary : colors.borderSubtle}`,
              borderRadius: '12px',
              padding: '0.25rem 0.75rem',
              fontSize: '0.75rem',
              fontWeight: '600',
              color: filters.vipOnly ? 'white' : colors.textSecondary,
              cursor: 'pointer',
              transition: 'all 0.15s ease'
            }}
          >
            {filters.vipOnly ? 'ON' : 'OFF'}
          </button>
        </div>
      </div>

      {/* Status Filter with dropdown showing options */}
      <div style={{ minWidth: '140px' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          Status
        </label>
        <select
          value={filters.status}
          onChange={(e) => onFilterChange({ status: e.target.value })}
          style={{
            width: '100%',
            padding: '0.5rem',
            borderRadius: '4px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem'
          }}
        >
          <option value="">All Ratings</option>
          <option value="Pending">Pending(6)</option>
          <option value="Good">Good</option>
          <option value="Poor">Poor</option>
        </select>
      </div>

      {/* Evaluator Filter */}
      <div style={{ minWidth: '150px' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          Evaluator
        </label>
        <select
          style={{
            width: '100%',
            padding: '0.5rem',
            borderRadius: '4px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem'
          }}
        >
          <option value="">All Evaluators</option>
        </select>
      </div>

      {/* Session Time Range */}
      <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'flex-end' }}>
        <div>
          <label style={{
            display: 'block',
            marginBottom: '0.5rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: colors.textSecondary
          }}>
            Session Time
          </label>
          <input
            type="date"
            defaultValue="2025-06-19"
            style={{
              padding: '0.5rem',
              borderRadius: '4px',
              border: `1px solid ${colors.borderSubtle}`,
              backgroundColor: colors.bgElevated,
              color: colors.textPrimary,
              fontSize: '0.875rem'
            }}
          />
        </div>
        <span style={{ fontSize: '0.875rem', color: colors.textSecondary, marginBottom: '0.25rem' }}>—</span>
        <div>
          <input
            type="date"
            defaultValue="2025-06-26"
            style={{
              padding: '0.5rem',
              borderRadius: '4px',
              border: `1px solid ${colors.borderSubtle}`,
              backgroundColor: colors.bgElevated,
              color: colors.textPrimary,
              fontSize: '0.875rem'
            }}
          />
        </div>
      </div>
    </div>
  )
} 