'use client'
import React from 'react'
import { Colors, EvaluationTask } from '../../types'

interface EvaluationSidebarProps {
  isOpen: boolean
  task: EvaluationTask | null
  colors: Colors
  onClose: () => void
  onEvaluate: (taskId: string, rating: 'Good' | 'Poor') => void
}

export default function EvaluationSidebar({ isOpen, task, colors, onClose, onEvaluate }: EvaluationSidebarProps) {
  if (!isOpen || !task) return null

  const handleAnnotation = (rating: 'Pending' | 'Good' | 'Poor') => {
    if (rating === 'Good' || rating === 'Poor') {
      onEvaluate(task.id, rating)
    }
    // Pending不需要特殊处理，只是重置状态
  }

  const getLanguageFlag = (language?: string) => {
    switch (language) {
      case 'EN': return '🇬🇧'
      case 'JA': return '🇯🇵'
      case 'KO': return '🇰🇷'
      default: return ''
    }
  }

  return (
    <div style={{
      width: '100%',
      height: '100%',
      backgroundColor: colors.bgCard,
      borderLeft: `1px solid ${colors.borderSubtle}`,
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* Header */}
      <div style={{
        padding: '1.5rem',
        borderBottom: `1px solid ${colors.borderSubtle}`,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        flexShrink: 0
      }}>
        <div style={{ flex: 1 }}>
          <h3 style={{
            margin: '0 0 0.5rem 0',
            fontSize: '1rem',
            fontWeight: '600',
            color: colors.textPrimary
          }}>
            Task ID: {task.taskId}
          </h3>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
            <span style={{
              fontSize: '0.75rem',
              color: colors.textSecondary
            }}>
              App: {task.appCode || 'vividarmy'}
            </span>
            {task.vip && (
              <span style={{
                fontSize: '0.75rem',
                color: '#FFD700',
                fontWeight: '600'
              }}>
                ⭐ VIP
              </span>
            )}
          </div>
          <div style={{
            fontSize: '0.75rem',
            color: colors.textSecondary
          }}>
            Evaluator: -
          </div>
        </div>
        <button
          onClick={onClose}
          style={{
            background: 'none',
            border: 'none',
            cursor: 'pointer',
            color: colors.textTertiary,
            padding: '0.25rem',
            borderRadius: '4px',
            fontSize: '1.2rem'
          }}
        >
          ✕
        </button>
      </div>

      {/* Annotation Buttons */}
      <div style={{
        display: 'flex',
        borderBottom: `1px solid ${colors.borderSubtle}`,
        flexShrink: 0
      }}>
        {[
          { key: 'Pending', label: '⏳ Pending', current: task.rating === 'Pending' },
          { key: 'Good', label: '😊 Good', current: task.rating === 'Good' },
          { key: 'Poor', label: '😞 Poor', current: task.rating === 'Poor' }
        ].map((annotation) => (
          <button
            key={annotation.key}
            onClick={() => handleAnnotation(annotation.key as 'Pending' | 'Good' | 'Poor')}
            style={{
              flex: 1,
              padding: '0.75rem 1rem',
              border: 'none',
              backgroundColor: annotation.current ? colors.primary : colors.bgCard,
              color: annotation.current ? 'white' : colors.textSecondary,
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              borderBottom: annotation.current ? `2px solid ${colors.primary}` : 'none',
              transition: 'all 0.15s ease'
            }}
            onMouseEnter={(e) => {
              if (!annotation.current) {
                e.currentTarget.style.backgroundColor = colors.bgElevated
              }
            }}
            onMouseLeave={(e) => {
              if (!annotation.current) {
                e.currentTarget.style.backgroundColor = colors.bgCard
              }
            }}
          >
            {annotation.label}
          </button>
        ))}
      </div>

      {/* Conversation Section - Always visible */}
      <div style={{
        flex: 1,
        padding: '1.5rem',
        overflow: 'auto'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '1rem'
        }}>
          <h4 style={{
            margin: 0,
            fontSize: '0.875rem',
            fontWeight: '600',
            color: colors.textPrimary
          }}>
            Conversation ({task.conversationData.length} messages)
          </h4>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem'
          }}>
            {/* Language flags */}
            <div style={{
              display: 'flex',
              gap: '0.25rem'
            }}>
              {[...new Set(task.conversationData.map(m => m.language).filter(Boolean))].map(lang => (
                <span key={lang} style={{ fontSize: '0.875rem' }}>
                  {getLanguageFlag(lang)}
                </span>
              ))}
            </div>
            {/* URL icon */}
            <button style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: colors.textTertiary,
              padding: '0.25rem',
              fontSize: '0.875rem'
            }}>
              🔗
            </button>
          </div>
        </div>
        
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem'
        }}>
          {task.conversationData.map((message, index) => (
            <div key={index} style={{
              padding: '0.75rem',
              borderRadius: '8px',
              backgroundColor: message.type === 'user' ? colors.bgElevated : colors.bgMain,
              border: `1px solid ${colors.borderSubtle}`
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                marginBottom: '0.5rem'
              }}>
                <span style={{
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: message.type === 'user' ? colors.primary : colors.success
                }}>
                  {message.type === 'user' ? 'User' : 'AI Assistant'}
                </span>
                <span style={{
                  fontSize: '0.625rem',
                  color: colors.textTertiary
                }}>
                  {message.timestamp}
                </span>
              </div>
              <div style={{
                fontSize: '0.75rem',
                color: colors.textPrimary,
                lineHeight: '1.4'
              }}>
                {message.content}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 