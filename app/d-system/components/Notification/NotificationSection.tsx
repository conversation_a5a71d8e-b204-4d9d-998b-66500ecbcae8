'use client'
import React from 'react'
import { Colors } from '../../types'
import { sortNotificationsByPriority } from '../../utils'
import NotificationCard from './NotificationCard'

interface NotificationSectionProps {
  title: string
  icon: React.ReactNode
  color: string
  notifications: any[]
  isMobile: boolean
  colors: Colors
  theme: 'light' | 'dark'
  activeNotification: string | null
  
  // 事件处理函数
  onCardClick: (notificationId: string) => void
  onStartChat: (notification: any) => void
}

export default function NotificationSection({
  title,
  icon,
  color,
  notifications,
  isMobile,
  colors,
  theme,
  activeNotification,
  onCardClick,
  onStartChat
}: NotificationSectionProps) {

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: isMobile ? '400px' : '100%',
      width: '100%',
      padding: '0'
    }}>
      {/* 分类标题 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        height: '32px',
        marginBottom: '16px',
        position: 'sticky',
        top: 0,
        zIndex: 5,
        width: '100%',
        paddingLeft: '2px'
      }}>
        <h3 style={{
          fontSize: '0.9rem',
          fontWeight: '600',
          margin: '0',
          color: color,
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }}>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
            {icon}
          </svg>
          {title}
        </h3>
      </div>
      
      {/* 通知列表 */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: '6px',
        paddingRight: '4px',
        width: '100%',
        paddingLeft: '2px'
      }}>
        {sortNotificationsByPriority(notifications).map(notification => (
          <NotificationCard
            key={notification.id}
            notification={notification}
            activeNotification={activeNotification}
            colors={colors}
            theme={theme}
            onCardClick={onCardClick}
            onStartChat={onStartChat}
          />
        ))}
      </div>
    </div>
  );
} 