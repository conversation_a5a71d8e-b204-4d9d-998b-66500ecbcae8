'use client'
import React from 'react'
import { Colors, Message, Chat, ChatHistoryGroup } from '../../types'
import NotificationSection from './NotificationSection'

interface NotificationWidgetProps {
  notifications: any[]
  activeNotification: string | null
  isMobile: boolean
  colors: Colors
  theme: 'light' | 'dark'
  complexSessions: any[]
  chatHistoryByDate: ChatHistoryGroup[]
  
  // 事件处理函数
  onCardClick: (notificationId: string) => void
  onStartChat: (notification: any, typeInfo: any) => void
}

export default function NotificationWidget({
  notifications,
  activeNotification,
  isMobile,
  colors,
  theme,
  complexSessions,
  chatHistoryByDate,
  onCardClick,
  onStartChat
}: NotificationWidgetProps) {

  // 获取通知类型对应的图标、颜色和标题
  const getTypeInfo = (type: string) => {
    switch(type) {
      case 'auto-plan':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />,
          color: colors.primary,
          title: 'Plan'
        };
      case 'reminder':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0118 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3l1.5 1.5 3-3.75" />,
          color: colors.accent,
          title: 'Reminder'
        };
      case 'schedule':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z" />,
          color: colors.success,
          title: 'Event'
        };
      case 'task':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />,
          color: colors.error,
          title: 'Task'
        };
      case 'system':
      default:
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />,
          color: colors.error,
          title: 'Alert'
        };
    }
  };

  // 处理开始聊天
  const handleStartChat = (notification: any) => {
    const typeInfo = getTypeInfo(notification.type);
    onStartChat(notification, typeInfo);
  };

  // 过滤不同类型的通知
  const taskNotifications = notifications.filter(item => item.type === 'task' || item.type === 'reminder' || item.type === 'system');
  const planNotifications = notifications.filter(item => item.type === 'auto-plan');
  const scheduleNotifications = notifications.filter(item => item.type === 'schedule');

  return (
    <div style={{
      display: 'grid',
      gridTemplateColumns: isMobile ? '1fr' : 'repeat(3, 1fr)',
      gap: isMobile ? '1rem' : '1.5rem',
      height: '100%',
      width: '100%',
      overflow: 'hidden'
    }}>
      {/* Tasks 分类 */}
      <NotificationSection
        title="Tasks"
        icon={<path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />}
        color={colors.error}
        notifications={taskNotifications}
        isMobile={isMobile}
        colors={colors}
        theme={theme}
        activeNotification={activeNotification}
        onCardClick={onCardClick}
        onStartChat={handleStartChat}
      />
      
      {/* Plans 分类 */}
      <NotificationSection
        title="Plans"
        icon={<path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />}
        color={colors.primary}
        notifications={planNotifications}
        isMobile={isMobile}
        colors={colors}
        theme={theme}
        activeNotification={activeNotification}
        onCardClick={onCardClick}
        onStartChat={handleStartChat}
      />
      
      {/* Schedule 分类 */}
      <NotificationSection
        title="Schedule"
        icon={<path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />}
        color={colors.success}
        notifications={scheduleNotifications}
        isMobile={isMobile}
        colors={colors}
        theme={theme}
        activeNotification={activeNotification}
        onCardClick={onCardClick}
        onStartChat={handleStartChat}
      />
    </div>
  );
} 