'use client'
import React from 'react'
import { Colors, Message, Chat, ChatHistoryGroup } from '../../types'
import { ANIMATION_DURATION } from '../../constants'

interface NotificationCardProps {
  notification: any
  activeNotification: string | null
  colors: Colors
  theme: 'light' | 'dark'
  
  // 事件处理函数
  onCardClick: (notificationId: string) => void
  onStartChat: (notification: any) => void
}

export default function NotificationCard({
  notification,
  activeNotification,
  colors,
  theme,
  onCardClick,
  onStartChat
}: NotificationCardProps) {

  // 获取通知类型对应的图标、颜色和标题
  const getTypeInfo = (type: string) => {
    switch(type) {
      case 'auto-plan':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />,
          color: colors.primary,
          title: 'Plan'
        };
      case 'reminder':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0118 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3l1.5 1.5 3-3.75" />,
          color: colors.accent,
          title: 'Reminder'
        };
      case 'schedule':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z" />,
          color: colors.success,
          title: 'Event'
        };
      case 'task':
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />,
          color: colors.error,
          title: 'Task'
        };
      case 'system':
      default:
        return {
          icon: <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />,
          color: colors.error,
          title: 'Alert'
        };
    }
  };

  const typeInfo = getTypeInfo(notification.type);

  // 处理卡片点击，只展开被点击的卡片
  const handleCardClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    onCardClick(notification.id);
    
    // 滚动处理：确保展开的卡片在视口内，但不会强制滚动到顶部
    // 延迟执行以等待DOM更新
    if (activeNotification !== notification.id) {
      setTimeout(() => {
        const card = document.getElementById(`notification-${notification.id}`);
        if (card) {
          // 只在卡片部分不可见时滚动
          const rect = card.getBoundingClientRect();
          const isFullyVisible = (
            rect.top >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight)
          );
          
          if (!isFullyVisible) {
            card.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
          }
        }
      }, 100);
    }
  };

  // 打开聊天处理
  const handleStartChat = (event: React.MouseEvent) => {
    event.stopPropagation();
    onStartChat(notification);
  };

  return (
    <div 
      id={`notification-${notification.id}`}
      key={notification.id}
      style={{
        backgroundColor: colors.bgCard,
        borderRadius: '8px',
        padding: '8px 10px',
        height: activeNotification === notification.id ? 'auto' : 'auto',
        minHeight: activeNotification === notification.id ? 'auto' : '64px',
        maxHeight: activeNotification === notification.id ? '400px' : '64px',
        border: `1px solid ${activeNotification === notification.id ? colors.primary : colors.borderSubtle}`,
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        transform: 'none',
        boxShadow: activeNotification === notification.id 
          ? (theme === 'light' ? '0 8px 16px rgba(0, 0, 0, 0.08)' : '0 8px 16px rgba(0, 0, 0, 0.3)')
          : (theme === 'light' ? '0 2px 6px rgba(0, 0, 0, 0.05)' : '0 2px 6px rgba(0, 0, 0, 0.2)'),
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: activeNotification === notification.id ? 'flex-start' : 'center',
        zIndex: activeNotification === notification.id ? 10 : 1,
        marginBottom: '8px',
        overflow: activeNotification === notification.id ? 'auto' : 'hidden',
        width: '100%',
        boxSizing: 'border-box',
        transformOrigin: 'top'
      }}
      onClick={handleCardClick}
    >
      {/* 左侧颜色标识，根据通知类型显示颜色 */}
      <div style={{
        position: 'absolute',
        left: '0',
        top: '16px',
        width: '4px',
        height: '20px',
        backgroundColor: notification.type === 'auto-plan' ? colors.primary :
                        notification.type === 'reminder' || notification.type === 'system' ? colors.accent :
                        notification.type === 'task' ? colors.error :
                        colors.success,
        borderRadius: '0 2px 2px 0'
      }}></div>
      
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2px',
        paddingLeft: '8px'
      }}>
        <div>
          <h3 style={{
            margin: '0 0 1px 0',
            fontSize: '12px',
            fontWeight: '600',
            color: colors.textPrimary,
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: '150px',
            lineHeight: '1.2'
          }}>
            {notification.title}
          </h3>
          <div style={{
            fontSize: '10px',
            color: colors.textTertiary,
            lineHeight: '1.1'
          }}>
            {new Date(notification.time).toLocaleString('en-US', {
              month: 'numeric',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        </div>
        
        {/* Chat 图标按钮 */}
        <div 
          onClick={handleStartChat}
          style={{
            width: '30px',
            height: '30px',
            borderRadius: '6px',
            backgroundColor: `${colors.primary}15`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: colors.primary,
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = `${colors.primary}25`;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = `${colors.primary}15`;
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 01-.923 1.785A5.969 5.969 0 006 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337z" />
          </svg>
        </div>
      </div>
      
      {/* 展开内容 */}
      {activeNotification === notification.id && (
        <div style={{
          marginTop: '4px',
          padding: '6px 8px',
          backgroundColor: theme === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.03)',
          borderRadius: '6px',
          fontSize: '11px',
          lineHeight: '1.3',
          animation: 'expandDown 0.3s ease',
          border: `1px solid ${colors.borderSubtle}`,
          maxHeight: '300px',
          overflowY: 'auto'
        }}>
          <style jsx>{`
            @keyframes expandDown {
              from { opacity: 0; }
              to { opacity: 1; }
            }
          `}</style>
          
          <div style={{ fontSize: '12px', fontWeight: '500', marginBottom: '6px', color: colors.textPrimary }}>
            {notification.title}
          </div>
          
          {/* 根据通知类型显示额外内容 */}
          {notification.content && notification.content.description && (
            <div style={{ fontSize: '11px', marginBottom: '6px', color: colors.textSecondary }}>
              {notification.content.description}
            </div>
          )}
          
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginTop: '6px'
          }}>
            <div style={{ fontSize: '10px', color: colors.textTertiary }}>
              {new Date(notification.time).toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'numeric',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 