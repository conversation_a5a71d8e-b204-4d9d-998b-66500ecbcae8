'use client'
import React from 'react';

interface SidebarLogoProps {
  colors: {
    primary: string;
    accent: string;
    textPrimary: string;
    textTertiary: string;
    borderSubtle: string;
  };
}

/**
 * 侧边栏Logo区域组件
 * 显示Doraemon品牌标识和平台名称
 */
export default function SidebarLogo({ colors }: SidebarLogoProps) {
  return (
    <div style={{ 
      padding: '1.5rem', 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center', 
      borderBottom: `1px solid ${colors.borderSubtle}` 
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* Doraemon Logo图标 */}
        <div style={{ 
          width: '2.5rem', 
          height: '2.5rem', 
          borderRadius: '0.5rem', 
          background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.accent} 100%)`,
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center', 
          marginRight: '0.75rem' 
        }}>
          <span style={{ 
            color: 'white', 
            fontWeight: 'bold', 
            fontSize: '1.25rem' 
          }}>
            D
          </span>
        </div>
        
        {/* 品牌文字 */}
        <div>
          <div style={{ 
            fontWeight: 'bold', 
            fontSize: '1.25rem', 
            color: colors.textPrimary 
          }}>
            Doraemon
          </div>
          <div style={{ 
            fontSize: '0.75rem', 
            color: colors.textTertiary 
          }}>
            AI Work Platform
          </div>
        </div>
      </div>
    </div>
  );
} 