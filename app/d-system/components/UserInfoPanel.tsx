'use client';

import React, { useState, useEffect } from 'react';
import { Colors } from '../types';

interface UserInfoPanelProps {
  colors: Colors;
  theme: 'light' | 'dark';
  themeMode: 'light' | 'dark' | 'system';
  showUserMenu: boolean;
  isMouseOverProfile: boolean;
  isMouseOverMenu: boolean;
  onProfileMouseEnter: () => void;
  onProfileMouseLeave: () => void;
  onMenuMouseEnter: () => void;
  onMenuMouseLeave: () => void;
  onThemeModeChange: (mode: 'light' | 'dark' | 'system') => void;
  onMyDPalClick: () => void;
}

export default function UserInfoPanel({
  colors,
  theme,
  themeMode,
  showUserMenu,
  isMouseOverProfile,
  isMouseOverMenu,
  onProfileMouseEnter,
  onProfileMouseLeave,
  onMenuMouseEnter,
  onMenuMouseLeave,
  onThemeModeChange,
  onMyDPalClick
}: UserInfoPanelProps) {
  return (
    <div style={{ 
      padding: '1rem', 
      borderTop: `1px solid ${colors.borderSubtle}`,
      position: 'absolute',
      bottom: 0,
      left: 0,
      width: '100%',
      boxSizing: 'border-box',
      backgroundColor: colors.bgCard
    }}
    onMouseEnter={onProfileMouseEnter}
    onMouseLeave={onProfileMouseLeave}
    >
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        cursor: 'pointer',
        position: 'relative',
        width: '100%',
        overflow: 'hidden'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center',
          width: '100%'
        }}>
          <div style={{ 
            width: '36px', 
            height: '36px', 
            borderRadius: '50%', 
            backgroundColor: colors.primary,
            marginRight: '0.75rem',
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontWeight: 'bold',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
          }}>
            {/* 显示用户首字母，而不是Doraemon头像 */}
            D
          </div>
          <div>
            <div style={{ 
              fontWeight: '500' 
            }}>
              David Chen
            </div>
            <div style={{ 
              fontSize: '0.75rem', 
              color: colors.textTertiary 
            }}>
              Data Analyst
            </div>
          </div>
        </div>
      </div>
      
      {/* 用户菜单 */}
      {showUserMenu && (
        <>
          {/* 隐形的连接区域，确保鼠标可以平滑地从个人资料移动到菜单 */}
          <div 
            style={{
              position: 'absolute',
              bottom: '100%',
              left: '0',
              right: '0',
              height: '8px',
              zIndex: 5
            }}
            onMouseEnter={onMenuMouseEnter}
            onMouseLeave={onMenuMouseLeave}
          />
          {/* 实际菜单 */}
          <div 
            style={{
              position: 'absolute',
              bottom: '100%',
              left: '1rem',
              right: '1rem',
              backgroundColor: colors.bgCard,
              borderRadius: '8px',
              boxShadow: theme === 'light' 
                ? '0 4px 12px rgba(0, 0, 0, 0.1)' 
                : '0 4px 12px rgba(0, 0, 0, 0.3)',
              border: `1px solid ${colors.borderSubtle}`,
              overflow: 'hidden',
              zIndex: 20,
              marginBottom: '8px' // 与用户资料区域保持间距
            }}
            onMouseEnter={onMenuMouseEnter}
            onMouseLeave={onMenuMouseLeave}
          >
            {/* My D-Pal */}
            <div 
              style={{ 
                padding: '0.75rem 1rem',
                display: 'flex',
                alignItems: 'center',
                borderBottom: `1px solid ${colors.borderSubtle}`,
                color: colors.textPrimary,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
              onClick={() => {
                onMyDPalClick();
              }}
              onMouseEnter={(e) => {
                onMenuMouseEnter(); // 确保鼠标在菜单项上时不关闭菜单
                e.currentTarget.style.backgroundColor = colors.bgSubtle;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px', marginRight: '8px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              My D-Pal
            </div>

            {/* Download D Client */}
            <div 
              style={{ 
                padding: '0.75rem 1rem',
                display: 'flex',
                alignItems: 'center',
                borderBottom: `1px solid ${colors.borderSubtle}`,
                color: colors.textPrimary,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
              onClick={() => {
                window.open('/d-system/download', '_blank');
              }}
              onMouseEnter={(e) => {
                onMenuMouseEnter();
                e.currentTarget.style.backgroundColor = colors.bgSubtle;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px', marginRight: '8px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
              </svg>
              Download D Client
            </div>

            {/* Admin Portal */}
            <div 
              style={{ 
                padding: '0.75rem 1rem',
                display: 'flex',
                alignItems: 'center',
                borderBottom: `1px solid ${colors.borderSubtle}`,
                color: colors.textPrimary,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
              onClick={() => {
                window.open('https://d.g123.jp/admin/agent', '_blank');
              }}
              onMouseEnter={(e) => {
                onMenuMouseEnter(); // 确保鼠标在菜单项上时不关闭菜单
                e.currentTarget.style.backgroundColor = colors.bgSubtle;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px', marginRight: '8px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Admin Portal
            </div>
            
            {/* 主题设置 */}
            <div style={{ padding: '0.75rem 1rem', borderBottom: `1px solid ${colors.borderSubtle}` }}>
              <div style={{ 
                color: colors.textPrimary,
                fontWeight: '500',
                marginBottom: '0.5rem',
                fontSize: '0.875rem'
              }}>
                Theme Settings
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                {(['light', 'dark', 'system'] as const).map((mode) => (
                  <div
                    key={mode}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '0.375rem 0.5rem',
                      borderRadius: '0.25rem',
                      cursor: 'pointer',
                      backgroundColor: themeMode === mode ? colors.bgSubtle : 'transparent',
                      transition: 'background-color 0.15s ease'
                    }}
                    onClick={() => {
                      onThemeModeChange(mode);
                      onMenuMouseEnter();
                    }}
                    onMouseEnter={(e) => {
                      onMenuMouseEnter();
                      if (themeMode !== mode) {
                        e.currentTarget.style.backgroundColor = colors.bgSubtle;
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (themeMode !== mode) {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }
                    }}
                  >
                    <div style={{ 
                      width: '12px', 
                      height: '12px', 
                      borderRadius: '50%',
                      border: `2px solid ${themeMode === mode ? colors.primary : colors.borderSubtle}`,
                      marginRight: '0.5rem',
                      position: 'relative'
                    }}>
                      {themeMode === mode && (
                        <div style={{
                          width: '4px',
                          height: '4px',
                          borderRadius: '50%',
                          backgroundColor: colors.primary,
                          position: 'absolute',
                          top: '2px',
                          left: '2px'
                        }} />
                      )}
                    </div>
                    <span style={{ 
                      fontSize: '0.8125rem', 
                      color: colors.textPrimary,
                      textTransform: 'capitalize'
                    }}>
                      {mode === 'system' ? 'Follow System' : mode}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Logout */}
            <div 
              style={{ 
                padding: '0.75rem 1rem',
                display: 'flex',
                alignItems: 'center',
                color: colors.error,
                cursor: 'pointer',
                transition: 'background-color 0.2s ease'
              }}
              onMouseEnter={(e) => {
                onMenuMouseEnter(); // 确保鼠标在菜单项上时不关闭菜单
                e.currentTarget.style.backgroundColor = colors.bgSubtle;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px', marginRight: '8px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
              </svg>
              Logout
            </div>
          </div>
        </>
      )}
    </div>
  );
} 