'use client'
import React from 'react'
import { Colors, Project } from '../../types'
import ChatInterface from '../Chat'
import WelcomeHeader from './WelcomeHeader'
import WorkOverview from './WorkOverview'
import Dashboard from './Dashboard'
import CSUserInfoPanel from '../CSAgentUserInfoPanel'

interface HomeInterfaceProps {
  // Chat state
  selectedChat: string | null
  selectedProject: string | null
  messages: any[]
  setMessages: React.Dispatch<React.SetStateAction<any[]>>
  isTyping: boolean
  setIsTyping: React.Dispatch<React.SetStateAction<boolean>>
  chatHistoryByDate: any[]
  setChatHistoryByDate: React.Dispatch<React.SetStateAction<any[]>>
  
  // Project state
  mockProjects: Project[]
  projectSessions: Array<{id: string, name: string, createdAt: string}>
  
  // Notification state
  notifications: any[]
  activeNotification: string | null
  complexSessions: any[]
  
  // UI state
  isMobile: boolean
  userInfoVisible: boolean
  setUserInfoVisible: React.Dispatch<React.SetStateAction<boolean>>
  colors: Colors
  theme: 'light' | 'dark'
  
  // Avatar and rendering
  dpalsAvatar: any
  renderD_PalAvatar: (avatarOptions: any, colors: any) => string
  mockUserInfo: any
  mockRelatedConversations: any[]
  
  // Event handlers
  onSendMessage: (message: string, files: File[]) => void
  onBackToProject: () => void
  onNotificationCardClick: (notificationId: string) => void
  onNotificationStartChat: (notification: any, typeInfo: any) => void
}

export default function HomeInterface({
  selectedChat,
  selectedProject,
  messages,
  setMessages,
  isTyping,
  setIsTyping,
  chatHistoryByDate,
  setChatHistoryByDate,
  mockProjects,
  projectSessions,
  notifications,
  activeNotification,
  complexSessions,
  isMobile,
  userInfoVisible,
  setUserInfoVisible,
  colors,
  theme,
  dpalsAvatar,
  renderD_PalAvatar,
  mockUserInfo,
  mockRelatedConversations,
  onSendMessage,
  onBackToProject,
  onNotificationCardClick,
  onNotificationStartChat
}: HomeInterfaceProps) {
  
  if (selectedChat) {
    // Show chat interface when a chat is selected
    return (
      <>
        {/* Chat Session Header */}
        {selectedProject && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            marginBottom: '1rem',
            padding: '0.75rem 0.5rem',
            backgroundColor: colors.bgCard,
            borderRadius: '0.5rem',
            border: `1px solid ${colors.borderSubtle}`
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <button
                onClick={onBackToProject}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '32px',
                  height: '32px',
                  borderRadius: '0.25rem',
                  border: 'none',
                  backgroundColor: 'transparent',
                  color: colors.textSecondary,
                  cursor: 'pointer',
                  marginRight: '0.75rem',
                  transition: 'background-color 0.15s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '18px', height: '18px'}}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
                </svg>
              </button>
              <div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: '0.875rem',
                  color: colors.textSecondary,
                  marginBottom: '0.25rem'
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '14px', height: '14px', marginRight: '0.25rem'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                  </svg>
                  {mockProjects.find(p => p.id === selectedProject)?.name}
                </div>
                <div style={{
                  fontSize: '1rem',
                  fontWeight: '500',
                  color: colors.textPrimary
                }}>
                  {complexSessions.find(s => s.id === selectedChat)?.title || 
                   projectSessions.find(s => s.id === selectedChat)?.name || 
                   "Conversation"}
                </div>
              </div>
            </div>
            
            {/* CS Agent User Info Button - Mobile Only */}
            {selectedProject === 'cs-agent' && isMobile && (
              <button
                onClick={() => setUserInfoVisible(true)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '32px',
                  height: '32px',
                  borderRadius: '0.25rem',
                  border: 'none',
                  backgroundColor: 'transparent',
                  color: colors.textSecondary,
                  cursor: 'pointer',
                  transition: 'background-color 0.15s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '18px', height: '18px'}}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                </svg>
              </button>
            )}
          </div>
        )}
        
        {/* Main Chat Content Area */}
        <div style={{
          flex: 1,
          display: 'flex',
          gap: selectedProject === 'cs-agent' ? '1rem' : '0',
          overflow: 'hidden'
        }}>
          {/* Chat Interface Component */}
          <ChatInterface
            messages={messages}
            setMessages={setMessages}
            isTyping={isTyping}
            setIsTyping={setIsTyping}
            selectedChat={selectedChat}
            chatHistoryByDate={chatHistoryByDate}
            setChatHistoryByDate={setChatHistoryByDate}
            colors={colors}
            theme={theme}
            dpalsAvatar={dpalsAvatar}
            renderD_PalAvatar={renderD_PalAvatar}
            isMobile={isMobile}
            selectedProject={selectedProject}
            onSendMessage={onSendMessage}
          />

          {/* Right User Info Panel - CS Agent Only, Desktop */}
          {selectedProject === 'cs-agent' && !isMobile && (
            <div style={{
              width: '300px',
              borderLeft: `1px solid ${colors.borderSubtle}`,
              backgroundColor: colors.bgCard,
              borderRadius: '0.5rem',
              overflow: 'hidden'
            }}>
              <CSUserInfoPanel
                userInfo={mockUserInfo}
                relatedConversations={mockRelatedConversations}
                colors={colors}
                theme={theme}
              />
            </div>
          )}
        </div>

        {/* Mobile User Info Drawer - CS Agent Only */}
        {selectedProject === 'cs-agent' && isMobile && userInfoVisible && (
          <div 
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              zIndex: 1000,
              display: 'flex',
              justifyContent: 'flex-end'
            }}
            onClick={() => setUserInfoVisible(false)}
          >
            <div 
              style={{
                width: '85%',
                maxWidth: '400px',
                backgroundColor: colors.bgMain,
                height: '100%',
                overflow: 'hidden',
                boxShadow: theme === 'light' 
                  ? '-2px 0 8px rgba(0, 0, 0, 0.1)' 
                  : '-2px 0 8px rgba(0, 0, 0, 0.3)',
                display: 'flex',
                flexDirection: 'column'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Drawer Header */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '1rem',
                borderBottom: `1px solid ${colors.borderSubtle}`,
                backgroundColor: colors.bgCard
              }}>
                <h3 style={{
                  margin: 0,
                  fontSize: '1.1rem',
                  fontWeight: '600',
                  color: colors.textPrimary
                }}>
                  User Information
                </h3>
                <button
                  onClick={() => setUserInfoVisible(false)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    borderRadius: '0.25rem',
                    border: 'none',
                    backgroundColor: 'transparent',
                    color: colors.textSecondary,
                    cursor: 'pointer',
                    transition: 'background-color 0.15s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '18px', height: '18px'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {/* Drawer Content */}
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <CSUserInfoPanel
                  userInfo={mockUserInfo}
                  relatedConversations={mockRelatedConversations}
                  colors={colors}
                  theme={theme}
                />
              </div>
            </div>
          </div>
        )}
      </>
    )
  }

  // Show home dashboard when no chat is selected
  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative'
    }}>
      {/* Welcome Header with Input */}
      <WelcomeHeader
        colors={colors}
        theme={theme}
        onSendMessage={onSendMessage}
      />
      
      {/* Work Overview Title */}
      <WorkOverview
        colors={colors}
        theme={theme}
      />
      
      {/* Dashboard Content */}
      <Dashboard
        notifications={notifications}
        activeNotification={activeNotification}
        isMobile={isMobile}
        colors={colors}
        theme={theme}
        complexSessions={complexSessions}
        chatHistoryByDate={chatHistoryByDate}
        onNotificationCardClick={onNotificationCardClick}
        onNotificationStartChat={onNotificationStartChat}
      />
    </div>
  )
} 