'use client'
import React from 'react'
import { Colors } from '../../types'
import NotificationWidget from '../Notification'

interface DashboardProps {
  notifications: any[]
  activeNotification: string | null
  isMobile: boolean
  colors: Colors
  theme: 'light' | 'dark'
  complexSessions: any[]
  chatHistoryByDate: any[]
  onNotificationCardClick: (notificationId: string) => void
  onNotificationStartChat: (notification: any, typeInfo: any) => void
}

export default function Dashboard({
  notifications,
  activeNotification,
  isMobile,
  colors,
  theme,
  complexSessions,
  chatHistoryByDate,
  onNotificationCardClick,
  onNotificationStartChat
}: DashboardProps) {
  return (
    <div style={{
      flex: 1,
      overflowY: 'auto',
      padding: '0 0',
      height: 'calc(100% - 270px)',
      width: '100%'
    }}>
      {/* Notification Display Component */}
      <NotificationWidget
        notifications={notifications}
        activeNotification={activeNotification}
        isMobile={isMobile}
        colors={colors}
        theme={theme}
        complexSessions={complexSessions}
        chatHistoryByDate={chatHistoryByDate}
        onCardClick={onNotificationCardClick}
        onStartChat={onNotificationStartChat}
      />
      
      {/* Future: Add more dashboard widgets here */}
      {/* 
        - Task list widget
        - Workflow templates
        - Recent activity feed
        - Quick actions panel
      */}
    </div>
  )
} 