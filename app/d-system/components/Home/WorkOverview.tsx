'use client'
import React from 'react'
import { Colors } from '../../types'

interface WorkOverviewProps {
  colors: Colors
  theme: 'light' | 'dark'
}

export default function WorkOverview({
  colors,
  theme
}: WorkOverviewProps) {
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '0.75rem',
      position: 'sticky',
      top: 0,
      backgroundColor: colors.bgMain,
      zIndex: 10,
      paddingTop: '0.5rem',
      paddingBottom: '0.5rem',
      boxShadow: theme === 'light' 
        ? '0 2px 4px rgba(0, 0, 0, 0.05)' 
        : '0 2px 4px rgba(0, 0, 0, 0.2)'
    }}>
      <h2 style={{ 
        fontSize: '1.1rem', 
        fontWeight: '600', 
        color: colors.textPrimary,
        margin: 0
      }}>
        Work Overview
      </h2>
    </div>
  )
} 