'use client'
import React from 'react'
import { Colors } from '../../types'
import AIInputBox from '../../../components/AIInputBox'

interface WelcomeHeaderProps {
  colors: Colors
  theme: 'light' | 'dark'
  onSendMessage: (message: string, files: File[]) => void
}

export default function WelcomeHeader({
  colors,
  theme,
  onSendMessage
}: WelcomeHeaderProps) {
  // Get dynamic greeting based on time
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Morning'
    if (hour < 18) return 'Afternoon'
    return 'Evening'
  }

  // Get formatted date
  const getFormattedDate = () => {
    return new Date().toLocaleDateString('en-US', {
      weekday: 'long', 
      month: 'long', 
      day: 'numeric'
    })
  }

  return (
    <div style={{
      padding: '0.5rem 0 1.5rem 0',
      borderBottom: `1px solid ${colors.borderSubtle}`,
      marginBottom: '0.5rem'
    }}>
      {/* Welcome Message */}
      <div style={{
        marginBottom: '1.5rem'
      }}>
        <h1 style={{ 
          fontSize: '1.25rem', 
          fontWeight: 'bold', 
          marginBottom: '0.25rem',
          color: colors.textPrimary
        }}>
          Good {getGreeting()}, David
        </h1>
        <p style={{ 
          fontSize: '0.9rem', 
          color: colors.textTertiary,
          margin: 0
        }}>
          {getFormattedDate()}
        </p>
      </div>
      
      {/* Quick Input Box */}
      <div style={{ 
        boxShadow: theme === 'light' 
          ? '0 2px 8px rgba(0, 0, 0, 0.05)' 
          : '0 2px 8px rgba(0, 0, 0, 0.2)',
        borderRadius: '12px',
        overflow: 'hidden'
      }}>
        <AIInputBox 
          onSend={onSendMessage}
          placeholder="Ask anything or start a new conversation..."
          theme={theme}
          colors={colors}
        />
      </div>
    </div>
  )
} 