'use client'
import React, { useRef } from 'react'
import { Colors, Project, ChatHistoryGroup } from '../../types'

// 导入所有侧边栏子组件
import Sidebar<PERSON>ogo from '../SidebarLogo'
import NewChatButton from '../NewChatButton'
import CSAgentSection from './CSAgentSection'
import ProjectsHeader from '../ProjectsHeader'
import ProjectList from '../ProjectList'
import RecentChatsHeader from '../RecentChatsHeader'
import ChatHistory from '../ChatHistory'
import UserInfoPanel from '../UserInfoPanel'

interface SidebarProps {
  // 基础状态
  colors: Colors
  theme: 'light' | 'dark'
  themeMode: 'light' | 'dark' | 'system'
  isMobile: boolean
  sidebarVisible: boolean

  // 导航状态
  activeNav: string
  selectedChat: string | null
  selectedProject: string | null

  // 聊天历史相关
  chatHistoryByDate: ChatHistoryGroup[]
  hoverChatId: string | null
  showChatMenu: string | null
  showProjectSubmenu: boolean
  menuPosition: { top: number; left: number }

  // 项目相关
  mockProjects: Project[]
  expandedProject: string | null
  hoverProjectId: string | null
  activeProjectMenu: string | null
  projectMenuPosition: { top: number; left: number }
  csAgentConsultations: any[]

  // 用户菜单相关
  showUserMenu: boolean
  isMouseOverProfile: boolean
  isMouseOverMenu: boolean

  // 事件处理函数
  onNewChatClick: () => void
  onCreateProject: () => void
  onProjectClick: (projectId: string) => void
  onProjectHover: (projectId: string) => void
  onProjectLeave: (projectId: string | null) => void
  onProjectMenuClick: (projectId: string, rect: DOMRect) => void
  onChatClick: (chatId: string, projectId?: string) => void
  onChatHover: (chatId: string) => void
  onChatLeave: () => void
  onMenuClick: (chatId: string, position: { top: number; left: number }) => void
  onMenuClose: () => void
  onProjectSubmenuToggle: (show: boolean) => void
  onDeleteChat: (chatId: string) => void
  onAddChatToProject: (chatId: string, projectId: string) => void
  onProfileMouseEnter: () => void
  onProfileMouseLeave: () => void
  onMenuMouseEnter: () => void
  onMenuMouseLeave: () => void
  onThemeModeChange: (mode: 'light' | 'dark' | 'system') => void
  onMyDPalClick: () => void
  onProjectMenuClose: () => void

  // 渲染函数
  renderProjectIcon: (project: any) => JSX.Element
}

export default function Sidebar({
  colors,
  theme,
  themeMode,
  isMobile,
  sidebarVisible,
  activeNav,
  selectedChat,
  selectedProject,
  chatHistoryByDate,
  hoverChatId,
  showChatMenu,
  showProjectSubmenu,
  menuPosition,
  mockProjects,
  expandedProject,
  hoverProjectId,
  activeProjectMenu,
  projectMenuPosition,
  csAgentConsultations,
  showUserMenu,
  isMouseOverProfile,
  isMouseOverMenu,
  onNewChatClick,
  onCreateProject,
  onProjectClick,
  onProjectHover,
  onProjectLeave,
  onProjectMenuClick,
  onChatClick,
  onChatHover,
  onChatLeave,
  onMenuClick,
  onMenuClose,
  onProjectSubmenuToggle,
  onDeleteChat,
  onAddChatToProject,
  onProfileMouseEnter,
  onProfileMouseLeave,
  onMenuMouseEnter,
  onMenuMouseLeave,
  onThemeModeChange,
  onMyDPalClick,
  onProjectMenuClose,
  renderProjectIcon
}: SidebarProps) {
  const projectMenuRef = useRef<HTMLDivElement>(null)

  return (
    <>
      {/* 侧边栏主容器 */}
      <div style={{
        width: '256px',
        backgroundColor: colors.bgCard,
        borderRight: `1px solid ${colors.borderSubtle}`,
        display: 'flex',
        flexDirection: 'column',
        transition: 'background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease',
        height: '100vh',
        position: isMobile ? 'fixed' : 'relative',
        left: 0,
        top: 0,
        zIndex: 999,
        transform: isMobile ? (sidebarVisible ? 'translateX(0)' : 'translateX(-100%)') : 'translateX(0)',
      }}>
        {/* Logo 区域 */}
        <SidebarLogo colors={colors} />
        
        {/* 导航菜单和会话列表的滚动容器 */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          flex: '1 1 auto',
          overflowY: 'auto',
          height: 'calc(100% - 190px)', // 调整高度，确保底部会话不被用户信息窗口遮挡
          paddingBottom: '20px' // 添加底部内边距，提供额外空间
        }}>
          {/* 导航菜单 */}
          <div style={{ padding: '0.5rem 0' }}>
            {/* NewChat导航按钮 */}
            <NewChatButton
              colors={colors}
              activeNav={activeNav}
              selectedChat={selectedChat}
              isMobile={isMobile}
              onClick={onNewChatClick}
            />

            {/* CS Agent 系统工具区域 */}
            <CSAgentSection
              colors={colors}
              theme={theme}
              activeNav={activeNav}
              selectedProject={selectedProject}
              csAgentConsultations={csAgentConsultations}
              onProjectClick={onProjectClick}
            />

            {/* 项目标题和添加按钮 */}
            <ProjectsHeader
              colors={colors}
              onCreateProject={onCreateProject}
            />

            {/* 项目列表 - 始终展开 */}
            <ProjectList
              mockProjects={mockProjects}
              colors={colors}
              theme={theme}
              activeNav={activeNav}
              expandedProject={expandedProject}
              selectedProject={selectedProject}
              hoverProjectId={hoverProjectId}
              activeProjectMenu={activeProjectMenu}
              csAgentConsultations={csAgentConsultations}
              renderProjectIcon={renderProjectIcon}
              onProjectClick={onProjectClick}
              onProjectHover={onProjectHover}
              onProjectLeave={onProjectLeave}
              onProjectMenuClick={onProjectMenuClick}
            />
          </div>
          
          {/* 最近聊天区域 */}
          <RecentChatsHeader colors={colors} />
          
          <ChatHistory
            chatHistoryByDate={chatHistoryByDate}
            selectedChat={selectedChat}
            hoverChatId={hoverChatId}
            showChatMenu={showChatMenu}
            showProjectSubmenu={showProjectSubmenu}
            menuPosition={menuPosition}
            mockProjects={mockProjects}
            colors={colors}
            theme={theme}
            onChatClick={onChatClick}
            onChatHover={onChatHover}
            onChatLeave={onChatLeave}
            onMenuClick={onMenuClick}
            onMenuClose={onMenuClose}
            onProjectSubmenuToggle={onProjectSubmenuToggle}
            onDeleteChat={onDeleteChat}
            onAddChatToProject={onAddChatToProject}
          />
        </div>
        
        {/* 用户资料区域 - 固定在底部 */}
        <UserInfoPanel
          colors={colors}
          theme={theme}
          themeMode={themeMode}
          showUserMenu={showUserMenu}
          isMouseOverProfile={isMouseOverProfile}
          isMouseOverMenu={isMouseOverMenu}
          onProfileMouseEnter={onProfileMouseEnter}
          onProfileMouseLeave={onProfileMouseLeave}
          onMenuMouseEnter={onMenuMouseEnter}
          onMenuMouseLeave={onMenuMouseLeave}
          onThemeModeChange={onThemeModeChange}
          onMyDPalClick={onMyDPalClick}
        />
      </div>

      {/* 项目操作菜单 - 固定定位 */}
      {activeProjectMenu && (
        <div 
          ref={projectMenuRef}
          style={{
            position: 'fixed',
            top: `${projectMenuPosition.top}px`,
            left: `${projectMenuPosition.left}px`,
            backgroundColor: colors.bgCard,
            boxShadow: theme === 'light' 
              ? '0 4px 12px rgba(0, 0, 0, 0.1)' 
              : '0 4px 12px rgba(0, 0, 0, 0.3)',
            border: `1px solid ${colors.borderSubtle}`,
            borderRadius: '6px',
            zIndex: 1000,
            minWidth: '140px',
            fontSize: '14px',
            animation: 'fadeIn 0.15s ease'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <style jsx>{`
            @keyframes fadeIn {
              from { opacity: 0; transform: translateY(-5px); }
              to { opacity: 1; transform: translateY(0); }
            }
          `}</style>
          <div 
            style={{
              padding: '8px 12px',
              display: 'flex',
              alignItems: 'center',
              color: colors.error,
              cursor: 'pointer',
              transition: 'background-color 0.15s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.bgSubtle;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            onClick={() => {
              // 删除项目的逻辑
              const projectToDelete = mockProjects.find(p => p.id === activeProjectMenu);
              if (projectToDelete && confirm(`Are you sure you want to delete "${projectToDelete.name}" project?`)) {
                alert(`Project deleted: ${projectToDelete.name}`);
              }
              onProjectMenuClose();
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginRight: '8px'}}>
              <path d="M3 6h18"></path>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            </svg>
            Delete
          </div>
        </div>
      )}
    </>
  )
} 