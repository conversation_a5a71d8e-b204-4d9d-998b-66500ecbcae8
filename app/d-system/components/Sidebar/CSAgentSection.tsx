'use client'
import React from 'react'
import { Colors, CSConsultation } from '../../types'

interface CSAgentSectionProps {
  colors: Colors
  theme: 'light' | 'dark'
  activeNav: string
  selectedProject: string | null
  csAgentConsultations: CSConsultation[]
  onProjectClick: (projectId: string) => void
}

export default function CSAgentSection({
  colors,
  theme,
  activeNav,
  selectedProject,
  csAgentConsultations,
  onProjectClick
}: CSAgentSectionProps) {
  const isCsSelected = activeNav === 'projects' && selectedProject === 'cs-agent'
  const isI18nSelected = activeNav === 'projects' && selectedProject === 'i18n-agent'
  const hasUnreadCS = csAgentConsultations.filter(c => c.unread).length > 0
  const hasUnreadI18n = csAgentConsultations.filter(c => c.unread).length > 0 // 使用相同的数据源

  const expertProjects = [
    {
      id: 'cs-agent',
      name: 'CS Expert',
      isSelected: isCsSelected,
      hasUnread: hasUnreadCS,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '14px', height: '14px'}}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6" />
          <path strokeLinecap="round" strokeLinejoin="round" d="M15 9l3 3-3 3" />
        </svg>
      ),
      backgroundColor: '#52c41a'
    },
    {
      id: 'i18n-agent',
      name: 'i18n Expert',
      isSelected: isI18nSelected,
      hasUnread: hasUnreadI18n,
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '14px', height: '14px'}}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802" />
        </svg>
      ),
      backgroundColor: '#1890ff'
    }
  ]

  return (
    <div style={{
      marginTop: '0.75rem'
    }}>
      {/* Expert Agent 标题 */}
      <div style={{ 
        padding: '0.5rem 1rem 0.25rem 1rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ 
          color: colors.textTertiary, 
          fontSize: '0.75rem', 
          fontWeight: 'bold', 
          textTransform: 'uppercase', 
          letterSpacing: '0.05em' 
        }}>
          Expert Agent
        </div>
      </div>

      {/* Expert 项目列表 */}
      <div style={{
        marginTop: '0.25rem',
        paddingLeft: '1rem'
      }}>
        {expertProjects.map((project) => (
          <div 
            key={project.id}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '0.5rem 1rem',
              margin: '0.125rem 0.5rem',
              borderRadius: '0.5rem',
              fontSize: '0.875rem',
              color: project.isSelected ? colors.primary : colors.textSecondary,
              backgroundColor: project.isSelected ? colors.bgSubtle : 'transparent',
              cursor: 'pointer',
              transition: 'background-color 0.15s ease, color 0.15s ease',
              position: 'relative',
              overflow: 'hidden',
              height: '36px'
            }}
            onClick={() => onProjectClick(project.id)}
            onMouseEnter={(e) => {
              if (!project.isSelected) {
                e.currentTarget.style.backgroundColor = theme === 'light' ? '#F5F5F5' : '#1A1E23'
                e.currentTarget.style.color = colors.textPrimary
              }
            }}
            onMouseLeave={(e) => {
              if (!project.isSelected) {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = colors.textSecondary
              }
            }}
          >
            {/* 左侧圆点指示符 (仅当前选中项显示) */}
            {project.isSelected && (
              <div style={{
                position: 'absolute',
                left: '0',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '3px',
                height: '60%',
                backgroundColor: colors.primary,
                borderRadius: '0 2px 2px 0'
              }}></div>
            )}
            
            {/* 项目内容 */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              maxWidth: '180px',
              position: 'relative'
            }}>
              {/* 项目图标 */}
              <div style={{
                width: '24px',
                height: '24px',
                backgroundColor: project.backgroundColor,
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '0.5rem',
                color: 'white'
              }}>
                {project.icon}
              </div>
              <span>{project.name}</span>
            </div>

            {/* 未读圆点 */}
            {project.hasUnread && (
              <div style={{
                position: 'absolute',
                right: '8px',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                backgroundColor: colors.primary,
                animation: 'pulse 2s infinite',
                zIndex: 10
              }}>
                <style jsx>{`
                  @keyframes pulse {
                    0% {
                      box-shadow: 0 0 0 0 rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0.4);
                    }
                    70% {
                      box-shadow: 0 0 0 4px rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0);
                    }
                    100% {
                      box-shadow: 0 0 0 0 rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0);
                    }
                  }
                `}</style>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
} 