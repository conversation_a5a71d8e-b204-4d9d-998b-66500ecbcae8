'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChatHistoryGroup, Project, Colors } from '../types';

interface ChatHistoryProps {
  chatHistoryByDate: ChatHistoryGroup[];
  selectedChat: string | null;
  hoverChatId: string | null;
  showChatMenu: string | null;
  showProjectSubmenu: boolean;
  menuPosition: { top: number; left: number };
  mockProjects: Project[];
  colors: Colors;
  theme: 'light' | 'dark';
  onChatClick: (chatId: string) => void;
  onChatHover: (chatId: string) => void;
  onChatLeave: () => void;
  onMenuClick: (chatId: string, position: { top: number; left: number }) => void;
  onMenuClose: () => void;
  onProjectSubmenuToggle: (show: boolean) => void;
  onDeleteChat: (chatId: string) => void;
  onAddChatToProject: (chatId: string, projectId: string) => void;
}

export default function ChatHistory({
  chatHistoryByDate,
  selectedChat,
  hoverChatId,
  showChatMenu,
  showProjectSubmenu,
  menuPosition,
  mockProjects,
  colors,
  theme,
  onChatClick,
  onChatHover,
  onChatLeave,
  onMenuClick,
  onMenuClose,
  onProjectSubmenuToggle,
  onDeleteChat,
  onAddChatToProject
}: ChatHistoryProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onMenuClose();
        onProjectSubmenuToggle(false);
      }
    }

    if (showChatMenu) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showChatMenu, onMenuClose, onProjectSubmenuToggle]);

  return (
    <div style={{ 
      padding: '0.5rem 0',
      display: 'flex', 
      flexDirection: 'column',
      flex: 1
    }}>
      {chatHistoryByDate.map((group, groupIndex) => (
        <div key={groupIndex} style={{ marginBottom: '0.5rem' }}>
          {/* 时间标签 */}
          <div style={{ 
            padding: '0.5rem 1rem 0.25rem 1rem', 
            fontSize: '0.7rem', 
            color: colors.textTertiary,
            fontWeight: '500',
            marginTop: groupIndex > 0 ? '0.5rem' : '0',
            borderTop: groupIndex > 0 ? `1px solid ${colors.borderSubtle}` : 'none',
            paddingTop: groupIndex > 0 ? '0.75rem' : '0.5rem',
            backgroundColor: theme === 'light' ? 'rgba(249, 250, 251, 0.7)' : 'rgba(15, 23, 42, 0.7)'
          }}>
            {group.label}
          </div>
          
          {/* 聊天列表 */}
          {group.chats.map((chat) => (
            <div 
              key={chat.id}
              style={{ 
                padding: '0.4rem 0.75rem',
                margin: '0.125rem 0.5rem',
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                color: selectedChat === chat.id ? colors.primary : colors.textSecondary,
                fontWeight: selectedChat === chat.id ? '500' : 'normal',
                cursor: 'pointer',
                backgroundColor: selectedChat === chat.id ? colors.bgSubtle : 'transparent',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                transition: 'background-color 0.15s ease, color 0.15s ease',
                position: 'relative',
                overflow: 'hidden',
                height: '32px' // 固定高度
              }}
              onClick={() => onChatClick(chat.id)}
              onMouseEnter={(e) => {
                if (selectedChat !== chat.id) {
                  e.currentTarget.style.backgroundColor = theme === 'light' ? '#F5F5F5' : '#1A1E23';
                }
                onChatHover(chat.id);
              }}
              onMouseLeave={(e) => {
                if (selectedChat !== chat.id) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
                if (showChatMenu !== chat.id) {
                  onChatLeave();
                }
              }}
            >
              {/* 左侧圆点指示符 (仅当前选中项显示) */}
              {selectedChat === chat.id && (
                <div style={{
                  position: 'absolute',
                  left: '0',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '3px',
                  height: '60%',
                  backgroundColor: colors.primary,
                  borderRadius: '0 2px 2px 0'
                }}></div>
              )}
              
              <div style={{ 
                overflow: 'hidden', 
                textOverflow: 'ellipsis', 
                whiteSpace: 'nowrap',
                flex: 1,
                paddingLeft: selectedChat !== chat.id ? '6px' : '0',
                transition: 'padding-left 0.15s ease'
              }}>
                {chat.title}
              </div>
              
              {/* 三点菜单图标 */}
              {(hoverChatId === chat.id || showChatMenu === chat.id) && (
                <div 
                  data-three-dots="true"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (showChatMenu === chat.id) {
                      onMenuClose();
                    } else {
                      const rect = e.currentTarget.getBoundingClientRect();
                      onMenuClick(chat.id, {
                        top: rect.top,
                        left: rect.right + 5
                      });
                    }
                    onProjectSubmenuToggle(false);
                  }}
                  style={{
                    width: '24px',
                    height: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginLeft: '4px',
                    transition: 'background-color 0.2s ease',
                    backgroundColor: showChatMenu === chat.id ? colors.bgSubtle : 'transparent',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle;
                  }}
                  onMouseLeave={(e) => {
                    if (showChatMenu !== chat.id) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="5" r="1"></circle>
                    <circle cx="12" cy="12" r="1"></circle>
                    <circle cx="12" cy="19" r="1"></circle>
                  </svg>
                </div>
              )}
              
              {/* 未读消息蓝点 */}
              {chat.hasNewMessage && !(hoverChatId === chat.id || showChatMenu === chat.id) && (
                <div style={{
                  position: 'absolute',
                  right: '8px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '6px',
                  height: '6px',
                  borderRadius: '50%',
                  backgroundColor: colors.primary,
                  animation: 'pulse 2s infinite',
                  zIndex: 10
                }}>
                  <style jsx>{`
                    @keyframes pulse {
                      0% {
                        box-shadow: 0 0 0 0 rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0.4);
                      }
                      70% {
                        box-shadow: 0 0 0 4px rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0);
                      }
                      100% {
                        box-shadow: 0 0 0 0 rgba(${theme === 'light' ? '37, 99, 235' : '59, 130, 246'}, 0);
                      }
                    }
                  `}</style>
                </div>
              )}
            </div>
          ))}
        </div>
      ))}

      {/* 聊天菜单 */}
      {showChatMenu && (
        <div 
          ref={menuRef}
          style={{
            position: 'fixed',
            top: `${menuPosition.top}px`,
            left: `${menuPosition.left}px`,
            backgroundColor: colors.bgCard,
            boxShadow: theme === 'light' 
              ? '0 4px 12px rgba(0, 0, 0, 0.1)' 
              : '0 4px 12px rgba(0, 0, 0, 0.3)',
            border: `1px solid ${colors.borderSubtle}`,
            borderRadius: '6px',
            zIndex: 1000,
            minWidth: '140px',
            fontSize: '14px',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 删除选项 */}
          <div 
            style={{
              padding: '8px 12px',
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              transition: 'background-color 0.15s ease',
              borderBottom: `1px solid ${colors.borderSubtle}`,
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.bgSubtle;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            onClick={() => {
              onDeleteChat(showChatMenu);
              onMenuClose();
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginRight: '8px'}}>
              <path d="M3 6h18"></path>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
            </svg>
            删除
          </div>

          {/* 添加到项目选项 */}
          <div 
            style={{
              padding: '8px 12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              cursor: 'pointer',
              transition: 'background-color 0.15s ease',
              position: 'relative',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.bgSubtle;
              onProjectSubmenuToggle(true);
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <div style={{display: 'flex', alignItems: 'center'}}>
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{marginRight: '8px'}}>
                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
              </svg>
              添加到项目
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 18l6-6-6-6"></path>
            </svg>
            
            {/* 项目二级菜单 */}
            {showProjectSubmenu && (
              <div 
                style={{
                  position: 'absolute',
                  left: '100%',
                  top: '0',
                  backgroundColor: colors.bgCard,
                  boxShadow: theme === 'light' 
                    ? '0 4px 12px rgba(0, 0, 0, 0.1)' 
                    : '0 4px 12px rgba(0, 0, 0, 0.3)',
                  border: `1px solid ${colors.borderSubtle}`,
                  borderRadius: '6px',
                  zIndex: 1001,
                  minWidth: '140px',
                  marginLeft: '6px',
                }}
                onMouseEnter={() => onProjectSubmenuToggle(true)}
                onMouseLeave={() => onProjectSubmenuToggle(false)}
              >
                {mockProjects.map(project => (
                  <div 
                    key={project.id}
                    style={{
                      padding: '8px 12px',
                      cursor: 'pointer',
                      transition: 'background-color 0.15s ease',
                      borderBottom: project.id !== mockProjects[mockProjects.length - 1].id ? 
                        `1px solid ${colors.borderSubtle}` : 'none',
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgSubtle;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                    onClick={() => {
                      onAddChatToProject(showChatMenu, project.id);
                      onMenuClose();
                      onProjectSubmenuToggle(false);
                    }}
                  >
                    {project.name}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 