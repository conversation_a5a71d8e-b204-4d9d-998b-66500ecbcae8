'use client';

import React, { useState, useRef, useEffect } from 'react';

interface NotificationItem {
  id: string;
  type: string;
  title: string;
  time: string;
  priority: string;
  content: any;
  isDeletable?: boolean;
}

interface Live2DCharacterWidgetProps {
  notifications: NotificationItem[];
  onNotificationClick: (notification: NotificationItem) => void;
  onDeleteNotification?: (notificationId: string) => void;
  theme: 'light' | 'dark';
  colors: any;
  avatarOptions: any;
  renderAvatar: (avatarOptions: any, colors: any) => string;
  isHomePage?: boolean;
  onChatNavigation?: (chatId: string) => void;
}

interface SpeechBubbleMessage {
  id: string;
  text: string;
  priority: 'urgent' | 'high' | 'medium' | 'low';
  timestamp: Date;
  chatId?: string;
  actionType?: 'chat' | 'notification';
}

export default function Live2DCharacterWidget({
  notifications,
  onNotificationClick,
  onDeleteNotification,
  theme,
  colors,
  avatarOptions,
  renderAvatar,
  isHomePage = true,
  onChatNavigation
}: Live2DCharacterWidgetProps) {
  const [showNotificationPanel, setShowNotificationPanel] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [speechMessages, setSpeechMessages] = useState<SpeechBubbleMessage[]>([]);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [urgentAlerts, setUrgentAlerts] = useState<SpeechBubbleMessage[]>([]);
  const [showUrgentModal, setShowUrgentModal] = useState(false);
  const widgetRef = useRef<HTMLDivElement>(null);

  // 计算未读通知数量
  const unreadCount = notifications.filter(n => n.priority === 'urgent' || n.priority === 'high' || n.priority === 'medium').length;
  const hasNotifications = notifications.length > 0;

  // 点击外部关闭弹窗
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (widgetRef.current && !widgetRef.current.contains(event.target as Node)) {
        setShowNotificationPanel(false);
      }
    }

    if (showNotificationPanel) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showNotificationPanel]);

  // 初始化语音消息和紧急警报
  useEffect(() => {
    // 使用统一的urgent消息数据源
    const { getSpeechBubbleMessages, getUrgentAlerts } = require('../lib/urgentMessages');
    
    // 获取气泡消息（前3个用于轮播）
    const bubbleMessages = getSpeechBubbleMessages().slice(0, 3);
    setSpeechMessages(bubbleMessages);
    
    // 获取紧急警报弹窗消息
    const alertMessages = getUrgentAlerts();
    setUrgentAlerts(alertMessages);
    
    // 如果有紧急警报，延迟显示弹窗
    // 移除自动触发，改为点击触发
    // if (alertMessages.length > 0) {
    //   setTimeout(() => setShowUrgentModal(true), 2000);
    // }
  }, []);

  // 自动切换消息
  useEffect(() => {
    if (speechMessages.length === 0) return;
    
    const interval = setInterval(() => {
      if (!isHovered && !showNotificationPanel) {
        setCurrentMessageIndex((prev) => (prev + 1) % speechMessages.length);
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 2000);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [speechMessages, isHovered, showNotificationPanel]);

  // 自动动画效果
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isHovered && !showNotificationPanel) {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 2000);
      }
    }, 8000);

    return () => clearInterval(interval);
  }, [isHovered, showNotificationPanel]);

  const handleCharacterClick = () => {
    setShowNotificationPanel(!showNotificationPanel);
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 1000);
  };

  const handleNotificationItemClick = (notification: NotificationItem) => {
    onNotificationClick(notification);
    setShowNotificationPanel(false);
  };

  const handleBubbleClick = (message: SpeechBubbleMessage) => {
    if (message.actionType === 'chat' && message.chatId && onChatNavigation) {
      onChatNavigation(message.chatId);
    }
  };

  const handleCloseUrgentAlert = () => {
    const remainingAlerts = urgentAlerts.slice(1);
    setUrgentAlerts(remainingAlerts);
    
    if (remainingAlerts.length === 0) {
      setShowUrgentModal(false);
    }
  };

  const handleUrgentChatClick = (chatId: string) => {
    if (onChatNavigation) {
      onChatNavigation(chatId);
      setShowUrgentModal(false);
      setUrgentAlerts([]);
    }
  };

  const formatTime = (timeStr: string) => {
    try {
      const date = new Date(timeStr);
      return date.toLocaleString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return timeStr;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return '#ef4444';
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return colors.textSecondary || '#666';
    }
  };

  return (
    <div 
      ref={widgetRef}
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 9999,
        display: 'flex',
        alignItems: 'flex-start',
        gap: '12px',
      }}
    >
      {/* 语音气泡 - 只在home页面显示，通知面板打开时隐藏 */}
      {isHomePage && speechMessages.length > 0 && speechMessages[currentMessageIndex] && !showNotificationPanel && (
        <div
          onClick={() => handleBubbleClick(speechMessages[currentMessageIndex])}
          style={{
            position: 'relative',
            maxWidth: '280px',
            backgroundColor: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high')
              ? '#fef2f2' : (colors.bgCard || '#fff'),
            borderRadius: '16px',
            padding: '16px 20px',
            border: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high')
              ? '2px solid #ef4444' : `1px solid ${colors.borderSubtle || '#e5e7eb'}`,
            boxShadow: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high')
              ? theme === 'dark' 
                ? '0 8px 24px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)' 
                : '0 8px 24px rgba(239, 68, 68, 0.3), 0 0 15px rgba(239, 68, 68, 0.1)'
              : theme === 'dark' 
                ? '0 4px 16px rgba(0, 0, 0, 0.2)' 
                : '0 4px 16px rgba(0, 0, 0, 0.1)',
            animation: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high')
              ? 'urgentPulse 1s ease-in-out infinite, fadeInBounce 0.5s ease-out'
              : 'fadeInBounce 0.5s ease-out',
            marginTop: '20px',
            cursor: speechMessages[currentMessageIndex].actionType === 'chat' ? 'pointer' : 'default',
            transition: 'all 0.2s ease',
          }}
          onMouseEnter={(e) => {
            if (speechMessages[currentMessageIndex].actionType === 'chat') {
              e.currentTarget.style.transform = 'scale(1.02)';
              e.currentTarget.style.boxShadow = theme === 'dark' 
                ? '0 6px 20px rgba(0, 0, 0, 0.3)' 
                : '0 6px 20px rgba(0, 0, 0, 0.15)';
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.boxShadow = theme === 'dark' 
              ? '0 4px 16px rgba(0, 0, 0, 0.2)' 
              : '0 4px 16px rgba(0, 0, 0, 0.1)';
          }}
        >
          {/* 气泡箭头 */}
          <div
            style={{
              position: 'absolute',
              right: '-8px',
              top: '20px',
              width: 0,
              height: 0,
              borderLeft: `8px solid ${colors.bgCard || '#fff'}`,
              borderTop: '8px solid transparent',
              borderBottom: '8px solid transparent',
            }}
          />
          
          {/* URGENT标签 */}
          {speechMessages[currentMessageIndex].priority === 'urgent' && (
            <div 
              onClick={() => setShowUrgentModal(true)}
              style={{
                position: 'absolute',
                top: '-8px',
                left: '16px',
                backgroundColor: '#ef4444',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '8px',
                fontSize: '10px',
                fontWeight: 'bold',
                textTransform: 'uppercase',
                letterSpacing: '0.5px',
                boxShadow: '0 2px 8px rgba(239, 68, 68, 0.3)',
                animation: 'pulse 2s infinite',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.1)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.5)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(239, 68, 68, 0.3)';
              }}
            >
              URGENT
            </div>
          )}

          {/* 消息内容 */}
          <div style={{
            fontSize: '14px',
            lineHeight: '1.4',
            color: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high') ? '#dc2626' : (colors.textPrimary || '#000'),
            fontWeight: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high') ? '600' : '500',
            marginTop: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high') ? '8px' : '0',
          }}>
            {speechMessages[currentMessageIndex].text}
          </div>
          
          {/* 优先级指示器 */}
          <div style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: (speechMessages[currentMessageIndex].priority === 'urgent' || speechMessages[currentMessageIndex].priority === 'high') ? '#ef4444' : '#f59e0b',
            animation: 'pulse 2s infinite',
          }} />
          
          {/* 说话动画点点 */}
          <div style={{
            display: 'flex',
            gap: '4px',
            marginTop: '8px',
            justifyContent: 'flex-end',
          }}>
            <div style={{
              width: '4px',
              height: '4px',
              borderRadius: '50%',
              backgroundColor: colors.textSecondary || '#666',
              animation: 'bounce 1.4s infinite ease-in-out',
              animationDelay: '0s',
            }} />
            <div style={{
              width: '4px',
              height: '4px',
              borderRadius: '50%',
              backgroundColor: colors.textSecondary || '#666',
              animation: 'bounce 1.4s infinite ease-in-out',
              animationDelay: '0.2s',
            }} />
            <div style={{
              width: '4px',
              height: '4px',
              borderRadius: '50%',
              backgroundColor: colors.textSecondary || '#666',
              animation: 'bounce 1.4s infinite ease-in-out',
              animationDelay: '0.4s',
            }} />
          </div>
        </div>
      )}
      {/* 通知面板 - 在人物左侧展开 */}
      {showNotificationPanel && (
        <div
          style={{
            width: '360px',
            maxHeight: '400px',
            backgroundColor: colors.bgCard || '#fff',
            borderRadius: '16px',
            border: `1px solid ${colors.borderSubtle || '#e5e7eb'}`,
            boxShadow: theme === 'dark' 
              ? '0 8px 24px rgba(0, 0, 0, 0.3)' 
              : '0 8px 24px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
            backdropFilter: 'blur(10px)',
            animation: 'slideInFromRight 0.3s ease-out',
            transformOrigin: 'top right',
          }}
        >
          {/* 面板头部 */}
          <div style={{
            padding: '20px 24px 16px',
            borderBottom: `1px solid ${colors.borderSubtle || '#e5e7eb'}`,
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '8px',
            }}>
              <h3 style={{
                margin: 0,
                fontSize: '18px',
                fontWeight: '600',
                color: colors.textPrimary || '#000',
              }}>
                Notifications
              </h3>
              <span style={{
                fontSize: '14px',
                color: colors.textSecondary || '#666',
                backgroundColor: colors.bgSubtle || '#f3f4f6',
                padding: '4px 8px',
                borderRadius: '12px',
              }}>
                {notifications.length} items
              </span>
            </div>
          </div>

          {/* 通知列表 */}
          <div style={{
            maxHeight: '300px',
            overflowY: 'auto',
            padding: '8px',
          }}>
            {notifications.length === 0 ? (
              <div style={{
                padding: '40px 24px',
                textAlign: 'center',
                color: colors.textSecondary || '#666',
              }}>
                <div style={{
                  fontSize: '48px',
                  marginBottom: '16px',
                  opacity: 0.5,
                }}>
                  🔔
                </div>
                <p style={{ margin: 0, fontSize: '14px' }}>
                  No notifications at the moment
                </p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  style={{
                    padding: '16px',
                    margin: '8px',
                    backgroundColor: colors.bgElevated || '#f9fafb',
                    borderRadius: '12px',
                    border: `1px solid ${colors.borderSubtle || '#e5e7eb'}`,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    position: 'relative',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle || '#f3f4f6';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    // 显示删除按钮
                    const deleteBtn = e.currentTarget.querySelector('.delete-btn') as HTMLElement;
                    if (deleteBtn) deleteBtn.style.opacity = '1';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = colors.bgElevated || '#f9fafb';
                    e.currentTarget.style.transform = 'translateY(0)';
                    // 隐藏删除按钮
                    const deleteBtn = e.currentTarget.querySelector('.delete-btn') as HTMLElement;
                    if (deleteBtn) deleteBtn.style.opacity = '0';
                  }}
                >
                  <div 
                    onClick={() => handleNotificationItemClick(notification)}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '8px',
                    }}
                  >
                    <div style={{
                      fontSize: '14px',
                      fontWeight: '600',
                      color: colors.textPrimary || '#000',
                      lineHeight: '1.4',
                    }}>
                      {notification.title}
                    </div>
                    
                    <div style={{
                      fontSize: '12px',
                      color: colors.textSecondary || '#666',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}>
                      {(notification.priority === 'urgent' || notification.priority === 'high') && (
                        <span style={{
                          backgroundColor: getPriorityColor(notification.priority),
                          color: '#fff',
                          padding: '2px 6px',
                          borderRadius: '4px',
                          fontSize: '10px',
                          fontWeight: '500',
                          textTransform: 'uppercase',
                        }}>
                          URGENT
                        </span>
                      )}
                      <span>{formatTime(notification.time)}</span>
                    </div>
                  </div>

                  {/* 删除按钮 - 只对可删除的通知显示 */}
                  {notification.isDeletable && onDeleteNotification && (
                    <button
                      className="delete-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteNotification(notification.id);
                      }}
                      style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        width: '20px',
                        height: '20px',
                        borderRadius: '50%',
                        border: 'none',
                        backgroundColor: '#ef4444',
                        color: 'white',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        opacity: '0',
                        transition: 'all 0.2s ease',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#dc2626';
                        e.currentTarget.style.transform = 'scale(1.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = '#ef4444';
                        e.currentTarget.style.transform = 'scale(1)';
                      }}
                    >
                      ×
                    </button>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Live2D 人物容器 */}
      <div
        onClick={handleCharacterClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          position: 'relative',
          width: isHomePage ? '80px' : '40px',
          height: isHomePage ? '80px' : '40px',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          transform: isHovered ? 'scale(1.05)' : 'scale(1)',
        }}
      >
        {/* 人物背景圆圈 */}
        <div style={{
          position: 'absolute',
          inset: 0,
          borderRadius: '50%',
          backgroundColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
          border: `2px solid ${theme === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.1)'}`,
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s ease',
          transform: isAnimating ? 'scale(1.1)' : 'scale(1)',
          boxShadow: isHovered 
            ? (theme === 'dark' ? '0 8px 24px rgba(255, 255, 255, 0.1)' : '0 8px 24px rgba(0, 0, 0, 0.1)')
            : 'none',
        }} />

        {/* 用户自定义头像或铃铛图标 */}
        {isHomePage ? (
          <div style={{
            position: 'absolute',
            inset: '8px',
            borderRadius: '50%',
            overflow: 'hidden',
            transition: 'all 0.3s ease',
            transform: isAnimating ? 'rotate(10deg) scale(1.1)' : 'rotate(0deg) scale(1)',
          }}>
            <img 
              src={renderAvatar(avatarOptions, colors)}
              alt="Character Avatar"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          </div>
        ) : (
          <div style={{
            position: 'absolute',
            inset: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '18px',
            transition: 'all 0.3s ease',
            transform: isAnimating ? 'rotate(10deg) scale(1.1)' : 'rotate(0deg) scale(1)',
            color: theme === 'dark' ? '#fff' : '#000',
          }}>
            🔔
          </div>
        )}

        {/* 通知徽章 */}
        {hasNotifications && unreadCount > 0 && (
          <div style={{
            position: 'absolute',
            top: '-4px',
            right: '-4px',
            minWidth: isHomePage ? '24px' : '18px',
            height: isHomePage ? '24px' : '18px',
            borderRadius: isHomePage ? '12px' : '9px',
            backgroundColor: '#ef4444',
            color: '#fff',
            fontSize: isHomePage ? '12px' : '10px',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: `2px solid ${colors.bgCard || '#fff'}`,
            animation: unreadCount > 0 ? 'pulse 2s infinite' : 'none',
          }}>
            {unreadCount > 99 ? '99+' : unreadCount}
          </div>
        )}


      </div>

      {/* 紧急任务全屏弹窗 */}
      {showUrgentModal && urgentAlerts.length > 0 && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            zIndex: 10000,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backdropFilter: 'blur(8px)',
            animation: 'fadeIn 0.3s ease-out',
          }}
        >
          <div
            style={{
              backgroundColor: colors.bgCard || '#fff',
              borderRadius: '20px',
              padding: '40px',
              maxWidth: '600px',
              width: '90%',
              maxHeight: '80vh',
              border: '3px solid #ef4444',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(239, 68, 68, 0.3)',
              animation: 'urgentModalSlide 0.5s ease-out',
              position: 'relative',
              overflow: 'auto',
            }}
          >
            {/* 紧急标题 */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              marginBottom: '24px',
            }}>
              <div style={{
                width: '48px',
                height: '48px',
                backgroundColor: '#ef4444',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                animation: 'pulse 1.5s infinite',
              }}>
                ⚠️
              </div>
              <div>
                <h2 style={{
                  margin: 0,
                  fontSize: '24px',
                  fontWeight: 'bold',
                  color: '#ef4444',
                  textTransform: 'uppercase',
                  letterSpacing: '1px',
                }}>
                  URGENT ALERT
                </h2>
                <p style={{
                  margin: 0,
                  fontSize: '14px',
                  color: colors.textSecondary || '#666',
                }}>
                  {urgentAlerts.length} critical task{urgentAlerts.length > 1 ? 's' : ''} requiring immediate attention
                </p>
              </div>
            </div>

            {/* 当前紧急任务 */}
            <div style={{
              backgroundColor: '#fef2f2',
              border: '2px solid #ef4444',
              borderRadius: '16px',
              padding: '24px',
              marginBottom: '24px',
              position: 'relative',
            }}>
              <div style={{
                position: 'absolute',
                top: '-12px',
                left: '20px',
                backgroundColor: '#ef4444',
                color: 'white',
                padding: '4px 12px',
                borderRadius: '12px',
                fontSize: '12px',
                fontWeight: 'bold',
                textTransform: 'uppercase',
              }}>
                Task {urgentAlerts.length} of {urgentAlerts.length}
              </div>

              <div style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#dc2626',
                lineHeight: '1.4',
                marginBottom: '16px',
                marginTop: '8px',
              }}>
                {urgentAlerts[0].text}
              </div>

              <div style={{
                fontSize: '12px',
                color: '#666',
                marginBottom: '20px',
              }}>
                Timestamp: {urgentAlerts[0].timestamp.toLocaleString()}
              </div>

              {/* 操作按钮 */}
              <div style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'flex-end',
              }}>
                <button
                  onClick={handleCloseUrgentAlert}
                  style={{
                    backgroundColor: colors.bgSubtle || '#f3f4f6',
                    color: colors.textSecondary || '#666',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '8px 16px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                  }}
                >
                  {urgentAlerts.length > 1 ? 'Next Alert' : 'Dismiss'}
                </button>
                
                <button
                  onClick={() => handleUrgentChatClick(urgentAlerts[0].chatId!)}
                  style={{
                    backgroundColor: '#ef4444',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '8px 20px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 4px 8px rgba(239, 68, 68, 0.3)',
                  }}
                >
                  🚀 Start Chat
                </button>
              </div>
            </div>

            {/* 队列信息 */}
            {urgentAlerts.length > 1 && (
              <div style={{
                backgroundColor: colors.bgSubtle || '#f9fafb',
                borderRadius: '12px',
                padding: '16px',
                border: `1px solid ${colors.borderSubtle || '#e5e7eb'}`,
              }}>
                <div style={{
                  fontSize: '14px',
                  fontWeight: '600',
                  color: colors.textPrimary || '#000',
                  marginBottom: '8px',
                }}>
                  Pending Alerts Queue:
                </div>
                {urgentAlerts.slice(1).map((alert, index) => (
                  <div key={alert.id} style={{
                    fontSize: '12px',
                    color: colors.textSecondary || '#666',
                    padding: '4px 0',
                    borderBottom: index < urgentAlerts.length - 2 ? `1px solid ${colors.borderSubtle || '#e5e7eb'}` : 'none',
                  }}>
                    {index + 2}. {alert.text.slice(0, 60)}...
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* CSS 动画 */}
      <style jsx>{`
        @keyframes slideInFromRight {
          from {
            opacity: 0;
            transform: translateX(20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
        }

        @keyframes fadeInBounce {
          0% {
            opacity: 0;
            transform: translateX(-20px) scale(0.8);
          }
          60% {
            opacity: 1;
            transform: translateX(5px) scale(1.05);
          }
          100% {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }

        @keyframes bounce {
          0%, 80%, 100% {
            transform: scale(0);
          }
          40% {
            transform: scale(1);
          }
        }

        @keyframes urgentPulse {
          0%, 100% {
            transform: scale(1);
            box-shadow: 0 8px 24px rgba(239, 68, 68, 0.3), 0 0 15px rgba(239, 68, 68, 0.1);
          }
          50% {
            transform: scale(1.02);
            box-shadow: 0 12px 32px rgba(239, 68, 68, 0.4), 0 0 25px rgba(239, 68, 68, 0.2);
          }
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        @keyframes urgentModalSlide {
          from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

      `}</style>
    </div>
  );
} 