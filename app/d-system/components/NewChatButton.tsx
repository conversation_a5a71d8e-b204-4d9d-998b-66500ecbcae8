'use client'
import React from 'react';

interface NewChatButtonProps {
  colors: {
    primary: string;
    textPrimary: string;
  };
  activeNav: string;
  selectedChat: string | null;
  isMobile: boolean;
  onClick: () => void;
}

/**
 * NewChat导航按钮组件
 * 点击后切换到主页并重置选中的聊天
 */
export default function NewChatButton({ 
  colors, 
  activeNav, 
  selectedChat, 
  isMobile, 
  onClick 
}: NewChatButtonProps) {
  const isActive = activeNav === 'home' && !selectedChat;

  return (
    <button
      onClick={onClick}
      style={{ 
        width: 'calc(100% - 2rem)', 
        margin: '0.25rem 1rem',
        justifyContent: 'flex-start',
        borderRadius: '0.5rem',
        backgroundColor: isActive ? colors.primary : 'transparent',
        color: isActive ? 'white' : colors.textPrimary,
        border: 'none',
        padding: '10px 16px',
        fontSize: '14px',
        fontWeight: '500',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        display: 'flex',
        alignItems: 'center'
      }}
    >
      {/* 聊天气泡图标 */}
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        fill="none" 
        viewBox="0 0 24 24" 
        strokeWidth={1.5} 
        stroke="currentColor" 
        style={{
          width: '1.25rem', 
          height: '1.25rem', 
          marginRight: '0.5rem'
        }}
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" 
        />
      </svg>
      <span>newchat</span>
    </button>
  );
} 