'use client'
import React from 'react';
import { Project, CSConsultation } from '../types';

interface ProjectListProps {
  mockProjects: Project[];
  colors: {
    primary: string;
    textSecondary: string;
    textPrimary: string;
    textTertiary: string;
    bgSubtle: string;
  };
  theme: string;
  activeNav: string;
  expandedProject: string | null;
  selectedProject: string | null;
  hoverProjectId: string | null;
  activeProjectMenu: string | null;
  csAgentConsultations: CSConsultation[];
  renderProjectIcon: (project: Project) => React.ReactNode;
  onProjectClick: (projectId: string) => void;
  onProjectHover: (projectId: string) => void;
  onProjectLeave: (projectId: string | null) => void;
  onProjectMenuClick: (projectId: string, rect: DOMRect) => void;
}

/**
 * 项目列表组件
 * 包含项目渲染、交互效果、菜单控制等复杂逻辑
 */
export default function ProjectList({
  mockProjects,
  colors,
  theme,
  activeNav,
  expandedProject,
  selectedProject,
  hoverProjectId,
  activeProjectMenu,
  csAgentConsultations,
  renderProjectIcon,
  onProjectClick,
  onProjectHover,
  onProjectLeave,
  onProjectMenuClick
}: ProjectListProps) {
  return (
    <div style={{
      marginTop: '0.25rem',
      paddingLeft: '1rem',
      display: 'flex',
      flexDirection: 'column',
      gap: '0.4rem',
      maxHeight: '200px',
      overflowY: 'auto'
    }}>
      {mockProjects.filter(project => project.id !== 'cs-agent').map(project => {
        const isSelected = activeNav === 'projects' && (expandedProject === project.id || selectedProject === project.id);
        const showMenu = !project.isSpecial && (hoverProjectId === project.id || activeProjectMenu === project.id);

        return (
          <div 
            key={project.id} 
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '0.5rem 1rem',
              margin: '0.125rem 0.5rem',
              borderRadius: '0.5rem',
              fontSize: '0.875rem',
              color: isSelected ? colors.primary : colors.textSecondary,
              backgroundColor: isSelected ? colors.bgSubtle : 'transparent',
              cursor: 'pointer',
              transition: 'background-color 0.15s ease, color 0.15s ease',
              position: 'relative',
              overflow: 'hidden',
              height: '36px' // 固定高度
            }}
            onClick={() => onProjectClick(project.id)}
            onMouseEnter={(e) => {
              if (!isSelected) {
                e.currentTarget.style.backgroundColor = theme === 'light' ? '#F5F5F5' : '#1A1E23';
                e.currentTarget.style.color = colors.textPrimary;
              }
              onProjectHover(project.id);
            }}
            onMouseLeave={(e) => {
              if (!isSelected) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = colors.textSecondary;
              }
              // 清除hover项目ID，除非活动菜单正在显示
              if (activeProjectMenu !== project.id) {
                onProjectLeave(null);
              }
            }}
          >
            {/* 左侧圆点指示符 (仅当前选中项显示) */}
            {isSelected && (
              <div style={{
                position: 'absolute',
                left: '0',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '3px',
                height: '60%',
                backgroundColor: colors.primary,
                borderRadius: '0 2px 2px 0'
              }}></div>
            )}
            
            {/* 项目内容 */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              maxWidth: '180px',
              position: 'relative'
            }}>
              {renderProjectIcon(project)}
              <span>{project.name}</span>
            </div>



            {/* 三点菜单 - 仅在hover项目或显示菜单时可见，且不是特殊项目 */}
            {showMenu && (
              <div 
                data-project-menu="true"
                onClick={(e) => {
                  e.stopPropagation();
                  const rect = e.currentTarget.getBoundingClientRect();
                  onProjectMenuClick(project.id, rect);
                }}
                style={{
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: colors.textTertiary,
                  borderRadius: '4px',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease',
                  backgroundColor: activeProjectMenu === project.id ? colors.bgSubtle : 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  if (activeProjectMenu !== project.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  strokeWidth={1.5} 
                  stroke="currentColor" 
                  style={{width: '0.875rem', height: '0.875rem'}}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" />
                </svg>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
} 