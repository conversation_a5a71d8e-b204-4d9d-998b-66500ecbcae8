'use client'
import React from 'react'
import { Colors, CSConsultation } from '../../types'
import CSAgentSystem from './CSAgent'

interface SystemProjectInterfaceProps {
  projectId: string
  selectedChat: string | null
  colors: Colors
  onConsultationClick: (consultationId: string) => void
  
  // 系统特定的数据和配置
  systemData?: {
    csAgent?: {
      consultations: CSConsultation[]
    }
    // 未来可以添加其他系统：
    // gameSupport?: { tickets: GameTicket[] }
    // marketing?: { campaigns: Campaign[] }
  }
}

export default function SystemProjectInterface({
  projectId,
  selectedChat,
  colors,
  onConsultationClick,
  systemData
}: SystemProjectInterfaceProps) {

  // 渲染系统特定的内容
  const renderSystemContent = () => {
    if ((projectId === 'cs-agent' || projectId === 'i18n-agent') && systemData?.csAgent) {
      return (
        <CSAgentSystem
          consultations={systemData.csAgent.consultations}
          selectedChat={selectedChat}
          colors={colors}
          onConsultationClick={onConsultationClick}
          projectType={projectId}
        />
      )
    }
    
    // 未来可以添加其他系统的内容渲染
    // if (projectId === 'game-support' && systemData?.gameSupport) { ... }
    
    return (
      <div style={{
        padding: '2rem',
        textAlign: 'center',
        color: colors.textTertiary,
        backgroundColor: colors.bgCard,
        borderRadius: '8px',
        border: `1px solid ${colors.borderSubtle}`
      }}>
        System content not available for this project
      </div>
    )
  }

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 系统特定内容 */}
      {renderSystemContent()}
    </div>
  )
} 