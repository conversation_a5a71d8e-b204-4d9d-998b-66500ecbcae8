'use client'
import React from 'react'
import { Colors } from '../../../types'

interface CSAgentFiltersProps {
  searchText: string
  onSearchChange: (text: string) => void
  selectedTab: 'all' | 'vip' | 'non-vip'
  onTabChange: (tab: 'all' | 'vip' | 'non-vip') => void
  waitingFilter: boolean
  onWaitingFilterChange: (checked: boolean) => void
  poorFilter: boolean
  onPoorFilterChange: (checked: boolean) => void
  colors: Colors
  projectType: string
}

export default function CSAgentFilters({
  searchText,
  onSearchChange,
  selectedTab,
  onTabChange,
  waitingFilter,
  onWaitingFilterChange,
  poorFilter,
  onPoorFilterChange,
  colors,
  projectType
}: CSAgentFiltersProps) {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '0.75rem',
      marginBottom: '1rem',
      padding: '0.75rem',
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      border: `1px solid ${colors.borderSubtle}`
    }}>
      {/* 搜索框 */}
      <div>
        <input
          type="text"
          placeholder={projectType === 'i18n-agent' ? "Search by title or game..." : "Search by title, R rank, or game..."}
          value={searchText}
          onChange={(e) => onSearchChange(e.target.value)}
          style={{
            width: '100%',
            padding: '0.5rem',
            borderRadius: '6px',
            border: `1px solid ${colors.borderStrong}`,
            backgroundColor: colors.bgMain,
            color: colors.textPrimary,
            fontSize: '0.875rem'
          }}
        />
      </div>

      {/* 标签筛选 - 根据项目类型显示不同筛选器 */}
      <div style={{
        display: 'flex',
        gap: '0.5rem',
        flexWrap: 'wrap',
        alignItems: 'center'
      }}>
        {/* CS Expert的完整筛选器 */}
        {projectType !== 'i18n-agent' && (
          <>
            {/* VIP筛选器 */}
            {(['all', 'vip', 'non-vip'] as const).map((tab) => (
              <button
                key={tab}
                onClick={() => onTabChange(tab)}
                style={{
                  padding: '0.375rem 0.75rem',
                  borderRadius: '20px',
                  border: `1px solid ${colors.borderStrong}`,
                  backgroundColor: selectedTab === tab ? colors.primary : 'transparent',
                  color: selectedTab === tab ? 'white' : colors.textSecondary,
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (selectedTab !== tab) {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle;
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedTab !== tab) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                {tab === 'all' ? 'All' : 
                 tab === 'vip' ? 'VIP' : 
                 'Non-VIP'}
              </button>
            ))}

            {/* 分割线 */}
            <div style={{
              width: '1px',
              height: '20px',
              backgroundColor: colors.borderStrong,
              margin: '0 0.25rem'
            }}></div>
          </>
        )}

        {/* Waiting筛选器 - 两个项目都显示 */}
        <button
          onClick={() => onWaitingFilterChange(!waitingFilter)}
          style={{
            padding: '0.375rem 0.75rem',
            borderRadius: '20px',
            border: `1px solid ${colors.borderStrong}`,
            backgroundColor: waitingFilter ? colors.primary : 'transparent',
            color: waitingFilter ? 'white' : colors.textSecondary,
            fontSize: '0.75rem',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            if (!waitingFilter) {
              e.currentTarget.style.backgroundColor = colors.bgSubtle;
            }
          }}
          onMouseLeave={(e) => {
            if (!waitingFilter) {
              e.currentTarget.style.backgroundColor = 'transparent';
            }
          }}
        >
          Waiting
        </button>

        {/* Poor筛选器 - 仅CS Expert显示 */}
        {projectType !== 'i18n-agent' && (
          <button
            onClick={() => onPoorFilterChange(!poorFilter)}
            style={{
              padding: '0.375rem 0.75rem',
              borderRadius: '20px',
              border: `1px solid ${colors.borderStrong}`,
              backgroundColor: poorFilter ? colors.primary : 'transparent',
              color: poorFilter ? 'white' : colors.textSecondary,
              fontSize: '0.75rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              if (!poorFilter) {
                e.currentTarget.style.backgroundColor = colors.bgSubtle;
              }
            }}
            onMouseLeave={(e) => {
              if (!poorFilter) {
                e.currentTarget.style.backgroundColor = 'transparent';
              }
            }}
          >
            Poor
          </button>
        )}
      </div>
    </div>
  )
} 