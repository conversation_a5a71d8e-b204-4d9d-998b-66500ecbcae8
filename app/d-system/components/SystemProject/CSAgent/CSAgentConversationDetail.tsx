'use client'
import React, { useState } from 'react'
import { Colors, CSConsultation, UserInfo, RelatedConversation } from '../../../types'
import CSAgentUserInfoPanel from '../../CSAgentUserInfoPanel'
import AIMessage from '../../../../components/AIMessage'
import UserMessage from '../../../../components/UserMessage'
import AIInputBox from '../../../../components/AIInputBox'

interface CSAgentConversationDetailProps {
  selectedChat: string | null
  consultation: CSConsultation | null
  chatData: any
  colors: Colors
  theme: 'light' | 'dark'
  onSendMessage?: (message: string, files: File[]) => void
  dpalsAvatar?: any
  renderD_PalAvatar?: (avatarOptions: any, colors: any) => string
  projectType?: string
}

export default function CSAgentConversationDetail({
  selectedChat,
  consultation,
  chatData,
  colors,
  theme,
  onSendMessage,
  dpalsAvatar,
  renderD_PalAvatar,
  projectType
}: CSAgentConversationDetailProps) {
  const [showUserInfo, setShowUserInfo] = useState(projectType === 'cs-expert')

  // 模拟用户信息数据（实际项目中应该从 API 获取）
  const mockUserInfo: UserInfo = {
    game: {
      name: consultation?.game || 'Gate',
      description: 'Fantasy strategy game with card collection mechanics',
      team: 'Game Team A'
    },
    user: {
      g123Id: 'G123456789',
      serverId: 'S001',
      gameUid: 'UID789123',
      roleId: 'R001',
      im: 'telegram:@' + (consultation?.userName?.toLowerCase()?.replace(' ', '_') || 'user'),
      rank: consultation?.userRank?.toString() || '0',
      amount: '$' + Math.floor((consultation?.userRank || 0) * 0.1).toFixed(2),
      vipLevel: consultation?.isVip ? 'VIP' : 'Standard',
      system: 'iOS 16.5 / Gate v2.1.3'
    }
  }

  const mockRelatedConversations: RelatedConversation[] = [
    {
      id: 'conv-001',
      firstUserMessage: 'I have a payment issue with my recent purchase',
      endTime: '2023-05-10 15:30:22'
    },
    {
      id: 'conv-002',
      firstUserMessage: 'I need help with account verification',
      endTime: '2023-05-08 10:45:15'
    }
  ]

  if (!selectedChat || !consultation) {
    return (
      <div style={{
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: colors.bgCard,
        borderRadius: '8px',
        border: `1px solid ${colors.borderSubtle}`
      }}>
        <div style={{
          textAlign: 'center',
          color: colors.textTertiary
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            margin: '0 auto 1rem',
            backgroundColor: colors.bgSubtle,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '24px', height: '24px'}}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" />
            </svg>
          </div>
          <h3 style={{
            margin: '0 0 0.5rem',
            fontSize: '1rem',
            fontWeight: '600',
            color: colors.textSecondary
          }}>
            Select a Consultation
          </h3>
          <p style={{
            margin: 0,
            fontSize: '0.875rem',
            color: colors.textTertiary
          }}>
            Choose a consultation from the list to view details
          </p>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      flex: 1,
      display: 'flex',
      height: '100%',
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      border: `1px solid ${colors.borderSubtle}`,
      overflow: 'hidden',
      margin: '0 0.5rem 0 0'
    }}>
      {/* 主要会话内容区域 */}
      <div style={{
        flex: showUserInfo ? '1' : '1',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflow: 'hidden'
      }}>
        {/* 会话标题栏 */}
        <div style={{
          padding: '1rem 1.5rem',
          borderBottom: `1px solid ${colors.borderSubtle}`,
          backgroundColor: colors.bgElevated,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          minHeight: '72px'
        }}>
          <div style={{ flex: 1 }}>
            <h3 style={{
              margin: 0,
              fontSize: '1.125rem',
              fontWeight: '600',
              color: colors.textPrimary,
              marginBottom: '0.25rem'
            }}>
              {consultation.title}
            </h3>
            <div style={{
              display: 'flex',
              gap: '1rem',
              fontSize: '0.75rem',
              color: colors.textTertiary
            }}>
              <span style={{
                color: consultation.status === 'active' ? colors.success : 
                      consultation.status === 'pending' ? colors.primary : colors.textTertiary
              }}>
                Status: {consultation.status}
              </span>
            </div>
          </div>
          
          {/* 右侧工具栏 - 仅CS Expert显示 */}
          {projectType !== 'i18n-agent' && (
            <div style={{
              display: 'flex',
              gap: '0.5rem',
              alignItems: 'center'
            }}>
              <button
                onClick={() => setShowUserInfo(!showUserInfo)}
                style={{
                  padding: '0.5rem',
                  borderRadius: '6px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: showUserInfo ? colors.primary : colors.bgCard,
                  color: showUserInfo ? 'white' : colors.textSecondary,
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'all 0.15s ease',
                  width: '36px',
                  height: '36px'
                }}
                onMouseEnter={(e) => {
                  if (!showUserInfo) {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!showUserInfo) {
                    e.currentTarget.style.backgroundColor = colors.bgCard;
                  }
                }}
                title="User Info"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* 会话消息列表 */}
        <div style={{
          flex: 1,
          padding: '1rem 1.5rem',
          overflowY: 'auto',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {chatData && chatData[selectedChat] ? (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              flex: 1
            }}>
              {chatData[selectedChat].map((message: any) => (
                message.type === 'ai' ? (
                  <AIMessage 
                    key={message.id}
                    content={Array.isArray(message.content) ? message.content : [{ type: 'text', content: message.content }]}
                    timestamp={message.timestamp}
                    theme={theme}
                    colors={colors}
                    avatar={renderD_PalAvatar ? renderD_PalAvatar(dpalsAvatar, colors) : undefined}
                  />
                ) : (
                  <UserMessage 
                    key={message.id}
                    content={typeof message.content === 'string' ? message.content : JSON.stringify(message.content)}
                    timestamp={message.timestamp}
                    theme={theme}
                    colors={colors}
                  />
                )
              ))}
            </div>
          ) : (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <div style={{
                padding: '2rem',
                backgroundColor: colors.bgElevated,
                borderRadius: '8px',
                textAlign: 'center',
                border: `1px solid ${colors.borderSubtle}`
              }}>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: colors.textSecondary
                }}>
                  No conversation data available for this consultation.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* 输入区域 */}
        <div style={{
          padding: '1rem 1.5rem'
        }}>
          <AIInputBox 
            onSend={onSendMessage || (() => {})}
            placeholder="Type your response..."
            theme={theme}
            showModeSwitch={false}
            colors={colors}
          />
        </div>
      </div>

      {/* 用户信息面板 - 仅在非 i18n-agent 项目中显示 */}
      {showUserInfo && projectType !== 'i18n-agent' && (
        <div style={{
          width: '280px',
          flexShrink: 0,
          borderLeft: `1px solid ${colors.borderSubtle}`,
          overflow: 'hidden',
          margin: '0 0 0 0.5rem'
        }}>
          <CSAgentUserInfoPanel
            userInfo={mockUserInfo}
            relatedConversations={mockRelatedConversations}
            colors={colors}
            theme={theme}
            onClose={() => setShowUserInfo(false)}
          />
        </div>
      )}
    </div>
  )
} 