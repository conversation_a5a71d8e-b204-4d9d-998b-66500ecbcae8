'use client'
import React from 'react'
import { Colors, CSConsultation } from '../../../types'
import { getUserRankLevel, getTypeColor, getTypeIcon } from '../../../utils'

interface CSAgentListProps {
  consultations: CSConsultation[]
  selectedChat: string | null
  waitingFilter: boolean
  colors: Colors
  onConsultationClick: (consultationId: string) => void
  projectType: string
}

export default function CSAgentList({
  consultations,
  selectedChat,
  waitingFilter,
  colors,
  onConsultationClick,
  projectType
}: CSAgentListProps) {
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '2px',
      backgroundColor: colors.bgSubtle,
      borderRadius: '12px',
      overflow: 'hidden',
      height: '100%',
      overflowY: 'auto',
      padding: '4px'
    }}>
      {consultations.map((consultation) => (
        <div
          key={consultation.id}
          onClick={() => onConsultationClick(consultation.id)}
          style={{
            cursor: 'pointer',
            background: selectedChat === consultation.id ? colors.bgSubtle : colors.bgCard,
            padding: '16px 20px',
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
            position: 'relative',
            transition: 'all 0.15s ease',
            borderLeft: selectedChat === consultation.id ? `3px solid ${colors.primary}` : '3px solid transparent',
            borderRadius: '8px',
            marginBottom: '2px'
          }}
          onMouseEnter={(e) => {
            if (selectedChat !== consultation.id) {
              e.currentTarget.style.backgroundColor = colors.bgElevated;
            }
          }}
          onMouseLeave={(e) => {
            if (selectedChat !== consultation.id) {
              e.currentTarget.style.backgroundColor = colors.bgCard;
            }
          }}
        >
          {/* 未读或等待状态指示器 */}
          {(consultation.unread || (waitingFilter && consultation.status === 'pending')) && (
            <div style={{
              position: 'absolute',
              right: '16px',
              top: '16px',
              width: '10px',
              height: '10px',
              borderRadius: '50%',
              backgroundColor: colors.primary,
              boxShadow: `0 0 0 2px ${colors.bgCard}`,
              animation: 'pulse 2s infinite'
            }} />
          )}

          {/* 咨询标题行 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start'
          }}>
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              overflow: 'hidden',
              paddingRight: '20px'
            }}>
              {/* 用户等级标识 - 仅在非i18n项目中显示 */}
              {projectType !== 'i18n-agent' && getUserRankLevel(consultation.userRank) && (
                <div style={{
                  backgroundColor: colors.error,
                  color: 'white',
                  padding: '3px 8px',
                  borderRadius: '6px',
                  fontSize: '11px',
                  fontWeight: 'bold',
                  minWidth: '34px',
                  textAlign: 'center',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }}>
                  {getUserRankLevel(consultation.userRank)}
                </div>
              )}
              {/* 咨询标题 */}
              <div style={{
                fontSize: '15px',
                fontWeight: '600',
                color: colors.textPrimary,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                flex: 1,
                lineHeight: '1.4'
              }}>
                {consultation.title}
              </div>
            </div>
          </div>

          {/* 最后消息内容 */}
          <div style={{
            fontSize: '13px',
            color: colors.textSecondary,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            lineHeight: '1.3',
            marginTop: '2px'
          }}>
            {consultation.lastMessage}
          </div>

          {/* 底部信息行 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: '8px'
          }}>
            {/* 左侧：游戏信息 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              {/* 游戏标签 */}
              <div style={{
                fontSize: '11px',
                color: colors.textTertiary,
                padding: '3px 6px',
                backgroundColor: colors.bgSubtle,
                borderRadius: '5px',
                fontWeight: '500'
              }}>
                {consultation.game}
              </div>
            </div>
            
            {/* 右侧：时间戳 */}
            <div style={{
              fontSize: '12px',
              color: colors.textTertiary,
              fontWeight: '500'
            }}>
              {consultation.timestamp.split(' ')[1]}
            </div>
          </div>
        </div>
      ))}
      
      {/* 空状态 */}
      {consultations.length === 0 && (
        <div style={{
          padding: '2rem',
          textAlign: 'center',
          color: colors.textTertiary,
          backgroundColor: colors.bgCard
        }}>
          No consultations match the current filters.
        </div>
      )}
    </div>
  )
} 