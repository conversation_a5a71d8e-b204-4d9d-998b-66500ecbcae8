'use client'
import React, { useState } from 'react'
import { Colors, CSConsultation } from '../../../types'
import { filterCSConsultations } from '../../../utils'
import CSAgentFilters from './CSAgentFilters'
import CSAgentList from './CSAgentList'

interface CSAgentSystemProps {
  consultations: CSConsultation[]
  selectedChat: string | null
  colors: Colors
  onConsultationClick: (consultationId: string) => void
  projectType: string
}

export default function CSAgentSystem({
  consultations,
  selectedChat,
  colors,
  onConsultationClick,
  projectType
}: CSAgentSystemProps) {
  // CS Agent特有的状态管理
  const [tab, setTab] = useState<'all' | 'vip' | 'non-vip'>('vip')
  const [searchText, setSearchText] = useState('')
  const [waitingFilter, setWaitingFilter] = useState(true)
  const [poorFilter, setPoorFilter] = useState(false)

  // 根据筛选条件过滤咨询数据
  const filteredConsultations = filterCSConsultations(
    consultations,
    tab,
    searchText,
    waitingFilter
  )

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden'
    }}>
      {/* 筛选器组件 */}
      <div style={{ flexShrink: 0 }}>
        <CSAgentFilters
          searchText={searchText}
          onSearchChange={setSearchText}
          selectedTab={tab}
          onTabChange={setTab}
          waitingFilter={waitingFilter}
          onWaitingFilterChange={setWaitingFilter}
          poorFilter={poorFilter}
          onPoorFilterChange={setPoorFilter}
          colors={colors}
          projectType={projectType}
        />
      </div>

      {/* 咨询列表组件 */}
      <div style={{
        flex: 1,
        overflow: 'hidden'
      }}>
        <CSAgentList
          consultations={filteredConsultations}
          selectedChat={selectedChat}
          waitingFilter={waitingFilter}
          colors={colors}
          onConsultationClick={onConsultationClick}
          projectType={projectType}
        />
      </div>
    </div>
  )
} 