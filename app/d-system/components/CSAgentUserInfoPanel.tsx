'use client'
import React from 'react'
import { UserInfo, RelatedConversation, Colors } from '../types'

interface CSAgentUserInfoPanelProps {
  userInfo: UserInfo
  relatedConversations: RelatedConversation[]
  colors?: Colors
  theme?: 'light' | 'dark'
  onClose?: () => void
}

export default function CSAgentUserInfoPanel({
  userInfo,
  relatedConversations,
  colors,
  theme = 'dark',
  onClose
}: CSAgentUserInfoPanelProps) {
  // 默认颜色方案
  const defaultColors: Colors = {
    bgMain: theme === 'light' ? '#ffffff' : '#1a1a1a',
    bgCard: theme === 'light' ? '#f8f9fa' : '#2a2a2a',
    bgElevated: theme === 'light' ? '#ffffff' : '#333333',
    bgSubtle: theme === 'light' ? '#f1f3f4' : '#3a3a3a',
    textPrimary: theme === 'light' ? '#1f2937' : '#ffffff',
    textSecondary: theme === 'light' ? '#6b7280' : '#d1d5db',
    textTertiary: theme === 'light' ? '#9ca3af' : '#9ca3af',
    borderSubtle: theme === 'light' ? '#e5e7eb' : '#4a4a4a',
    borderStrong: theme === 'light' ? '#d1d5db' : '#6b7280',
    primary: '#3b82f6',
    primaryLight: '#93c5fd',
    accent: '#10b981',
    success: '#059669',
    error: '#dc2626'
  }

  const finalColors = colors || defaultColors

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: finalColors.bgCard,
      overflow: 'hidden'
    }}>


      {/* Content */}
      <div style={{
        flex: 1,
        padding: '1rem',
        overflowY: 'auto'
      }}>
        {/* Game Information */}
        <div style={{ marginBottom: '1.5rem' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '0.75rem'
          }}>
            <h4 style={{
              margin: 0,
              fontSize: '0.875rem',
              fontWeight: '600',
              color: finalColors.textSecondary,
              textTransform: 'uppercase',
              letterSpacing: '0.05em'
            }}>
              Game
            </h4>
            {onClose && (
              <button
                onClick={onClose}
                style={{
                  background: 'none',
                  border: 'none',
                  color: finalColors.textSecondary,
                  cursor: 'pointer',
                  padding: '4px',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'background-color 0.15s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = finalColors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <div style={{
            padding: '0.75rem',
            backgroundColor: finalColors.bgSubtle,
            borderRadius: '0.5rem',
            border: `1px solid ${finalColors.borderSubtle}`
          }}>
            <div style={{
              fontSize: '0.875rem',
              fontWeight: '500',
              color: finalColors.textPrimary,
              marginBottom: '0.25rem'
            }}>
              {userInfo.game.name}
            </div>
            <div style={{
              fontSize: '0.75rem',
              color: finalColors.textSecondary,
              marginBottom: '0.5rem'
            }}>
              {userInfo.game.description}
            </div>
            <div style={{
              fontSize: '0.75rem',
              color: finalColors.textTertiary
            }}>
              Team: {userInfo.game.team}
            </div>
          </div>
        </div>

        {/* User Details */}
        <div style={{ marginBottom: '1.5rem' }}>
          <h4 style={{
            margin: '0 0 0.75rem 0',
            fontSize: '0.875rem',
            fontWeight: '600',
            color: finalColors.textSecondary,
            textTransform: 'uppercase',
            letterSpacing: '0.05em'
          }}>
            User Details
          </h4>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem'
          }}>
            {[
              { label: 'G123 ID', value: userInfo.user.g123Id },
              { label: 'Server ID', value: userInfo.user.serverId },
              { label: 'Game UID', value: userInfo.user.gameUid },
              { label: 'Role ID', value: userInfo.user.roleId },
              { label: 'IM', value: userInfo.user.im },
              { label: 'Rank', value: userInfo.user.rank },
              { label: 'Amount', value: userInfo.user.amount },
              { label: 'VIP Level', value: userInfo.user.vipLevel }
            ].map((item, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '0.5rem 0.75rem',
                backgroundColor: index % 2 === 0 ? finalColors.bgSubtle : 'transparent',
                borderRadius: '0.25rem'
              }}>
                <span style={{
                  fontSize: '0.75rem',
                  color: finalColors.textSecondary,
                  minWidth: '70px'
                }}>
                  {item.label}:
                </span>
                <span style={{
                  fontSize: '0.75rem',
                  color: finalColors.textPrimary,
                  fontWeight: '500',
                  textAlign: 'right',
                  flex: 1
                }}>
                  {item.value}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* System Information */}
        <div style={{ marginBottom: '1.5rem' }}>
          <h4 style={{
            margin: '0 0 0.75rem 0',
            fontSize: '0.875rem',
            fontWeight: '600',
            color: finalColors.textSecondary,
            textTransform: 'uppercase',
            letterSpacing: '0.05em'
          }}>
            System
          </h4>
          <div style={{
            padding: '0.75rem',
            backgroundColor: finalColors.bgSubtle,
            borderRadius: '0.5rem',
            border: `1px solid ${finalColors.borderSubtle}`,
            fontSize: '0.75rem',
            color: finalColors.textPrimary
          }}>
            {userInfo.user.system}
          </div>
        </div>

        {/* Related Conversations */}
        {relatedConversations && relatedConversations.length > 0 && (
          <div>
            <h4 style={{
              margin: '0 0 0.75rem 0',
              fontSize: '0.875rem',
              fontWeight: '600',
              color: finalColors.textSecondary,
              textTransform: 'uppercase',
              letterSpacing: '0.05em'
            }}>
              Related Conversations
            </h4>
            <button
              onClick={() => {
                window.open('/d-system', '_blank')
              }}
              style={{
                width: '100%',
                padding: '0.75rem',
                backgroundColor: finalColors.primary,
                color: '#ffffff',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = finalColors.primaryLight
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = finalColors.primary
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
              </svg>
              View Related Conversation
            </button>
          </div>
        )}
      </div>
    </div>
  )
} 