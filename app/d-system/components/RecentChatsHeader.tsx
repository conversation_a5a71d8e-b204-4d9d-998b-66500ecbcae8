'use client'
import React from 'react';

interface RecentChatsHeaderProps {
  colors: {
    textTertiary: string;
  };
}

/**
 * Recent Chats标题组件
 * 显示聊天历史区域的标题
 */
export default function RecentChatsHeader({ colors }: RecentChatsHeaderProps) {
  return (
    <div style={{ 
      padding: '0.5rem 1rem 0.5rem 1rem',
      color: colors.textTertiary,
      fontSize: '0.75rem',
      fontWeight: 'bold',
      textTransform: 'uppercase',
      letterSpacing: '0.05em',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <span>Recent Chats</span>
    </div>
  );
} 