'use client'
import React, { useState } from 'react'
import { Colors, Project } from '../../types'
import SystemProjectInterface from '../SystemProject'
import AIInputBox from '../../../components/AIInputBox'
import TeamMembersModal from './TeamMembersModal'
import { teamMembers, csAgentChatData, i18nAgentChatData, csAgentConsultations, i18nAgentConsultations, knowledgeTasks, i18nKnowledgeTasks, evaluationTasks, i18nEvaluationTasks } from '../../data/mockData'
import MessageItem from '../../../components/MessageItem'
import CSAgentConversationDetail from '../SystemProject/CSAgent/CSAgentConversationDetail'
import KnowledgeManager from '../Knowledge/KnowledgeManager'
import Evaluation from '../Evaluation'

interface ProjectDetailProps {
  selectedProject: string
  project: Project | null
  projectSessions: Array<{id: string, name: string, createdAt: string}>
  selectedChat: string | null
  colors: Colors
  theme: 'light' | 'dark'
  csAgentConsultations: any[]
  onChatClick: (chatId: string, projectId?: string) => void
  onProjectSessionSend: (message: string, files: File[]) => void
  setProjectSessions: React.Dispatch<React.SetStateAction<Array<{id: string, name: string, createdAt: string}>>>
  setComplexSessions: React.Dispatch<React.SetStateAction<any[]>>
  systemData?: any
  dpalsAvatar?: any
  renderD_PalAvatar?: (avatarOptions: any, colors: any) => string
  onKnowledgeClick?: () => void
  onEvaluationClick?: () => void
}

export default function ProjectDetail({
  selectedProject,
  project,
  projectSessions,
  selectedChat,
  colors,
  theme,
  csAgentConsultations,
  onChatClick,
  onProjectSessionSend,
  setProjectSessions,
  setComplexSessions,
  systemData,
  dpalsAvatar,
  renderD_PalAvatar,
  onKnowledgeClick,
  onEvaluationClick
}: ProjectDetailProps) {
  const [isTeamModalOpen, setIsTeamModalOpen] = useState(false)
  const [showAgentInfo, setShowAgentInfo] = useState(false)
  const [currentView, setCurrentView] = useState<'main' | 'knowledge' | 'evaluation'>('main')
  const handleNewSessionSend = (message: string, files: File[]) => {
    // 创建新的会话
    const sessionId = `session-${Date.now()}`;
    const newSession = {
      id: sessionId,
      name: message.slice(0, 30) + (message.length > 30 ? '...' : ''),
      createdAt: new Date().toLocaleDateString('en-US', {year: 'numeric', month: 'short', day: 'numeric'})
    };
    
    // 更新项目会话列表
    setProjectSessions(prev => [newSession, ...prev]);
    
    // 创建新消息并切换到会话
    const sampleUserMessage = {
      id: Date.now(),
      type: 'user' as const,
      content: message,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      attachments: []
    };
    
    // 添加新的复杂会话
    const newComplexSession = {
      id: sessionId,
      title: message.slice(0, 30) + (message.length > 30 ? '...' : ''),
      messages: [sampleUserMessage]
    };
    
    setComplexSessions(prev => [newComplexSession, ...prev]);
    
    // 切换到新会话，保留项目选中状态
    setTimeout(() => {
      onChatClick(sessionId, selectedProject);
    }, 100);
  }

  if (!selectedProject) {
    return null
  }

  return (
    <div style={{ 
      display: 'flex', 
      height: '100vh',
      paddingTop: '1rem'
    }}>
      {(selectedProject === 'cs-agent' || selectedProject === 'i18n-agent') ? (
        currentView === 'knowledge' ? (
          // Knowledge Management 视图
          <KnowledgeManager
            colors={colors}
            theme={theme}
            tasks={selectedProject === 'cs-agent' ? knowledgeTasks : i18nKnowledgeTasks}
            onBackToCSAgent={() => setCurrentView('main')}
            systemType={selectedProject === 'cs-agent' ? 'cs' : 'i18n'}
          />
        ) : currentView === 'evaluation' ? (
          // Evaluation Management 视图
          <Evaluation
            colors={colors}
            theme={theme}
            tasks={selectedProject === 'cs-agent' ? evaluationTasks : i18nEvaluationTasks}
            onBackToCSAgent={() => setCurrentView('main')}
            systemType={selectedProject === 'cs-agent' ? 'cs' : 'i18n'}
          />
        ) : (
          // CS Agent 两列布局
          <div style={{
            display: 'flex',
            height: '100%',
            width: '100%',
            gap: '1rem',
            padding: '0 1.5rem'
          }}>
          {/* 左侧：项目头部 + CS Agent 筛选器和列表 */}
          <div style={{
            flex: '0 0 480px',
            display: 'flex',
            flexDirection: 'column',
            height: '100%',
            overflow: 'hidden'
          }}>
            {/* Project Header */}
            <div style={{
              padding: '1.25rem 1.5rem',
              borderBottom: `1px solid ${colors.borderSubtle}`,
              backgroundColor: colors.bgCard,
              borderRadius: '12px',
              marginBottom: '1rem',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '0.75rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '6px',
                    backgroundColor: selectedProject === 'cs-agent' ? '#52c41a' : '#1890ff',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white'
                  }}>
                    {selectedProject === 'cs-agent' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h1 style={{ 
                      fontSize: '1.125rem', 
                      fontWeight: '600', 
                      margin: 0,
                      color: colors.textPrimary
                    }}>
                      {selectedProject === 'cs-agent' ? 'CS Expert' : 'i18n Expert'}
                    </h1>
                  </div>
                </div>
                
                {/* 右侧按钮区域 */}
                <div style={{
                  display: 'flex',
                  gap: '0.5rem',
                  alignItems: 'center'
                }}>
                  <button
                    onClick={() => setCurrentView('knowledge')}
                    style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      border: `1px solid ${colors.borderSubtle}`,
                      backgroundColor: colors.bgElevated,
                      color: colors.textSecondary,
                      fontSize: '0.75rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem',
                      transition: 'background-color 0.15s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgSubtle;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgElevated;
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '12px', height: '12px'}}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
                    </svg>
                    Knowledge
                  </button>
                  <button
                    onClick={() => setCurrentView('evaluation')}
                    style={{
                      padding: '0.25rem 0.5rem',
                      borderRadius: '4px',
                      border: `1px solid ${colors.borderSubtle}`,
                      backgroundColor: colors.bgElevated,
                      color: colors.textSecondary,
                      fontSize: '0.75rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem',
                      transition: 'background-color 0.15s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgSubtle;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgElevated;
                    }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '12px', height: '12px'}}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Evaluation
                  </button>
                </div>
              </div>
              {/* 团队成员头像区域 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                gap: '0.25rem'
              }}>
                <div 
                  onClick={() => setIsTeamModalOpen(true)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    padding: '0.25rem',
                    borderRadius: '6px',
                    transition: 'background-color 0.15s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  {/* Agent Info Icon */}
                  <div style={{ position: 'relative', marginRight: '0.5rem' }}>
                    <div
                      onMouseEnter={() => setShowAgentInfo(true)}
                      onMouseLeave={() => setShowAgentInfo(false)}
                      onClick={(e) => e.stopPropagation()}
                      style={{
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                        backgroundColor: colors.primary,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        cursor: 'pointer',
                        fontSize: '0.625rem',
                        fontWeight: '600'
                      }}
                    >
                      i
                    </div>
                    {showAgentInfo && (
                      <div style={{
                        position: 'absolute',
                        top: '20px',
                        left: '0',
                        backgroundColor: colors.bgCard,
                        border: `1px solid ${colors.borderSubtle}`,
                        borderRadius: '8px',
                        padding: '0.75rem',
                        minWidth: '200px',
                        maxWidth: '280px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                        zIndex: 1000,
                        fontSize: '0.875rem',
                        color: colors.textPrimary,
                        lineHeight: '1.4'
                      }}>
                        <div style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
                          {selectedProject === 'i18n-agent' ? 'i18n Expert Capabilities' : 'CS Agent Capabilities'}
                        </div>
                        <div>
                          {selectedProject === 'i18n-agent' 
                            ? 'Specialized in resolving internationalization and localization issues, providing support for multilingual content, text encoding, regional formatting, and cross-cultural user experience optimization.'
                            : 'Specialized in resolving player consultation issues, providing real-time support for game-related questions, account problems, and technical assistance.'
                          }
                        </div>
                      </div>
                    )}
                  </div>

                  {teamMembers.slice(0, 4).map((member, index) => (
                    <div
                      key={member.id}
                      style={{
                        width: '20px',
                        height: '20px',
                        borderRadius: '50%',
                        backgroundColor: colors.primary,
                        backgroundImage: member.avatar ? `url(${member.avatar})` : 'none',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '0.625rem',
                        fontWeight: '500',
                        border: `2px solid ${colors.bgCard}`,
                        marginLeft: index > 0 ? '-0.375rem' : '0',
                        zIndex: teamMembers.length - index,
                        position: 'relative'
                      }}
                    >
                      {!member.avatar && member.username.charAt(0).toUpperCase()}
                    </div>
                  ))}
                  <div
                    style={{
                      marginLeft: '-0.125rem',
                      padding: '0.125rem 0.375rem',
                      borderRadius: '3px',
                      border: `1px solid ${colors.borderSubtle}`,
                      backgroundColor: colors.bgElevated,
                      color: colors.textSecondary,
                      fontSize: '0.625rem',
                      zIndex: 1
                    }}
                  >
                    +{teamMembers.length - 4}
                  </div>
                </div>
              </div>
            </div>

            {/* CS Agent 筛选器和列表 */}
            <div style={{
              flex: 1,
              overflow: 'hidden'
            }}>
              <SystemProjectInterface
                projectId={selectedProject}
                selectedChat={selectedChat}
                colors={colors}
                onConsultationClick={(consultationId) => onChatClick(consultationId, selectedProject)}
                systemData={systemData}
              />
            </div>
          </div>
            
          {/* 右侧：CS Agent 会话详情 */}
          <CSAgentConversationDetail
            selectedChat={selectedChat}
            consultation={systemData?.csAgent?.consultations?.find((c: any) => c.id === selectedChat) || null}
            chatData={selectedProject === 'i18n-agent' ? i18nAgentChatData : csAgentChatData}
            colors={colors}
            theme={theme}
            onSendMessage={(message, files) => {
              // 这里可以处理 CS Agent 特定的消息发送逻辑
              console.log('CS Agent message sent:', message, files);
            }}
            dpalsAvatar={dpalsAvatar}
            renderD_PalAvatar={renderD_PalAvatar}
            projectType={selectedProject}
          />
        </div>
        )
      ) : (
        // 普通项目展示 
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          width: '100%',
          padding: '0 1.5rem'
        }}>
          {/* Project Header */}
          <div style={{
            padding: '1.5rem',
            borderBottom: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgCard,
            borderRadius: '8px',
            marginBottom: '1rem'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '0.75rem'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem'
              }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '8px',
                  backgroundColor: colors.primary,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white'
                }}>
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '20px', height: '20px'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                  </svg>
                </div>
                <div>
                  <h1 style={{ 
                    fontSize: '1.5rem', 
                    fontWeight: '600', 
                    margin: 0,
                    color: colors.textPrimary
                  }}>
                    {project?.name || 'Project'}
                  </h1>
                </div>
              </div>
            </div>
            <p style={{
              fontSize: '0.875rem',
              color: colors.textTertiary,
              margin: 0
            }}>
              {projectSessions.length} sessions • Last updated: {new Date().toLocaleDateString('en-US', {month: 'short', day: 'numeric', year: 'numeric'})}
            </p>
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '0.75rem'
          }}>
            <h2 style={{
              fontSize: '1rem',
              fontWeight: '600',
              margin: 0,
              color: colors.textPrimary
            }}>
              Sessions
            </h2>
          </div>
          
          {/* 会话列表 */}
          <div style={{ flex: 1, overflow: 'auto', marginBottom: '1rem' }}>
            {projectSessions.length > 0 ? (
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '0.5rem'
              }}>
                {projectSessions.map(session => (
                  <div 
                    key={session.id}
                    style={{
                      padding: '0.75rem',
                      backgroundColor: colors.bgCard,
                      borderRadius: '8px',
                      border: `1px solid ${colors.borderSubtle}`,
                      cursor: 'pointer',
                      transition: 'background-color 0.15s ease, color 0.15s ease'
                    }}
                    onClick={() => {
                      onChatClick(session.id, selectedProject);
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgSubtle;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgCard;
                    }}
                  >
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <div>
                        <div style={{
                          fontWeight: '500',
                          marginBottom: '0.25rem',
                          color: colors.textPrimary
                        }}>
                          {session.name}
                        </div>
                        <div style={{
                          fontSize: '0.75rem',
                          color: colors.textTertiary
                        }}>
                          Created: {session.createdAt}
                        </div>
                      </div>
                      <div style={{
                        width: '28px',
                        height: '28px',
                        borderRadius: '50%',
                        backgroundColor: `${colors.primary}15`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: colors.primary
                      }}>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{
                padding: '2rem',
                textAlign: 'center',
                color: colors.textTertiary,
                backgroundColor: colors.bgCard,
                borderRadius: '8px',
                border: `1px solid ${colors.borderSubtle}`
              }}>
                No sessions yet. Create a new session to get started.
              </div>
            )}
          </div>

          {/* 输入区域 */}
          <div style={{ marginTop: '1rem' }}>
            <AIInputBox 
              onSend={handleNewSessionSend}
              placeholder="Ask something to create a new session..."
              theme={theme}
              showModeSwitch={true}
              colors={colors}
            />
          </div>
        </div>
      )}
      
      {/* 团队成员权限弹窗 */}
      <TeamMembersModal 
        isOpen={isTeamModalOpen}
        onClose={() => setIsTeamModalOpen(false)}
        members={teamMembers}
        colors={colors}
      />
    </div>
  )
} 