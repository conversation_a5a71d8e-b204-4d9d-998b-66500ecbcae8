'use client'
import React from 'react'
import { Project, Colors } from '../../types'
import ProjectGrid from './ProjectGrid'
import ProjectDetail from './ProjectDetail'

interface ProjectManagerProps {
  selectedProject: string | null
  expandedProject: string | null
  projectSessions: Array<{id: string, name: string, createdAt: string}>
  selectedChat: string | null
  mockProjects: Project[]
  csAgentConsultations: any[]
  colors: Colors
  theme: 'light' | 'dark'
  // Event handlers
  onProjectClick: (projectId: string) => void
  onCreateProject: () => void
  onChatClick: (chatId: string, projectId?: string) => void
  onProjectSessionSend: (message: string, files: File[]) => void
  onKnowledgeClick?: () => void
  // State setters
  setProjectSessions: React.Dispatch<React.SetStateAction<Array<{id: string, name: string, createdAt: string}>>>
  setComplexSessions: React.Dispatch<React.SetStateAction<any[]>>
  // System data for CS Agent
  systemData?: {
    csAgent?: {
      consultations: any[]
    }
  }
  // D-system avatar related
  dpalsAvatar?: any
  renderD_PalAvatar?: (avatarOptions: any, colors: any) => string
}

export default function ProjectManager({
  selectedProject,
  expandedProject,
  projectSessions,
  selectedChat,
  mockProjects,
  csAgentConsultations,
  colors,
  theme,
  onProjectClick,
  onCreateProject,
  onChatClick,
  onProjectSessionSend,
  onKnowledgeClick,
  setProjectSessions,
  setComplexSessions,
  systemData,
  dpalsAvatar,
  renderD_PalAvatar
}: ProjectManagerProps) {
  // Find the current project
  const currentProject = selectedProject 
    ? mockProjects.find(p => p.id === selectedProject) || null 
    : null

  // If a project is selected, show project detail view
  if (selectedProject) {
    return (
      <ProjectDetail
        selectedProject={selectedProject}
        project={currentProject}
        projectSessions={projectSessions}
        selectedChat={selectedChat}
        colors={colors}
        theme={theme}
        csAgentConsultations={csAgentConsultations}
        onChatClick={onChatClick}
        onProjectSessionSend={onProjectSessionSend}
        setProjectSessions={setProjectSessions}
        setComplexSessions={setComplexSessions}
        systemData={systemData}
        dpalsAvatar={dpalsAvatar}
        renderD_PalAvatar={renderD_PalAvatar}
        onKnowledgeClick={onKnowledgeClick}
      />
    )
  }

  // Otherwise, show project grid view
  return (
    <ProjectGrid
      projects={mockProjects}
      expandedProject={expandedProject}
      csAgentConsultations={csAgentConsultations}
      colors={colors}
      theme={theme}
      onProjectClick={onProjectClick}
      onCreateProject={onCreateProject}
    />
  )
} 