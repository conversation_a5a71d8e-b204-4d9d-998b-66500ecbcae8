'use client'
import React from 'react'
import { Project, Colors } from '../../types'

interface ProjectCardProps {
  project: Project
  isExpanded: boolean
  csAgentConsultations?: any[]
  colors: Colors
  onProjectClick: (projectId: string) => void
  expandedProject: string | null
  theme: 'light' | 'dark'
}

export default function ProjectCard({
  project,
  isExpanded,
  csAgentConsultations = [],
  colors,
  onProjectClick,
  expandedProject,
  theme
}: ProjectCardProps) {
  const handleCardClick = () => {
    onProjectClick(project.id)
  }

  const renderProjectIcon = () => {
    if (project.id === 'cs-agent') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '18px', height: '18px'}}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155" />
        </svg>
      )
    } else if (project.id === 'i18n-agent') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '18px', height: '18px'}}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802" />
        </svg>
      )
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '18px', height: '18px'}}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
        </svg>
      )
    }
  }

  const hasUnreadItems = (project.id === 'cs-agent' || project.id === 'i18n-agent') && 
    (csAgentConsultations.filter(c => c.unread).length > 0 || 
     csAgentConsultations.filter(c => c.status === 'pending').length > 0)

  const getSessionCount = () => {
    return (project.id === 'cs-agent' || project.id === 'i18n-agent') ? csAgentConsultations.length : project.sessions.length
  }

  return (
    <div 
      style={{
        backgroundColor: colors.bgCard,
        height: "130px",
        padding: '1.5rem',
        borderRadius: '12px',
        border: `1px solid ${colors.borderSubtle}`,
        cursor: 'pointer',
        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
        transform: expandedProject === project.id ? 'scale(1.02)' : 'scale(1)',
        boxShadow: expandedProject === project.id 
          ? (theme === 'light' ? '0 8px 16px rgba(0, 0, 0, 0.1)' : '0 8px 16px rgba(0, 0, 0, 0.4)')
          : (theme === 'light' ? '0 4px 6px rgba(0, 0, 0, 0.05)' : '0 4px 6px rgba(0, 0, 0, 0.3)')
      }}
      onClick={handleCardClick}
    >
      {/* Project Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '1rem'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem',
          flex: 1
        }}>
          <div style={{
            width: '32px',
            height: '32px',
            borderRadius: '8px',
            backgroundColor: project.id === 'cs-agent' ? '#52c41a' : project.id === 'i18n-agent' ? '#1890ff' : colors.primary,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white'
          }}>
            {renderProjectIcon()}
          </div>
          <div>
            <h3 style={{ 
              margin: 0, 
              fontSize: '1.125rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              {project.name}
            </h3>
          </div>
          {/* Unread indicator for CS Agent */}
          {hasUnreadItems && (
            <div style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: colors.primary,
              marginLeft: '8px',
              flexShrink: 0,
              animation: 'pulse 2s infinite'
            }} />
          )}
        </div>
        <div style={{
          width: '28px',
          height: '28px',
          borderRadius: '50%',
          backgroundColor: colors.bgElevated,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'transform 0.2s ease'
        }}>
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24" 
            strokeWidth={1.5} 
            stroke="currentColor" 
            style={{
              width: '18px', 
              height: '18px',
              color: colors.textSecondary,
              transform: expandedProject === project.id ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s ease'
            }}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
          </svg>
        </div>
      </div>

      {/* Project Overview */}
      <div style={{ 
        padding: '0.75rem',
        backgroundColor: colors.bgElevated,
        borderRadius: '8px',
        marginBottom: '1rem',
        fontSize: '0.875rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
          <span style={{ color: colors.textSecondary }}>
            {(project.id === 'cs-agent' || project.id === 'i18n-agent') ? 'Consultations:' : 'Sessions:'}
          </span>
          <span style={{ fontWeight: '500', color: colors.textPrimary }}>
            {getSessionCount()}
          </span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
          <span style={{ color: colors.textSecondary }}>Last Updated:</span>
          <span style={{ fontWeight: '500', color: colors.textPrimary }}>{new Date().toLocaleDateString()}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span style={{ color: colors.textSecondary }}>Status:</span>
          <span style={{ 
            fontWeight: '500',
            color: colors.success
          }}>Active</span>
        </div>
      </div>
    </div>
  )
} 