'use client'
import React, { useState } from 'react'
import { Colors, TeamMember, Permission } from '../../types'

interface TeamMembersModalProps {
  isOpen: boolean
  onClose: () => void
  members: TeamMember[]
  colors: Colors
}

const permissionIcons = {
  reply: {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.444 3 12c0 2.104.859 4.023 2.273 5.48.432.447.74 1.04.586 1.641a4.483 4.483 0 01-.923 1.785A5.969 5.969 0 006 21c1.282 0 2.47-.402 3.445-1.087.81.22 1.668.337 2.555.337z" />
      </svg>
    ),
    label: 'Reply'
  },
  evaluate: {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    label: 'Evaluate'
  },
  knowledge: {
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
      </svg>
    ),
    label: 'Knowledge'
  }
}

export default function TeamMembersModal({ isOpen, onClose, members, colors }: TeamMembersModalProps) {
  const [hoveredPermission, setHoveredPermission] = useState<{permission: Permission, position: {x: number, y: number}} | null>(null)
  const [editingCapabilities, setEditingCapabilities] = useState<string | null>(null)
  const [capabilitiesValues, setCapabilitiesValues] = useState<{[key: string]: string}>(() => {
    const initial: {[key: string]: string} = {}
    members.forEach(member => {
      initial[member.id] = member.capabilities || ''
    })
    return initial
  })

  const handleCapabilitiesEdit = (memberId: string) => {
    setEditingCapabilities(memberId)
  }

  const handleCapabilitiesSave = (memberId: string) => {
    setEditingCapabilities(null)
    // 这里可以添加保存到后端的逻辑
    console.log(`Saved capabilities for ${memberId}:`, capabilitiesValues[memberId])
  }

  const handleCapabilitiesChange = (memberId: string, value: string) => {
    setCapabilitiesValues(prev => ({
      ...prev,
      [memberId]: value
    }))
  }
  
  if (!isOpen) return null

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: colors.bgCard,
          borderRadius: '12px',
          padding: '1.5rem',
          width: '480px',
          maxHeight: '80vh',
          overflow: 'auto',
          border: `1px solid ${colors.borderSubtle}`,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1.5rem'
        }}>
          <h3 style={{
            margin: 0,
            fontSize: '1.25rem',
            fontWeight: '600',
            color: colors.textPrimary
          }}>
            Team Members & Permissions
          </h3>
          <button
            onClick={onClose}
            style={{
              backgroundColor: 'transparent',
              border: 'none',
              cursor: 'pointer',
              padding: '0.25rem',
              color: colors.textTertiary,
              borderRadius: '4px'
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '20px', height: '20px'}}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '0.75rem'
        }}>
          {members.map((member) => (
            <div
              key={member.id}
              style={{
                padding: '0.75rem',
                backgroundColor: member.isCurrentUser ? `${colors.primary}08` : colors.bgElevated,
                borderRadius: '8px',
                border: member.isCurrentUser ? `1px solid ${colors.primary}20` : `1px solid ${colors.borderSubtle}`
              }}
            >
              {/* 成员信息行 */}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '0.5rem'
                }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem'
                }}>
                  <div style={{
                    width: '36px',
                    height: '36px',
                    borderRadius: '50%',
                    backgroundColor: colors.primary,
                    backgroundImage: `url(${member.avatar})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    position: 'relative'
                  }}>
                    {!member.avatar && member.username.charAt(0).toUpperCase()}
                    {member.isCurrentUser && (
                      <div style={{
                        position: 'absolute',
                        bottom: '-2px',
                        right: '-2px',
                        width: '12px',
                        height: '12px',
                        backgroundColor: colors.success,
                        borderRadius: '50%',
                        border: `2px solid ${colors.bgCard}`
                      }} />
                    )}
                  </div>
                  <div>
                    <div style={{
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: colors.textPrimary,
                      marginBottom: '0.125rem'
                    }}>
                      {member.username}
                      {member.isCurrentUser && (
                        <span style={{
                          marginLeft: '0.5rem',
                          fontSize: '0.75rem',
                          color: colors.primary,
                          fontWeight: '400'
                        }}>
                          (You)
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  gap: '0.375rem',
                  alignItems: 'center'
                }}>
                  {member.permissions.map((permission) => (
                    <div
                      key={permission}
                      style={{
                        padding: '0.25rem',
                        borderRadius: '4px',
                        backgroundColor: `${colors.primary}15`,
                        color: colors.primary,
                        cursor: 'help',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        const rect = e.currentTarget.getBoundingClientRect()
                        setHoveredPermission({
                          permission,
                          position: {
                            x: rect.left + rect.width / 2,
                            y: rect.top - 8
                          }
                        })
                      }}
                      onMouseLeave={() => setHoveredPermission(null)}
                    >
                      {permissionIcons[permission].icon}
                    </div>
                  ))}
                </div>
              </div>

              {/* 能力范围输入框 */}
              <div style={{ marginTop: '0.5rem' }}>
                {editingCapabilities === member.id ? (
                  <textarea
                    value={capabilitiesValues[member.id] || ''}
                    onChange={(e) => handleCapabilitiesChange(member.id, e.target.value)}
                    onBlur={() => handleCapabilitiesSave(member.id)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                        handleCapabilitiesSave(member.id)
                      }
                      if (e.key === 'Escape') {
                        setEditingCapabilities(null)
                        setCapabilitiesValues(prev => ({
                          ...prev,
                          [member.id]: member.capabilities || ''
                        }))
                      }
                    }}
                    placeholder="Describe capabilities for AI matching..."
                    autoFocus
                    rows={3}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: `1px solid ${colors.primary}`,
                      borderRadius: '4px',
                      backgroundColor: colors.bgCard,
                      color: colors.textPrimary,
                      fontSize: '0.875rem',
                      outline: 'none',
                      boxShadow: `0 0 0 2px ${colors.primary}20`,
                      resize: 'none',
                      fontFamily: 'inherit',
                      lineHeight: '1.5'
                    }}
                  />
                ) : (
                  <div
                    onClick={() => handleCapabilitiesEdit(member.id)}
                    style={{
                      padding: '0.5rem',
                      border: `1px solid ${colors.borderSubtle}`,
                      borderRadius: '4px',
                      backgroundColor: colors.bgCard,
                      color: capabilitiesValues[member.id] ? colors.textSecondary : colors.textTertiary,
                      fontSize: '0.875rem',
                      cursor: 'text',
                      minHeight: '2.25rem',
                      maxHeight: '2.25rem',
                      lineHeight: '1.5',
                      transition: 'border-color 0.15s ease',
                      overflow: 'hidden',
                      display: '-webkit-box',
                      WebkitLineClamp: 1,
                      WebkitBoxOrient: 'vertical',
                      whiteSpace: 'normal'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = colors.borderStrong
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = colors.borderSubtle
                    }}
                  >
                    {capabilitiesValues[member.id] || 'Describe capabilities for AI matching...'}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* 自定义 Tooltip */}
      {hoveredPermission && (
        <div
          style={{
            position: 'fixed',
            left: hoveredPermission.position.x,
            top: hoveredPermission.position.y,
            transform: 'translateX(-50%) translateY(-100%)',
            backgroundColor: colors.textPrimary,
            color: colors.bgCard,
            padding: '0.25rem 0.5rem',
            borderRadius: '4px',
            fontSize: '0.75rem',
            fontWeight: '500',
            zIndex: 1001,
            whiteSpace: 'nowrap',
            pointerEvents: 'none',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
          }}
        >
          {permissionIcons[hoveredPermission.permission].label}
        </div>
      )}
    </div>
  )
} 