'use client'
import React from 'react'
import { Project, Colors } from '../../types'
import ProjectCard from './ProjectCard'

interface ProjectGridProps {
  projects: Project[]
  expandedProject: string | null
  csAgentConsultations: any[]
  colors: Colors
  theme: 'light' | 'dark'
  onProjectClick: (projectId: string) => void
  onCreateProject: () => void
}

export default function ProjectGrid({
  projects,
  expandedProject,
  csAgentConsultations,
  colors,
  theme,
  onProjectClick,
  onCreateProject
}: ProjectGridProps) {
  return (
    <div style={{ padding: '2rem', height: 'calc(100vh - 4rem)', overflowY: 'auto' }}>
      <div style={{ marginBottom: '1.5rem' }}>
        <h2 style={{ 
          fontSize: '1.25rem', 
          marginBottom: '1rem', 
          color: colors.textPrimary,
          fontWeight: '600' 
        }}>
          Your Projects
        </h2>
        
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', 
          gap: '1.5rem' 
        }}>
          {/* Existing Project Cards */}
          {projects.map(project => (
            <ProjectCard
              key={project.id}
              project={project}
              isExpanded={expandedProject === project.id}
              csAgentConsultations={csAgentConsultations}
              colors={colors}
              onProjectClick={onProjectClick}
              expandedProject={expandedProject}
              theme={theme}
            />
          ))}
          
          {/* Create New Project Card */}
          <div 
            style={{ 
              backgroundColor: 'transparent',
              padding: '1.5rem',
              borderRadius: '12px',
              border: `2px dashed ${colors.borderStrong}`,
              cursor: 'pointer',
              height: '100%',
              minHeight: '200px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'background-color 0.15s ease, color 0.15s ease'
            }}
            onClick={onCreateProject}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.bgSubtle;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <div style={{ 
              width: '48px',
              height: '48px',
              borderRadius: '50%',
              backgroundColor: `${colors.primary}20`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '1rem'
            }}>
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24" 
                strokeWidth={1.5} 
                stroke={colors.primary} 
                style={{ width: '24px', height: '24px' }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
              </svg>
            </div>
            <div style={{ 
              fontWeight: '500', 
              fontSize: '1.1rem', 
              marginBottom: '0.5rem',
              color: colors.textPrimary
            }}>
              Create New Project
            </div>
            <div style={{ 
              color: colors.textTertiary, 
              textAlign: 'center', 
              fontSize: '0.875rem' 
            }}>
              Start organizing your work in a new project
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 