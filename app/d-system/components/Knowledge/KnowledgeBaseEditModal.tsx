'use client'
import React, { useState, useEffect } from 'react'
import { Colors, KnowledgeBaseItem } from '../../types'

interface KnowledgeBaseEditModalProps {
  isOpen: boolean
  onClose: () => void
  item: KnowledgeBaseItem | null
  colors: Colors
}

export default function KnowledgeBaseEditModal({ isOpen, onClose, item, colors }: KnowledgeBaseEditModalProps) {
  const [selectedAppCode, setSelectedAppCode] = useState<string>('')
  const [isGeneral, setIsGeneral] = useState<boolean>(false)
  const [question, setQuestion] = useState<string>('')
  const [answer, setAnswer] = useState<string>('')

  useEffect(() => {
    if (item) {
      setSelectedAppCode(item.appCode || '')
      setIsGeneral(item.general || false)
      setQuestion(item.question || '')
      setAnswer(item.answer || '')
    }
  }, [item])

  if (!isOpen || !item) return null

  const handleSubmit = () => {
    // TODO: 提交审批逻辑
    console.log('Submit for approval:', {
      appCode: selectedAppCode,
      general: isGeneral,
      question,
      answer
    })
    onClose()
  }

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: colors.bgCard,
        borderRadius: '12px',
        width: '90%',
        maxWidth: '800px',
        maxHeight: '90%',
        display: 'flex',
        flexDirection: 'column',
        border: `1px solid ${colors.borderSubtle}`
      }}>
        {/* Modal Header */}
        <div style={{
          padding: '1.5rem',
          borderBottom: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h2 style={{
              margin: 0,
              fontSize: '1.25rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Edit Knowledge Base Item
            </h2>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: '0.25rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: colors.textSecondary,
                  fontFamily: 'monospace'
                }}>
                  ID: {item.id}
                </p>
              </div>
              <span style={{
                padding: '0.25rem 0.5rem',
                borderRadius: '12px',
                fontSize: '0.75rem',
                fontWeight: '500',
                color: colors.textSecondary,
                backgroundColor: colors.bgSubtle,
                fontFamily: 'monospace'
              }}>
                {item.version}
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: colors.textTertiary,
              padding: '0.5rem',
              borderRadius: '4px'
            }}
          >
            ✕
          </button>
        </div>

        {/* Modal Content */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '1.5rem'
        }}>
          {/* Knowledge Configuration */}
          <div style={{
            padding: '1rem',
            marginBottom: '1.5rem',
            border: `1px solid ${colors.borderSubtle}`,
            borderRadius: '8px',
            backgroundColor: colors.bgElevated
          }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              fontSize: '0.875rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Knowledge Configuration
            </h4>
            
            <div style={{ display: 'flex', gap: '1.5rem', alignItems: 'flex-start' }}>
              {/* General Checkbox */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <input
                  type="checkbox"
                  id="general-checkbox"
                  checked={isGeneral}
                  onChange={(e) => {
                    const checked = e.target.checked
                    setIsGeneral(checked)
                    if (checked) {
                      setSelectedAppCode('')
                    }
                  }}
                  style={{
                    cursor: 'pointer'
                  }}
                />
                <label
                  htmlFor="general-checkbox"
                  style={{
                    fontSize: '0.875rem',
                    color: colors.textSecondary,
                    cursor: 'pointer'
                  }}
                >
                  General Knowledge
                </label>
              </div>

              {/* App Code Selection */}
              {!isGeneral && (
                <div style={{ flex: 1 }}>
                  <label style={{
                    display: 'block',
                    marginBottom: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: colors.textSecondary
                  }}>
                    App Code <span style={{ color: colors.error }}>*</span>
                  </label>
                  <select
                    value={selectedAppCode}
                    onChange={(e) => setSelectedAppCode(e.target.value)}
                    style={{
                      width: '200px',
                      padding: '0.5rem',
                      borderRadius: '4px',
                      border: `1px solid ${colors.borderSubtle}`,
                      backgroundColor: colors.bgCard,
                      color: colors.textPrimary,
                      fontSize: '0.875rem',
                      cursor: 'pointer'
                    }}
                  >
                    <option value="">Select App Code</option>
                    <option value="kumo">kumo</option>
                    <option value="heroes">heroes</option>
                    <option value="puzzle">puzzle</option>
                    <option value="adventure">adventure</option>
                    <option value="strategy">strategy</option>
                    <option value="goblinslayer">goblinslayer</option>
                    <option value="tsukimichi">tsukimichi</option>
                    <option value="vividarmy">vividarmy</option>
                  </select>
                </div>
              )}
            </div>
          </div>

          {/* Question & Answer */}
          <div style={{
            padding: '1rem',
            border: `1px solid ${colors.borderSubtle}`,
            borderRadius: '8px',
            backgroundColor: colors.bgElevated
          }}>
            <h4 style={{
              margin: '0 0 1rem 0',
              fontSize: '0.875rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Question & Answer
            </h4>
            
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.textSecondary
              }}>
                Question <span style={{ color: colors.error }}>*</span>
              </label>
              <textarea
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                placeholder="Enter the question..."
                style={{
                  width: '100%',
                  minHeight: '80px',
                  padding: '0.75rem',
                  borderRadius: '4px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgCard,
                  color: colors.textPrimary,
                  fontSize: '0.875rem',
                  resize: 'vertical',
                  outline: 'none'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '0.5rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.textSecondary
              }}>
                Answer <span style={{ color: colors.error }}>*</span>
              </label>
              <textarea
                value={answer}
                onChange={(e) => setAnswer(e.target.value)}
                placeholder="Enter the answer..."
                style={{
                  width: '100%',
                  minHeight: '120px',
                  padding: '0.75rem',
                  borderRadius: '4px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgCard,
                  color: colors.textPrimary,
                  fontSize: '0.875rem',
                  resize: 'vertical',
                  outline: 'none'
                }}
              />
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div style={{
          padding: '1.5rem',
          borderTop: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '1rem'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              border: `1px solid ${colors.borderSubtle}`,
              backgroundColor: 'transparent',
              color: colors.textSecondary,
              fontSize: '0.875rem',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={!question.trim() || !answer.trim() || (!isGeneral && !selectedAppCode)}
            style={{
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              border: 'none',
              backgroundColor: (!question.trim() || !answer.trim() || (!isGeneral && !selectedAppCode)) 
                ? colors.bgSubtle 
                : colors.primary,
              color: (!question.trim() || !answer.trim() || (!isGeneral && !selectedAppCode))
                ? colors.textTertiary
                : 'white',
              fontSize: '0.875rem',
              cursor: (!question.trim() || !answer.trim() || (!isGeneral && !selectedAppCode))
                ? 'not-allowed'
                : 'pointer'
            }}
          >
            Submit for Approval
          </button>
        </div>
      </div>
    </div>
  )
} 