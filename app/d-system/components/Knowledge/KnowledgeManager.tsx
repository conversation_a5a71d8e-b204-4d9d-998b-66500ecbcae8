'use client'
import React, { useState } from 'react'
import { Colors, KnowledgeTask, KnowledgeFilter, KnowledgeDetailData, KnowledgeBaseItem, KnowledgeBaseFilter } from '../../types'
import { knowledgeDetailData, knowledgeBaseItems } from '../../data/mockData'
import KnowledgeTaskList from './KnowledgeTaskList'
import KnowledgeFilters from './KnowledgeFilters'
import KnowledgeDetailModal from './KnowledgeDetailModal'
import KnowledgeBaseList from './KnowledgeBaseList'
import KnowledgeBaseFilters from './KnowledgeBaseFilters'
import KnowledgeBaseEditModal from './KnowledgeBaseEditModal'

interface KnowledgeManagerProps {
  colors: Colors
  theme: 'light' | 'dark'
  tasks: KnowledgeTask[]
  onBackToCSAgent?: () => void
  systemType?: 'cs' | 'i18n'  // 新增系统类型参数
}

export default function KnowledgeManager({ colors, theme, tasks, onBackToCSAgent, systemType = 'cs' }: KnowledgeManagerProps) {
  const [activeTab, setActiveTab] = useState<'tasks' | 'base'>('tasks')
  const [filters, setFilters] = useState<KnowledgeFilter>({
    appCode: '',
    general: '',
    status: '',
    keyword: ''
  })
  const [baseFilters, setBaseFilters] = useState<KnowledgeBaseFilter>({
    appCode: '',
    general: '',
    keyword: ''
  })
  const [selectedTask, setSelectedTask] = useState<string | null>(null)
  const [selectedBaseItem, setSelectedBaseItem] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalData, setModalData] = useState<KnowledgeDetailData | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editItem, setEditItem] = useState<KnowledgeBaseItem | null>(null)

  const handleFilterChange = (newFilters: Partial<KnowledgeFilter>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }

  const handleBaseFilterChange = (newFilters: Partial<KnowledgeBaseFilter>) => {
    setBaseFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }

  const handleTaskClick = (taskId: string) => {
    setSelectedTask(taskId)
    const data = knowledgeDetailData[taskId]
    if (data) {
      setModalData(data)
      setIsModalOpen(true)
    }
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setModalData(null)
  }

  const handleEditBaseItem = (item: KnowledgeBaseItem) => {
    setEditItem(item)
    setIsEditModalOpen(true)
  }

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false)
    setEditItem(null)
  }

  const handleReset = () => {
    if (activeTab === 'tasks') {
      setFilters({
        appCode: '',
        general: '',
        status: '',
        keyword: ''
      })
    } else {
      setBaseFilters({
        appCode: '',
        general: '',
        keyword: ''
      })
    }
  }

  const filteredTasks = tasks.filter(task => {
    const matchesAppCode = !filters.appCode || task.appCode.toLowerCase().includes(filters.appCode.toLowerCase())
    const matchesGeneral = !filters.general || 
      (filters.general === 'Yes' && task.general) || 
      (filters.general === 'No' && !task.general)
    const matchesStatus = !filters.status || task.status === filters.status
    const matchesKeyword = !filters.keyword || 
      task.question.toLowerCase().includes(filters.keyword.toLowerCase()) ||
      task.assignedTo.toLowerCase().includes(filters.keyword.toLowerCase())

    return matchesAppCode && matchesGeneral && matchesStatus && matchesKeyword
  })

  const filteredBaseItems = knowledgeBaseItems.filter(item => {
    const matchesAppCode = !baseFilters.appCode || item.appCode.toLowerCase().includes(baseFilters.appCode.toLowerCase())
    const matchesGeneral = !baseFilters.general || 
      (baseFilters.general === 'Yes' && item.general) || 
      (baseFilters.general === 'No' && !item.general)
    const matchesKeyword = !baseFilters.keyword || 
      item.question.toLowerCase().includes(baseFilters.keyword.toLowerCase()) ||
      item.answer.toLowerCase().includes(baseFilters.keyword.toLowerCase()) ||
      item.name.toLowerCase().includes(baseFilters.keyword.toLowerCase())

    return matchesAppCode && matchesGeneral && matchesKeyword
  })

  return (
    <div style={{
      padding: '1.5rem',
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      backgroundColor: colors.bgMain,
      boxSizing: 'border-box'
    }}>
      {/* Header with back button */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '1.5rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          {onBackToCSAgent && (
            <button
              onClick={onBackToCSAgent}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: `1px solid ${colors.borderSubtle}`,
                backgroundColor: colors.bgCard,
                color: colors.textSecondary,
                fontSize: '0.875rem',
                cursor: 'pointer',
                transition: 'all 0.15s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.bgElevated
                e.currentTarget.style.borderColor = colors.borderStrong
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = colors.bgCard
                e.currentTarget.style.borderColor = colors.borderSubtle
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
              </svg>
              Back to {systemType === 'cs' ? 'CS Expert' : 'i18n Expert'}
            </button>
          )}
          <h1 style={{
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600',
            color: colors.textPrimary
          }}>
            {systemType === 'cs' ? 'CS Expert' : 'i18n Expert'} Knowledge Management
          </h1>
        </div>
      </div>

      {/* Tab Navigation with buttons */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottom: `1px solid ${colors.borderSubtle}`,
        marginBottom: '1.5rem'
      }}>
        <div style={{ display: 'flex' }}>
          <button
            onClick={() => setActiveTab('tasks')}
            style={{
              padding: '0.75rem 1.5rem',
              borderBottom: activeTab === 'tasks' ? `2px solid ${colors.primary}` : '2px solid transparent',
              backgroundColor: 'transparent',
              border: 'none',
              color: activeTab === 'tasks' ? colors.primary : colors.textSecondary,
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.15s ease'
            }}
          >
            Knowledge Tasks
          </button>
          <button
            onClick={() => setActiveTab('base')}
            style={{
              padding: '0.75rem 1.5rem',
              borderBottom: activeTab === 'base' ? `2px solid ${colors.primary}` : '2px solid transparent',
              backgroundColor: 'transparent',
              border: 'none',
              color: activeTab === 'base' ? colors.primary : colors.textSecondary,
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'all 0.15s ease'
            }}
          >
            Knowledge Base
          </button>
        </div>

        <div style={{
          display: 'flex',
          gap: '0.5rem'
        }}>
          {activeTab === 'base' && (
            <button
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: `1px solid ${colors.borderSubtle}`,
                backgroundColor: colors.bgCard,
                color: colors.textSecondary,
                fontSize: '0.875rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" />
              </svg>
              Chat Test
            </button>
          )}
          <button
            style={{
              padding: '0.5rem 1rem',
              borderRadius: '6px',
              border: 'none',
              backgroundColor: colors.primary,
              color: 'white',
              fontSize: '0.875rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            New
          </button>
        </div>
      </div>

      {/* Content Based on Active Tab */}
      {activeTab === 'tasks' ? (
        <>
          {/* Task Filters */}
          <KnowledgeFilters
            filters={filters}
            colors={colors}
            onFilterChange={handleFilterChange}
            onReset={handleReset}
          />

          {/* Task List */}
          <KnowledgeTaskList
            tasks={filteredTasks}
            colors={colors}
            onTaskClick={handleTaskClick}
            selectedTask={selectedTask}
          />
        </>
      ) : (
        <>
          {/* Base Filters */}
          <KnowledgeBaseFilters
            filters={baseFilters}
            colors={colors}
            onFilterChange={handleBaseFilterChange}
            onReset={handleReset}
          />

          {/* Base List */}
          <KnowledgeBaseList
            items={filteredBaseItems}
            colors={colors}
            selectedItem={selectedBaseItem}
            onEdit={handleEditBaseItem}
          />
        </>
      )}

      {/* Knowledge Detail Modal */}
      <KnowledgeDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        data={modalData}
        colors={colors}
      />

      {/* Knowledge Base Edit Modal */}
      <KnowledgeBaseEditModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        item={editItem}
        colors={colors}
      />
    </div>
  )
} 