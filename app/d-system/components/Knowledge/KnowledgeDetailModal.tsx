'use client'
import React, { useState } from 'react'
import { Colors, KnowledgeDetailData, QAPair, KnowledgeSuggestion } from '../../types'

interface KnowledgeDetailModalProps {
  isOpen: boolean
  onClose: () => void
  data: KnowledgeDetailData | null
  colors: Colors
}

export default function KnowledgeDetailModal({ isOpen, onClose, data, colors }: KnowledgeDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'knowledge' | 'suggestions'>('knowledge')
  const [qaPairs, setQAPairs] = useState<QAPair[]>(data?.qaPairs || [])
  const [suggestions, setSuggestions] = useState<KnowledgeSuggestion[]>(data?.suggestions || [])
  const [selectedAppCode, setSelectedAppCode] = useState<string>(data?.task.appCode || '')
  const [isGeneral, setIsGeneral] = useState<boolean>(data?.task.general || false)

  React.useEffect(() => {
    if (data) {
      setQAPairs(data.qaPairs)
      setSuggestions(data.suggestions)
      setSelectedAppCode(data.task.appCode || '')
      setIsGeneral(data.task.general || false)
    }
  }, [data])

  if (!isOpen || !data) return null

  // 根据状态判断是否可编辑
  const isEditable = data.task.status === 'Draft' || data.task.status === 'Revision'
  const isReviewingState = data.task.status === 'Reviewing'
  const isCompletedState = data.task.status === 'Completed'
  const isRevisionState = data.task.status === 'Revision'

  const addQAPair = () => {
    if (!isEditable) return
    const newPair: QAPair = {
      id: `qa-${Date.now()}`,
      question: '',
      answer: ''
    }
    setQAPairs([...qaPairs, newPair])
  }

  const updateQAPair = (id: string, field: 'question' | 'answer', value: string) => {
    if (!isEditable) return
    setQAPairs(qaPairs.map(pair => 
      pair.id === id ? { ...pair, [field]: value } : pair
    ))
  }

  const removeQAPair = (id: string) => {
    if (!isEditable || qaPairs.length <= 1) return
    setQAPairs(qaPairs.filter(pair => pair.id !== id))
  }

  const toggleSuggestion = (suggestionId: string) => {
    if (!isEditable) return
    setSuggestions(suggestions.map(suggestion =>
      suggestion.id === suggestionId 
        ? { ...suggestion, isSelected: !suggestion.isSelected }
        : suggestion
    ))
  }

  const duplicateSuggestions = suggestions.filter(s => s.type === 'duplicate')
  const conflictSuggestions = suggestions.filter(s => s.type === 'conflict')

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: colors.bgCard,
        borderRadius: '12px',
        width: '90%',
        maxWidth: '1000px',
        maxHeight: '90%',
        display: 'flex',
        flexDirection: 'column',
        border: `1px solid ${colors.borderSubtle}`
      }}>
        {/* Modal Header */}
        <div style={{
          padding: '1.5rem',
          borderBottom: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h2 style={{
              margin: 0,
              fontSize: '1.25rem',
              fontWeight: '600',
              color: colors.textPrimary
            }}>
              Knowledge Task Details
            </h2>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: '0.25rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: colors.textSecondary,
                  fontFamily: 'monospace'
                }}>
                  Task ID: {data.task.taskId}
                </p>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(data.task.taskId)
                  }}
                  style={{
                    background: 'none',
                    border: `1px solid ${colors.borderSubtle}`,
                    borderRadius: '4px',
                    padding: '0.25rem',
                    cursor: 'pointer',
                    color: colors.textSecondary,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  title="Copy Task ID"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '14px', height: '14px'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 01-.75.75H9a.75.75 0 01-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 01-2.25 2.25H6.75A2.25 2.25 0 014.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 011.927-.184" />
                  </svg>
                </button>
              </div>
              <span style={{
                padding: '0.25rem 0.5rem',
                borderRadius: '12px',
                fontSize: '0.75rem',
                fontWeight: '500',
                color: data.task.status === 'Completed' ? colors.success : 
                       data.task.status === 'Reviewing' ? colors.primary :
                       data.task.status === 'Revision' ? colors.error : colors.accent,
                backgroundColor: data.task.status === 'Completed' ? `${colors.success}15` : 
                                data.task.status === 'Reviewing' ? `${colors.primary}15` :
                                data.task.status === 'Revision' ? `${colors.error}15` : `${colors.accent}15`
              }}>
                {data.task.status}
              </span>
              {!isEditable && (
                <span style={{
                  fontSize: '0.75rem',
                  color: colors.textTertiary,
                  fontStyle: 'italic'
                }}>
                  {isReviewingState ? 'Review in progress - editing disabled' :
                   isCompletedState ? 'Approved - editing disabled' : 'Read only'}
                </span>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              color: colors.textTertiary,
              padding: '0.5rem',
              borderRadius: '4px'
            }}
          >
            ✕
          </button>
        </div>

        {/* Tab Navigation */}
        <div style={{
          display: 'flex',
          borderBottom: `1px solid ${colors.borderSubtle}`,
          backgroundColor: colors.bgElevated
        }}>
          <button
            onClick={() => setActiveTab('knowledge')}
            style={{
              padding: '1rem 2rem',
              border: 'none',
              background: 'none',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: activeTab === 'knowledge' ? colors.primary : colors.textSecondary,
              borderBottom: activeTab === 'knowledge' ? `2px solid ${colors.primary}` : '2px solid transparent'
            }}
          >
            Add Knowledge
          </button>
          <button
            onClick={() => setActiveTab('suggestions')}
            style={{
              padding: '1rem 2rem',
              border: 'none',
              background: 'none',
              cursor: 'pointer',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: activeTab === 'suggestions' ? colors.primary : colors.textSecondary,
              borderBottom: activeTab === 'suggestions' ? `2px solid ${colors.primary}` : '2px solid transparent'
            }}
          >
            Suggestions ({suggestions.length})
          </button>
        </div>

        {/* Tab Content */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '1.5rem'
        }}>
          {activeTab === 'knowledge' && (
            <div>
              {/* 驳回原因展示 */}
              {isRevisionState && data.rejectionReason && (
                <div style={{
                  padding: '1rem',
                  marginBottom: '1.5rem',
                  backgroundColor: `${colors.error}10`,
                  border: `1px solid ${colors.error}30`,
                  borderRadius: '8px'
                }}>
                  <h4 style={{
                    margin: '0 0 0.5rem 0',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    color: colors.error
                  }}>
                    Rejection Reason
                  </h4>
                  <p style={{
                    margin: 0,
                    fontSize: '0.875rem',
                    color: colors.textPrimary,
                    lineHeight: '1.5'
                  }}>
                    {data.rejectionReason}
                  </p>
                </div>
              )}

              {/* App Code and General Selection */}
              <div style={{
                padding: '1rem',
                marginBottom: '1.5rem',
                border: `1px solid ${colors.borderSubtle}`,
                borderRadius: '8px',
                backgroundColor: colors.bgElevated
              }}>
                <h4 style={{
                  margin: '0 0 1rem 0',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: colors.textPrimary
                }}>
                  Knowledge Configuration
                </h4>
                
                <div style={{ display: 'flex', gap: '1.5rem', alignItems: 'flex-start' }}>
                  {/* General Checkbox */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <input
                      type="checkbox"
                      id="general-checkbox"
                      checked={isGeneral}
                      onChange={(e) => {
                        if (!isEditable) return
                        const checked = e.target.checked
                        setIsGeneral(checked)
                        if (checked) {
                          setSelectedAppCode('')
                        }
                      }}
                      disabled={!isEditable}
                      style={{
                        cursor: isEditable ? 'pointer' : 'not-allowed'
                      }}
                    />
                    <label
                      htmlFor="general-checkbox"
                      style={{
                        fontSize: '0.875rem',
                        color: colors.textSecondary,
                        cursor: isEditable ? 'pointer' : 'not-allowed'
                      }}
                    >
                      General Knowledge
                    </label>
                  </div>

                  {/* App Code Selection */}
                  {!isGeneral && (
                    <div style={{ flex: 1 }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: colors.textSecondary
                      }}>
                        App Code <span style={{ color: colors.error }}>*</span>
                      </label>
                      <select
                        value={selectedAppCode}
                        onChange={(e) => {
                          if (!isEditable) return
                          setSelectedAppCode(e.target.value)
                        }}
                        disabled={!isEditable}
                        style={{
                          width: '200px',
                          padding: '0.5rem',
                          borderRadius: '4px',
                          border: `1px solid ${colors.borderSubtle}`,
                          backgroundColor: isEditable ? colors.bgCard : colors.bgSubtle,
                          color: colors.textPrimary,
                          fontSize: '0.875rem',
                          cursor: isEditable ? 'pointer' : 'not-allowed'
                        }}
                      >
                        <option value="">Select App Code</option>
                        <option value="kumo">kumo</option>
                        <option value="heroes">heroes</option>
                        <option value="puzzle">puzzle</option>
                        <option value="adventure">adventure</option>
                        <option value="strategy">strategy</option>
                      </select>
                    </div>
                  )}
                </div>
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1rem'
              }}>
                <h3 style={{
                  margin: 0,
                  fontSize: '1rem',
                  fontWeight: '600',
                  color: colors.textPrimary
                }}>
                  Question & Answer Pairs
                </h3>
                {isEditable && (
                  <button
                    onClick={addQAPair}
                    style={{
                      padding: '0.5rem 1rem',
                      borderRadius: '4px',
                      border: 'none',
                      backgroundColor: colors.primary,
                      color: 'white',
                      fontSize: '0.875rem',
                      cursor: 'pointer'
                    }}
                  >
                    + Add Q&A Pair
                  </button>
                )}
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {qaPairs.map((pair, index) => (
                  <div
                    key={pair.id}
                    style={{
                      padding: '1rem',
                      border: `1px solid ${colors.borderSubtle}`,
                      borderRadius: '8px',
                      backgroundColor: colors.bgElevated
                    }}
                  >
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '1rem'
                    }}>
                      <span style={{
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: colors.textSecondary
                      }}>
                        Q&A Pair #{index + 1}
                      </span>
                      {isEditable && qaPairs.length > 1 && (
                        <button
                          onClick={() => removeQAPair(pair.id)}
                          style={{
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            color: colors.error,
                            padding: '0.25rem'
                          }}
                        >
                          🗑️
                        </button>
                      )}
                    </div>
                    
                    <div style={{ marginBottom: '1rem' }}>
                      <label style={{
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: colors.textSecondary
                      }}>
                        Question
                      </label>
                      <textarea
                        value={pair.question}
                        onChange={(e) => updateQAPair(pair.id, 'question', e.target.value)}
                        placeholder="Enter the question..."
                        disabled={!isEditable}
                        style={{
                          width: '100%',
                          minHeight: '60px',
                          padding: '0.75rem',
                          borderRadius: '4px',
                          border: `1px solid ${colors.borderSubtle}`,
                          backgroundColor: isEditable ? colors.bgCard : colors.bgSubtle,
                          color: colors.textPrimary,
                          fontSize: '0.875rem',
                          resize: 'vertical',
                          outline: 'none',
                          cursor: isEditable ? 'text' : 'not-allowed'
                        }}
                      />
                    </div>

                    <div>
                      <label style={{
                        display: 'block',
                        marginBottom: '0.5rem',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: colors.textSecondary
                      }}>
                        Answer
                      </label>
                      <textarea
                        value={pair.answer}
                        onChange={(e) => updateQAPair(pair.id, 'answer', e.target.value)}
                        placeholder="Enter the answer..."
                        disabled={!isEditable}
                        style={{
                          width: '100%',
                          minHeight: '100px',
                          padding: '0.75rem',
                          borderRadius: '4px',
                          border: `1px solid ${colors.borderSubtle}`,
                          backgroundColor: isEditable ? colors.bgCard : colors.bgSubtle,
                          color: colors.textPrimary,
                          fontSize: '0.875rem',
                          resize: 'vertical',
                          outline: 'none',
                          cursor: isEditable ? 'text' : 'not-allowed'
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'suggestions' && (
            <div>
              {suggestions.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '3rem',
                  color: colors.textTertiary
                }}>
                  No suggestions available for this task.
                </div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
                  {/* Duplicate Knowledge Suggestions */}
                  {duplicateSuggestions.length > 0 && (
                    <div>
                      <h3 style={{
                        margin: '0 0 1rem 0',
                        fontSize: '1rem',
                        fontWeight: '600',
                        color: colors.textPrimary
                      }}>
                        Duplicate Knowledge Suggestions
                      </h3>
                      
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                        {duplicateSuggestions.map((suggestion) => (
                          <div
                            key={suggestion.id}
                            style={{
                              padding: '1rem',
                              border: `1px solid ${colors.borderSubtle}`,
                              borderRadius: '8px',
                              backgroundColor: colors.bgElevated
                            }}
                          >
                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              marginBottom: '1rem'
                            }}>
                              <input
                                type="checkbox"
                                checked={suggestion.isSelected}
                                onChange={() => toggleSuggestion(suggestion.id)}
                                disabled={!isEditable}
                                style={{
                                  width: '16px',
                                  height: '16px',
                                  accentColor: colors.primary,
                                  cursor: isEditable ? 'pointer' : 'not-allowed'
                                }}
                              />
                              <span style={{
                                fontSize: '0.875rem',
                                fontWeight: '500',
                                color: suggestion.isSelected ? colors.error : colors.textSecondary
                              }}>
                                {suggestion.isSelected ? 'Delete existing knowledge' : 'Keep existing knowledge'}
                              </span>
                            </div>

                            <div style={{
                              display: 'grid',
                              gridTemplateColumns: '1fr 1fr',
                              gap: '1rem'
                            }}>
                              <div>
                                <h4 style={{
                                  margin: '0 0 0.5rem 0',
                                  fontSize: '0.875rem',
                                  fontWeight: '500',
                                  color: colors.textSecondary
                                }}>
                                  Existing Knowledge
                                </h4>
                                <div style={{
                                  padding: '0.75rem',
                                  backgroundColor: colors.bgCard,
                                  borderRadius: '4px',
                                  border: `1px solid ${colors.borderSubtle}`
                                }}>
                                  <p style={{
                                    margin: '0 0 0.5rem 0',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: colors.textPrimary
                                  }}>
                                    Q: {suggestion.existingQA.question}
                                  </p>
                                  <p style={{
                                    margin: 0,
                                    fontSize: '0.875rem',
                                    color: colors.textSecondary
                                  }}>
                                    A: {suggestion.existingQA.answer}
                                  </p>
                                </div>
                              </div>

                              <div>
                                <h4 style={{
                                  margin: '0 0 0.5rem 0',
                                  fontSize: '0.875rem',
                                  fontWeight: '500',
                                  color: colors.textSecondary
                                }}>
                                  New Knowledge
                                </h4>
                                <div style={{
                                  padding: '0.75rem',
                                  backgroundColor: colors.bgCard,
                                  borderRadius: '4px',
                                  border: `1px solid ${colors.borderSubtle}`
                                }}>
                                  <p style={{
                                    margin: '0 0 0.5rem 0',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: colors.textPrimary
                                  }}>
                                    Q: {suggestion.newQA.question}
                                  </p>
                                  <p style={{
                                    margin: 0,
                                    fontSize: '0.875rem',
                                    color: colors.textSecondary
                                  }}>
                                    A: {suggestion.newQA.answer}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Conflict Knowledge Suggestions */}
                  {conflictSuggestions.length > 0 && (
                    <div>
                      <h3 style={{
                        margin: '0 0 1rem 0',
                        fontSize: '1rem',
                        fontWeight: '600',
                        color: colors.textPrimary
                      }}>
                        Conflict Knowledge Suggestions
                      </h3>
                      
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                        {conflictSuggestions.map((suggestion) => (
                          <div
                            key={suggestion.id}
                            style={{
                              padding: '1rem',
                              border: `1px solid ${colors.borderSubtle}`,
                              borderRadius: '8px',
                              backgroundColor: colors.bgElevated
                            }}
                          >
                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              marginBottom: '1rem'
                            }}>
                              <input
                                type="checkbox"
                                checked={suggestion.isSelected}
                                onChange={() => toggleSuggestion(suggestion.id)}
                                disabled={!isEditable}
                                style={{
                                  width: '16px',
                                  height: '16px',
                                  accentColor: colors.error,
                                  cursor: isEditable ? 'pointer' : 'not-allowed'
                                }}
                              />
                              <span style={{
                                fontSize: '0.875rem',
                                fontWeight: '500',
                                color: suggestion.isSelected ? colors.error : colors.textSecondary
                              }}>
                                {suggestion.isSelected ? 'Delete existing knowledge' : 'Keep existing knowledge'}
                              </span>
                            </div>

                            <div style={{
                              display: 'grid',
                              gridTemplateColumns: '1fr 1fr',
                              gap: '1rem'
                            }}>
                              <div>
                                <h4 style={{
                                  margin: '0 0 0.5rem 0',
                                  fontSize: '0.875rem',
                                  fontWeight: '500',
                                  color: colors.textSecondary
                                }}>
                                  Existing Knowledge (Conflicting)
                                </h4>
                                <div style={{
                                  padding: '0.75rem',
                                  backgroundColor: colors.bgCard,
                                  borderRadius: '4px',
                                  border: `1px solid ${colors.error}30`
                                }}>
                                  <p style={{
                                    margin: '0 0 0.5rem 0',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: colors.textPrimary
                                  }}>
                                    Q: {suggestion.existingQA.question}
                                  </p>
                                  <p style={{
                                    margin: 0,
                                    fontSize: '0.875rem',
                                    color: colors.textSecondary
                                  }}>
                                    A: {suggestion.existingQA.answer}
                                  </p>
                                </div>
                              </div>

                              <div>
                                <h4 style={{
                                  margin: '0 0 0.5rem 0',
                                  fontSize: '0.875rem',
                                  fontWeight: '500',
                                  color: colors.textSecondary
                                }}>
                                  New Knowledge
                                </h4>
                                <div style={{
                                  padding: '0.75rem',
                                  backgroundColor: colors.bgCard,
                                  borderRadius: '4px',
                                  border: `1px solid ${colors.success}30`
                                }}>
                                  <p style={{
                                    margin: '0 0 0.5rem 0',
                                    fontSize: '0.875rem',
                                    fontWeight: '500',
                                    color: colors.textPrimary
                                  }}>
                                    Q: {suggestion.newQA.question}
                                  </p>
                                  <p style={{
                                    margin: 0,
                                    fontSize: '0.875rem',
                                    color: colors.textSecondary
                                  }}>
                                    A: {suggestion.newQA.answer}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div style={{
          padding: '1.5rem',
          borderTop: `1px solid ${colors.borderSubtle}`,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '1rem'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '0.75rem 1.5rem',
              borderRadius: '4px',
              border: `1px solid ${colors.borderSubtle}`,
              backgroundColor: colors.bgElevated,
              color: colors.textSecondary,
              fontSize: '0.875rem',
              cursor: 'pointer'
            }}
          >
            {isEditable ? 'Cancel' : 'Close'}
          </button>
          
          {/* 根据状态显示不同的操作按钮 */}
          {data.task.status === 'Draft' && (
            <button
              style={{
                padding: '0.75rem 1.5rem',
                borderRadius: '4px',
                border: 'none',
                backgroundColor: colors.primary,
                color: 'white',
                fontSize: '0.875rem',
                cursor: 'pointer'
              }}
            >
              Submit for Review
            </button>
          )}
          
          {data.task.status === 'Revision' && (
            <>
              <button
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '4px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgElevated,
                  color: colors.textSecondary,
                  fontSize: '0.875rem',
                  cursor: 'pointer'
                }}
              >
                Save Changes
              </button>
              <button
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: colors.primary,
                  color: 'white',
                  fontSize: '0.875rem',
                  cursor: 'pointer'
                }}
              >
                Submit for Review
              </button>
            </>
          )}
          
          {data.task.status === 'Reviewing' && (
            <span style={{
              padding: '0.75rem 1.5rem',
              fontSize: '0.875rem',
              color: colors.textTertiary,
              fontStyle: 'italic'
            }}>
              Review in progress...
            </span>
          )}
          
          {data.task.status === 'Completed' && (
            <span style={{
              padding: '0.75rem 1.5rem',
              fontSize: '0.875rem',
              color: colors.success,
              fontWeight: '500'
            }}>
              ✓ Approved
            </span>
          )}
        </div>
      </div>
    </div>
  )
} 