'use client'
import React from 'react'
import { Colors, KnowledgeBaseFilter } from '../../types'

interface KnowledgeBaseFiltersProps {
  filters: KnowledgeBaseFilter
  colors: Colors
  onFilterChange: (filters: Partial<KnowledgeBaseFilter>) => void
  onReset: () => void
}

export default function KnowledgeBaseFilters({ 
  filters, 
  colors, 
  onFilterChange, 
  onReset 
}: KnowledgeBaseFiltersProps) {
  const appCodeOptions = [
    { value: '', label: 'All Apps' },
    { value: 'goblinslayer', label: 'Goblin Slayer' },
    { value: 'tsukimichi', label: 'Tsukimichi' },
    { value: 'vividarmy', label: 'Vivid Army' }
  ]

  const generalOptions = [
    { value: '', label: 'All' },
    { value: 'Yes', label: 'Yes' },
    { value: 'No', label: 'No' }
  ]

  return (
    <div style={{
      display: 'flex',
      gap: '1rem',
      alignItems: 'center',
      marginBottom: '1.5rem',
      padding: '1rem',
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      border: `1px solid ${colors.borderSubtle}`
    }}>
      {/* App Code Filter */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
        <label style={{
          fontSize: '0.75rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          App Code
        </label>
        <select
          value={filters.appCode}
          onChange={(e) => onFilterChange({ appCode: e.target.value })}
          style={{
            padding: '0.5rem 0.75rem',
            borderRadius: '6px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem',
            minWidth: '120px',
            cursor: 'pointer'
          }}
        >
          {appCodeOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* General Filter */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
        <label style={{
          fontSize: '0.75rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          General
        </label>
        <select
          value={filters.general}
          onChange={(e) => onFilterChange({ general: e.target.value })}
          style={{
            padding: '0.5rem 0.75rem',
            borderRadius: '6px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem',
            minWidth: '80px',
            cursor: 'pointer'
          }}
        >
          {generalOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Keyword Search */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem', flex: 1 }}>
        <label style={{
          fontSize: '0.75rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          Keyword
        </label>
        <div style={{ position: 'relative' }}>
          <input
            type="text"
            placeholder="Search in questions and answers..."
            value={filters.keyword}
            onChange={(e) => onFilterChange({ keyword: e.target.value })}
            style={{
              width: '100%',
              padding: '0.5rem 2.5rem 0.5rem 0.75rem',
              borderRadius: '6px',
              border: `1px solid ${colors.borderSubtle}`,
              backgroundColor: colors.bgElevated,
              color: colors.textPrimary,
              fontSize: '0.875rem',
              outline: 'none'
            }}
          />
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24" 
            strokeWidth={1.5} 
            stroke="currentColor" 
            style={{
              position: 'absolute',
              right: '0.75rem',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '16px',
              height: '16px',
              color: colors.textTertiary,
              pointerEvents: 'none'
            }}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
          </svg>
        </div>
      </div>

      {/* Action Buttons */}
      <div style={{ 
        display: 'flex', 
        gap: '0.5rem',
        alignSelf: 'flex-end',
        paddingBottom: '0.25rem'
      }}>
        <button
          onClick={() => {
            // Search functionality is handled by real-time filtering
          }}
          style={{
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            border: 'none',
            backgroundColor: colors.primary,
            color: 'white',
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'background-color 0.15s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.opacity = '0.9'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.opacity = '1'
          }}
        >
          Search
        </button>
        <button
          onClick={onReset}
          style={{
            padding: '0.5rem 1rem',
            borderRadius: '6px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgCard,
            color: colors.textSecondary,
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.15s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = colors.bgElevated
            e.currentTarget.style.borderColor = colors.borderStrong
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = colors.bgCard
            e.currentTarget.style.borderColor = colors.borderSubtle
          }}
        >
          Reset
        </button>
      </div>
    </div>
  )
} 