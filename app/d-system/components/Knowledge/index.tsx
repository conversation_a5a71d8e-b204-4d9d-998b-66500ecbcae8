'use client'
import React, { useState } from 'react'
import { Colors, KnowledgeTask, KnowledgeFilter, KnowledgeDetailData, KnowledgeBaseItem, KnowledgeBaseFilter } from '../../types'
import { knowledgeDetailData, knowledgeBaseItems } from '../../data/mockData'
import KnowledgeTaskList from './KnowledgeTaskList'
import KnowledgeFilters from './KnowledgeFilters'
import KnowledgeDetailModal from './KnowledgeDetailModal'
import KnowledgeBaseList from './KnowledgeBaseList'
import KnowledgeBaseFilters from './KnowledgeBaseFilters'

interface KnowledgeProps {
  colors: Colors
  theme: 'light' | 'dark'
  tasks: KnowledgeTask[]
  onBackToCSAgent?: () => void
}

export default function Knowledge({ colors, theme, tasks, onBackToCSAgent }: KnowledgeProps) {
  const [activeTab, setActiveTab] = useState<'tasks' | 'base'>('tasks')
  const [filters, setFilters] = useState<KnowledgeFilter>({
    appCode: '',
    general: '',
    status: '',
    keyword: ''
  })
  const [baseFilters, setBaseFilters] = useState<KnowledgeBaseFilter>({
    appCode: '',
    general: '',
    keyword: ''
  })
  const [selectedTask, setSelectedTask] = useState<string | null>(null)
  const [selectedBaseItem, setSelectedBaseItem] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalData, setModalData] = useState<KnowledgeDetailData | null>(null)

  const handleFilterChange = (newFilters: Partial<KnowledgeFilter>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }

  const handleBaseFilterChange = (newFilters: Partial<KnowledgeBaseFilter>) => {
    setBaseFilters(prev => ({
      ...prev,
      ...newFilters
    }))
  }

  const handleTaskClick = (taskId: string) => {
    setSelectedTask(taskId)
    const data = knowledgeDetailData[taskId]
    if (data) {
      setModalData(data)
      setIsModalOpen(true)
    }
  }

  const handleBaseItemEdit = (item: any) => {
    // 处理知识库条目编辑
    console.log('Edit knowledge base item:', item)
    // 这里可以添加编辑逻辑
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setModalData(null)
  }

  const handleReset = () => {
    if (activeTab === 'tasks') {
      setFilters({
        appCode: '',
        general: '',
        status: '',
        keyword: ''
      })
    } else {
      setBaseFilters({
        appCode: '',
        general: '',
        keyword: ''
      })
    }
  }

  const filteredTasks = tasks.filter(task => {
    const matchesAppCode = !filters.appCode || task.appCode.toLowerCase().includes(filters.appCode.toLowerCase())
    const matchesGeneral = !filters.general || 
      (filters.general === 'Yes' && task.general) || 
      (filters.general === 'No' && !task.general)
    const matchesStatus = !filters.status || task.status === filters.status
    const matchesKeyword = !filters.keyword || 
      task.question.toLowerCase().includes(filters.keyword.toLowerCase()) ||
      task.assignedTo.toLowerCase().includes(filters.keyword.toLowerCase())

    return matchesAppCode && matchesGeneral && matchesStatus && matchesKeyword
  })

  const filteredBaseItems = knowledgeBaseItems.filter(item => {
    const matchesAppCode = !baseFilters.appCode || item.appCode.toLowerCase().includes(baseFilters.appCode.toLowerCase())
    const matchesGeneral = !baseFilters.general || 
      (baseFilters.general === 'Yes' && item.general) || 
      (baseFilters.general === 'No' && !item.general)
    const matchesKeyword = !baseFilters.keyword || 
      item.question.toLowerCase().includes(baseFilters.keyword.toLowerCase()) ||
      item.answer.toLowerCase().includes(baseFilters.keyword.toLowerCase()) ||
      item.name.toLowerCase().includes(baseFilters.keyword.toLowerCase())

    return matchesAppCode && matchesGeneral && matchesKeyword
  })

  return (
    <div style={{
      padding: '1.5rem',
      height: '100%',
      backgroundColor: colors.bgMain
    }}>
      {/* Header with back button */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '1.5rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          {onBackToCSAgent && (
            <button
              onClick={onBackToCSAgent}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                border: `1px solid ${colors.borderSubtle}`,
                backgroundColor: colors.bgCard,
                color: colors.textSecondary,
                fontSize: '0.875rem',
                cursor: 'pointer',
                transition: 'all 0.15s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = colors.bgElevated
                e.currentTarget.style.borderColor = colors.borderStrong
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = colors.bgCard
                e.currentTarget.style.borderColor = colors.borderSubtle
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '16px', height: '16px'}}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
              </svg>
              Back to CS Agent
            </button>
          )}
          <h1 style={{
            margin: 0,
            fontSize: '1.5rem',
            fontWeight: '600',
            color: colors.textPrimary
          }}>
            Knowledge Management
          </h1>
        </div>
        

      </div>

      {/* Tab Navigation */}
      <div style={{
        display: 'flex',
        borderBottom: `1px solid ${colors.borderSubtle}`,
        marginBottom: '1.5rem'
      }}>
        <button
          onClick={() => setActiveTab('tasks')}
          style={{
            padding: '0.75rem 1.5rem',
            borderBottom: activeTab === 'tasks' ? `2px solid ${colors.primary}` : '2px solid transparent',
            backgroundColor: 'transparent',
            border: 'none',
            color: activeTab === 'tasks' ? colors.primary : colors.textSecondary,
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.15s ease'
          }}
        >
          Knowledge Tasks
        </button>
        <button
          onClick={() => setActiveTab('base')}
          style={{
            padding: '0.75rem 1.5rem',
            borderBottom: activeTab === 'base' ? `2px solid ${colors.primary}` : '2px solid transparent',
            backgroundColor: 'transparent',
            border: 'none',
            color: activeTab === 'base' ? colors.primary : colors.textSecondary,
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer',
            transition: 'all 0.15s ease'
          }}
        >
          Knowledge Base
        </button>
      </div>

      {/* Content Based on Active Tab */}
      {activeTab === 'tasks' ? (
        <>
          {/* Task Filters */}
          <KnowledgeFilters
            filters={filters}
            colors={colors}
            onFilterChange={handleFilterChange}
            onReset={handleReset}
          />

          {/* Task List */}
          <KnowledgeTaskList
            tasks={filteredTasks}
            colors={colors}
            onTaskClick={handleTaskClick}
            selectedTask={selectedTask}
          />
        </>
      ) : (
        <>
          {/* Base Filters */}
          <KnowledgeBaseFilters
            filters={baseFilters}
            colors={colors}
            onFilterChange={handleBaseFilterChange}
            onReset={handleReset}
          />

          {/* Base List */}
          <KnowledgeBaseList
            items={filteredBaseItems}
            colors={colors}
            selectedItem={selectedBaseItem}
            onEdit={handleBaseItemEdit}
          />
        </>
      )}

      {/* Knowledge Detail Modal */}
      <KnowledgeDetailModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        data={modalData}
        colors={colors}
      />
    </div>
  )
} 