'use client'
import React from 'react'
import { Colors, KnowledgeTask } from '../../types'

interface KnowledgeTaskListProps {
  tasks: KnowledgeTask[]
  colors: Colors
  onTaskClick: (taskId: string) => void
  selectedTask: string | null
}

export default function KnowledgeTaskList({ tasks, colors, onTaskClick, selectedTask }: KnowledgeTaskListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return colors.success
      case 'Reviewing':
        return colors.primary
      case 'Revision':
        return colors.error
      case 'Draft':
        return colors.accent
      default:
        return colors.textSecondary
    }
  }

  const getStatusBackground = (status: string) => {
    switch (status) {
      case 'Completed':
        return `${colors.success}15`
      case 'Reviewing':
        return `${colors.primary}15`
      case 'Revision':
        return `${colors.error}15`
      case 'Draft':
        return `${colors.accent}15`
      default:
        return `${colors.textSecondary}15`
    }
  }



  return (
    <div style={{
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      border: `1px solid ${colors.borderSubtle}`,
      overflow: 'hidden'
    }}>
      {/* Table Header */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr 1fr',
        gap: '1rem',
        padding: '1rem',
        backgroundColor: colors.bgElevated,
        borderBottom: `1px solid ${colors.borderSubtle}`,
        fontSize: '0.875rem',
        fontWeight: '600',
        color: colors.textSecondary
      }}>
        <div>Question</div>
        <div>App Code</div>
        <div>General</div>
        <div>Task ID</div>
        <div>Status</div>
        <div>Name</div>
        <div>Action</div>
      </div>

      {/* Table Body */}
      <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
        {tasks.length === 0 ? (
          <div style={{
            padding: '3rem',
            textAlign: 'center',
            color: colors.textTertiary,
            fontSize: '0.875rem'
          }}>
            No tasks found matching the current filters.
          </div>
        ) : (
          tasks.map((task) => (
            <div
              key={task.id}
              onClick={() => onTaskClick(task.id)}
              style={{
                display: 'grid',
                gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr 1fr',
                gap: '1rem',
                padding: '1rem',
                borderBottom: `1px solid ${colors.borderSubtle}`,
                cursor: 'pointer',
                transition: 'background-color 0.15s ease',
                backgroundColor: selectedTask === task.id ? `${colors.primary}08` : 'transparent'
              }}
              onMouseEnter={(e) => {
                if (selectedTask !== task.id) {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle
                }
              }}
              onMouseLeave={(e) => {
                if (selectedTask !== task.id) {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }
              }}
            >
              {/* Question */}
              <div style={{
                fontSize: '0.875rem',
                color: colors.textPrimary,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {task.question}
              </div>

              {/* App Code */}
              <div style={{
                fontSize: '0.875rem',
                color: colors.textPrimary,
                fontWeight: '500'
              }}>
                {task.appCode || '-'}
              </div>

              {/* General */}
              <div style={{
                fontSize: '0.875rem',
                color: colors.textSecondary
              }}>
                {task.general ? 'Yes' : 'No'}
              </div>

              {/* Task ID */}
              <div style={{
                fontSize: '0.875rem',
                color: colors.textSecondary,
                fontFamily: 'monospace',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                {task.taskId}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    navigator.clipboard.writeText(task.taskId)
                  }}
                  style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    color: colors.textTertiary,
                    padding: '0.125rem',
                    borderRadius: '2px'
                  }}
                  title="Copy Task ID"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '12px', height: '12px'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75" />
                  </svg>
                </button>
              </div>

              {/* Status */}
              <div>
                <span style={{
                  padding: '0.25rem 0.5rem',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  color: getStatusColor(task.status),
                  backgroundColor: getStatusBackground(task.status)
                }}>
                  {task.status}
                </span>
              </div>

              {/* Name */}
              <div style={{
                fontSize: '0.875rem',
                color: colors.textSecondary
              }}>
                {task.assignedTo}
              </div>

              {/* Action */}
              <div>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onTaskClick(task.id)
                  }}
                  style={{
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    border: `1px solid ${colors.borderSubtle}`,
                    backgroundColor: colors.bgElevated,
                    color: colors.textSecondary,
                    fontSize: '0.75rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem',
                    transition: 'all 0.15s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle
                    e.currentTarget.style.borderColor = colors.borderStrong
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = colors.bgElevated
                    e.currentTarget.style.borderColor = colors.borderSubtle
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '12px', height: '12px'}}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Detail
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
} 