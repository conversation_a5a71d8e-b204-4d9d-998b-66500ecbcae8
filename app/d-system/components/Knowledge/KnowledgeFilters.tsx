'use client'
import React from 'react'
import { Colors, KnowledgeFilter } from '../../types'

interface KnowledgeFiltersProps {
  filters: KnowledgeFilter
  colors: Colors
  onFilterChange: (filters: Partial<KnowledgeFilter>) => void
  onReset: () => void
}

export default function KnowledgeFilters({ filters, colors, onFilterChange, onReset }: KnowledgeFiltersProps) {
  const appCodes = ['kumo', 'heroes', 'puzzle', 'adventure', 'strategy']
  const generalOptions = ['Yes', 'No']
  const statusOptions = ['Completed', 'Reviewing', 'Revision', 'Draft']

  return (
    <div style={{
      display: 'flex',
      gap: '1rem',
      marginBottom: '1.5rem',
      padding: '1rem',
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      border: `1px solid ${colors.borderSubtle}`,
      flexWrap: 'wrap',
      alignItems: 'flex-end'
    }}>
      {/* App Code Filter */}
      <div style={{ minWidth: '150px' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          App Code
        </label>
        <select
          value={filters.appCode}
          onChange={(e) => onFilterChange({ appCode: e.target.value })}
          style={{
            width: '100%',
            padding: '0.5rem',
            borderRadius: '4px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem'
          }}
        >
          <option value="">All Apps</option>
          {appCodes.map(code => (
            <option key={code} value={code}>{code}</option>
          ))}
        </select>
      </div>

      {/* General Filter */}
      <div style={{ minWidth: '120px' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          General
        </label>
        <select
          value={filters.general}
          onChange={(e) => onFilterChange({ general: e.target.value })}
          style={{
            width: '100%',
            padding: '0.5rem',
            borderRadius: '4px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem'
          }}
        >
          <option value="">All</option>
          {generalOptions.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
      </div>

      {/* Status Filter */}
      <div style={{ minWidth: '140px' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          Status
        </label>
        <select
          value={filters.status}
          onChange={(e) => onFilterChange({ status: e.target.value })}
          style={{
            width: '100%',
            padding: '0.5rem',
            borderRadius: '4px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem'
          }}
        >
          <option value="">All Status</option>
          {statusOptions.map(status => (
            <option key={status} value={status}>{status}</option>
          ))}
        </select>
      </div>

      {/* Keyword Search */}
      <div style={{ minWidth: '200px', flex: '1' }}>
        <label style={{
          display: 'block',
          marginBottom: '0.5rem',
          fontSize: '0.875rem',
          fontWeight: '500',
          color: colors.textSecondary
        }}>
          Keyword
        </label>
        <input
          type="text"
          value={filters.keyword}
          onChange={(e) => onFilterChange({ keyword: e.target.value })}
          placeholder="Search questions or assignees..."
          style={{
            width: '100%',
            padding: '0.5rem',
            borderRadius: '4px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textPrimary,
            fontSize: '0.875rem',
            outline: 'none'
          }}
        />
      </div>

      {/* Action Buttons */}
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <button
          onClick={onReset}
          style={{
            padding: '0.5rem 1rem',
            borderRadius: '4px',
            border: `1px solid ${colors.borderSubtle}`,
            backgroundColor: colors.bgElevated,
            color: colors.textSecondary,
            fontSize: '0.875rem',
            cursor: 'pointer',
            transition: 'all 0.15s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = colors.bgSubtle
            e.currentTarget.style.borderColor = colors.borderStrong
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = colors.bgElevated
            e.currentTarget.style.borderColor = colors.borderSubtle
          }}
        >
          Reset
        </button>
        
        <button
          style={{
            padding: '0.5rem 1rem',
            borderRadius: '4px',
            border: 'none',
            backgroundColor: colors.primary,
            color: 'white',
            fontSize: '0.875rem',
            cursor: 'pointer',
            transition: 'background-color 0.15s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = colors.primaryLight
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = colors.primary
          }}
        >
          Search
        </button>
      </div>
    </div>
  )
} 