'use client'
import React from 'react'
import { Colors, KnowledgeBaseItem } from '../../types'

interface KnowledgeBaseListProps {
  items: KnowledgeBaseItem[]
  colors: Colors
  selectedItem: string | null
  onEdit: (item: KnowledgeBaseItem) => void
}

export default function KnowledgeBaseList({ items, colors, selectedItem, onEdit }: KnowledgeBaseListProps) {
  return (
    <div style={{
      backgroundColor: colors.bgCard,
      borderRadius: '8px',
      overflow: 'hidden',
      border: `1px solid ${colors.borderSubtle}`
    }}>
      {/* Table Header */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '120px 80px 2fr 2fr 120px 80px 100px',
        gap: '1rem',
        padding: '1rem 1.5rem',
        backgroundColor: colors.bgElevated,
        borderBottom: `1px solid ${colors.borderSubtle}`,
        fontSize: '0.875rem',
        fontWeight: '600',
        color: colors.textSecondary
      }}>
        <div>App Code</div>
        <div>General</div>
        <div>Question</div>
        <div>Answer</div>
        <div>Name</div>
        <div>Version</div>
        <div>Operation</div>
      </div>

      {/* Table Body */}
      <div>
        {items.map((item, index) => (
          <div
            key={item.id}
            style={{
              display: 'grid',
              gridTemplateColumns: '120px 80px 2fr 2fr 120px 80px 100px',
              gap: '1rem',
              padding: '1rem 1.5rem',
              borderBottom: index < items.length - 1 ? `1px solid ${colors.borderSubtle}` : 'none',
              backgroundColor: selectedItem === item.id ? colors.bgSubtle : 'transparent',
              transition: 'background-color 0.15s ease',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              if (selectedItem !== item.id) {
                e.currentTarget.style.backgroundColor = colors.bgSubtle
              }
            }}
            onMouseLeave={(e) => {
              if (selectedItem !== item.id) {
                e.currentTarget.style.backgroundColor = 'transparent'
              }
            }}
          >
            {/* App Code */}
            <div style={{
              fontSize: '0.875rem',
              color: colors.textPrimary,
              fontWeight: '500'
            }}>
              {item.appCode || '-'}
            </div>

            {/* General */}
            <div style={{
              fontSize: '0.875rem',
              color: colors.textPrimary
            }}>
              {item.general ? 'Yes' : 'No'}
            </div>

            {/* Question */}
            <div style={{
              fontSize: '0.875rem',
              color: colors.textPrimary,
              lineHeight: '1.4',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}>
              {item.question}
            </div>

            {/* Answer */}
            <div style={{
              fontSize: '0.875rem',
              color: colors.textSecondary,
              lineHeight: '1.4',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}>
              {item.answer}
            </div>

            {/* Name */}
            <div style={{
              fontSize: '0.875rem',
              color: colors.textPrimary
            }}>
              {item.name || '-'}
            </div>

            {/* Version */}
            <div style={{
              fontSize: '0.875rem',
              color: colors.textPrimary,
              fontFamily: 'monospace',
              fontWeight: '500'
            }}>
              {item.version}
            </div>

            {/* Operation */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <button
                onClick={() => onEdit(item)}
                style={{
                  padding: '0.25rem 0.5rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: colors.primary,
                  color: 'white',
                  fontSize: '0.75rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '12px', height: '12px'}}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                </svg>
              </button>
              <button
                style={{
                  padding: '0.25rem 0.5rem',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: colors.error,
                  color: 'white',
                  fontSize: '0.75rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" style={{width: '12px', height: '12px'}}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>

      {items.length === 0 && (
        <div style={{
          padding: '3rem',
          textAlign: 'center',
          color: colors.textTertiary,
          fontSize: '0.875rem'
        }}>
          No knowledge base items found
        </div>
      )}
    </div>
  )
} 