'use client';

import React from 'react';
import './d-system.css';
import './index.css';
import { ThemeProvider } from './theme-context';

export default function DSystemLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider>
      <style jsx global>{`
        /* 确保CSS变量被正确应用 */
        :root[data-theme="light"] {
          --bg-main: #F9FAFB !important;
          --bg-card: #FFFFFF !important;
          --text-primary: #111827 !important;
        }
        
        :root[data-theme="dark"] {
          --bg-main: #0F172A !important;
          --bg-card: #1E293B !important;
          --text-primary: #F9FAFB !important;
        }
        
        /* 强制应用基本样式 */
        .d-system-container {
          display: flex !important;
          min-height: 100vh !important;
          background-color: var(--bg-main) !important;
        }
      `}</style>
      <div className="d-system-container">
        {children}
      </div>
    </ThemeProvider>
  );
} 