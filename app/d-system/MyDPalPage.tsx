'use client';

import React, { useState } from 'react';

interface MyDPalPageProps {
  theme: 'light' | 'dark';
  colors: {
    bgMain: string;
    bgCard: string;
    bgElevated: string;
    bgSubtle: string;
    textPrimary: string;
    textSecondary: string;
    textTertiary: string;
    borderSubtle: string;
    borderStrong: string;
    primary: string;
    primaryLight: string;
    accent: string;
    success: string;
    error: string;
  };
  onClose: (newAvatarOptions?: any) => void;
  initialAvatar?: {
    headgear: string;
    eyes: string;
    mouth: string;
    necklace: string;
    pattern: string;
    color: string;
    experience: number;
  };
}

// 游戏化的角色定制选项配置
const customizationOptions = {
  headgear: [
    { id: 'default', name: 'Classic', emoji: '🎯', rarity: 'common', description: 'The classic look' },
    { id: 'hat', name: 'Magician Hat', emoji: '🎩', rarity: 'rare', description: 'Mysterious and magical' },
    { id: 'bow', name: 'Pink Bow', emoji: '🎀', rarity: 'common', description: 'Cute and adorable' },
    { id: 'crown', name: 'Royal Crown', emoji: '👑', rarity: 'legendary', description: 'Fit for a king!' },
    { id: 'headphones', name: 'DJ Headphones', emoji: '🎧', rarity: 'rare', description: 'Ready to drop beats' },
  ],
  eyes: [
    { id: 'normal', name: 'Normal', emoji: '👀', rarity: 'common', description: 'Standard happy look' },
    { id: 'sleepy', name: 'Sleepy', emoji: '😴', rarity: 'common', description: 'Need more sleep' },
    { id: 'angry', name: 'Fierce', emoji: '😠', rarity: 'rare', description: 'Ready for battle' },
    { id: 'excited', name: 'Sparkly', emoji: '✨', rarity: 'rare', description: 'Full of energy' },
    { id: 'closed', name: 'Zen', emoji: '🧘', rarity: 'rare', description: 'Inner peace' },
  ],
  mouth: [
    { id: 'smile', name: 'Happy Smile', emoji: '😊', rarity: 'common', description: 'Default cheerful' },
    { id: 'laugh', name: 'Big Laugh', emoji: '😄', rarity: 'common', description: 'Infectious joy' },
    { id: 'surprised', name: 'Surprised', emoji: '😮', rarity: 'rare', description: 'What a discovery!' },
    { id: 'neutral', name: 'Calm', emoji: '😐', rarity: 'common', description: 'Cool and collected' },
    { id: 'sad', name: 'Pouty', emoji: '😢', rarity: 'rare', description: 'Needs a hug' },
  ],
  necklace: [
    { id: 'bell', name: 'Magic Bell', emoji: '🔔', rarity: 'legendary', description: 'The iconic bell' },
    { id: 'bowtie', name: 'Formal Bowtie', emoji: '🎀', rarity: 'rare', description: 'Ready for business' },
    { id: 'scarf', name: 'Cozy Scarf', emoji: '🧣', rarity: 'common', description: 'Warm and comfy' },
    { id: 'pendant', name: 'Lucky Pendant', emoji: '🔮', rarity: 'rare', description: 'Brings good fortune' },
    { id: 'none', name: 'Natural', emoji: '🌟', rarity: 'common', description: 'Au naturel' },
  ],
  pattern: [
    { id: 'classic', name: 'Classic Blue', emoji: '💙', rarity: 'legendary', description: 'The original design' },
    { id: 'striped', name: 'Zebra Stripes', emoji: '🦓', rarity: 'rare', description: 'Bold and striking' },
    { id: 'stars', name: 'Starry Night', emoji: '⭐', rarity: 'rare', description: 'Cosmic patterns' },
    { id: 'dots', name: 'Polka Dots', emoji: '🔵', rarity: 'common', description: 'Playful spots' },
    { id: 'hearts', name: 'Love Hearts', emoji: '💖', rarity: 'rare', description: 'Full of love' },
  ],
  color: [
    { id: 'blue', name: 'Ocean Blue', emoji: '🌊', rarity: 'legendary', description: 'The classic blue' },
    { id: 'pink', name: 'Sakura Pink', emoji: '🌸', rarity: 'rare', description: 'Cherry blossom beauty' },
    { id: 'green', name: 'Forest Green', emoji: '🌲', rarity: 'rare', description: 'Nature lover' },
    { id: 'yellow', name: 'Sunshine Yellow', emoji: '☀️', rarity: 'common', description: 'Bright and cheerful' },
    { id: 'purple', name: 'Royal Purple', emoji: '🔮', rarity: 'rare', description: 'Mystical aura' },
    { id: 'red', name: 'Ruby Red', emoji: '💎', rarity: 'rare', description: 'Bold and confident' },
  ]
};

// 获取稀有度颜色
const getRarityColor = (rarity: string, theme: string) => {
  switch (rarity) {
    case 'common': return theme === 'light' ? '#6B7280' : '#9CA3AF';
    case 'rare': return theme === 'light' ? '#3B82F6' : '#60A5FA';
    case 'legendary': return theme === 'light' ? '#F59E0B' : '#FCD34D';
    default: return theme === 'light' ? '#6B7280' : '#9CA3AF';
  }
};

// 增强版的Doraemon渲染组件
const DoraemonAvatar = ({ options, size = 240 }: { options: any, size?: number }) => {
  const scale = size / 240; // 基准尺寸240px
  
  return (
    <div style={{
      width: `${size}px`,
      height: `${size}px`,
      position: 'relative',
      margin: '0 auto',
      filter: 'drop-shadow(0 12px 24px rgba(0, 0, 0, 0.15))',
      transform: 'perspective(1000px) rotateX(5deg)',
      transition: 'all 0.3s ease'
    }}>
      {/* 主体背景光环 */}
      <div style={{
        position: 'absolute',
        top: '-10px',
        left: '-10px',
        right: '-10px',
        bottom: '-10px',
        background: `radial-gradient(circle, ${
          options.color === 'blue' ? 'rgba(26, 148, 230, 0.2)' : 
          options.color === 'pink' ? 'rgba(255, 126, 182, 0.2)' : 
          options.color === 'green' ? 'rgba(76, 175, 80, 0.2)' : 
          options.color === 'yellow' ? 'rgba(255, 193, 7, 0.2)' : 
          options.color === 'purple' ? 'rgba(156, 39, 176, 0.2)' : 'rgba(229, 57, 53, 0.2)'
        } 0%, transparent 70%)`,
        borderRadius: '50%',
        animation: 'pulse 3s ease-in-out infinite'
      }}></div>

      {/* 主体圆形 */}
      <div style={{
        width: '100%',
        height: '100%',
        background: `linear-gradient(135deg, ${
          options.color === 'blue' ? '#1A94E6, #1976D2' : 
          options.color === 'pink' ? '#FF7EB6, #E91E63' : 
          options.color === 'green' ? '#4CAF50, #388E3C' : 
          options.color === 'yellow' ? '#FFC107, #F57C00' : 
          options.color === 'purple' ? '#9C27B0, #7B1FA2' : '#E53935, #C62828'
        })`,
        borderRadius: '50%',
        position: 'relative',
        overflow: 'hidden',
        border: '3px solid rgba(255, 255, 255, 0.3)',
        boxShadow: `
          inset 0 ${8 * scale}px ${16 * scale}px rgba(255, 255, 255, 0.2),
          inset 0 -${8 * scale}px ${16 * scale}px rgba(0, 0, 0, 0.1),
          0 ${8 * scale}px ${32 * scale}px rgba(0, 0, 0, 0.2)
        `
      }}>
        {/* 白色腹部 - 更立体 */}
        <div style={{
          position: 'absolute',
          bottom: '0',
          left: `${30 * scale}px`,
          width: `${140 * scale}px`,
          height: `${100 * scale}px`,
          background: 'linear-gradient(135deg, #ffffff, #f5f5f5)',
          borderRadius: '50% 50% 0 0',
          border: '2px solid rgba(0, 0, 0, 0.1)',
          boxShadow: `inset 0 ${4 * scale}px ${8 * scale}px rgba(255, 255, 255, 0.8)`
        }}></div>
        
        {/* 花纹图案 - 增强效果 */}
        {options.pattern !== 'classic' && (
          <div style={{
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            opacity: 0.15,
            backgroundImage: 
              options.pattern === 'striped' ? 'linear-gradient(45deg, #000 25%, transparent 25%, transparent 75%, #000 75%, #000)' :
              options.pattern === 'stars' ? "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.45,13.97L5.82,21L12,17.27Z'/%3E%3C/svg%3E\")" :
              options.pattern === 'dots' ? 'radial-gradient(circle, #000 3px, transparent 3px)' : 
              options.pattern === 'hearts' ? "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z'/%3E%3C/svg%3E\")" : '',
            backgroundSize: options.pattern === 'striped' ? `${20 * scale}px ${20 * scale}px` : `${40 * scale}px ${40 * scale}px`,
            borderRadius: '50%'
          }}></div>
        )}
        
        {/* 眼睛 - 更生动 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          width: `${110 * scale}px`,
          position: 'absolute',
          top: `${60 * scale}px`,
          left: '50%',
          transform: 'translateX(-50%)'
        }}>
          {/* 左眼 */}
          <div style={{
            width: `${32 * scale}px`,
            height: options.eyes === 'closed' ? `${3 * scale}px` : `${32 * scale}px`,
            borderRadius: options.eyes === 'closed' ? `${2 * scale}px` : '50%',
            background: options.eyes === 'closed' ? '#000' : 'linear-gradient(135deg, #ffffff, #f0f0f0)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden',
            border: options.eyes !== 'closed' ? `${2 * scale}px solid rgba(0, 0, 0, 0.1)` : 'none',
            boxShadow: options.eyes !== 'closed' ? `inset 0 ${2 * scale}px ${4 * scale}px rgba(255, 255, 255, 0.8)` : 'none'
          }}>
            {options.eyes !== 'closed' && (
              <div style={{
                width: `${14 * scale}px`,
                height: `${14 * scale}px`,
                borderRadius: '50%',
                background: 'radial-gradient(circle at 30% 30%, #333, #000)',
                position: 'relative',
                top: options.eyes === 'sleepy' ? `${6 * scale}px` : 
                     options.eyes === 'angry' ? `${-3 * scale}px` : '0px',
                left: options.eyes === 'excited' ? `${2 * scale}px` : '0px',
                transform: options.eyes === 'excited' ? 'scale(1.2)' : 'scale(1)',
                boxShadow: options.eyes === 'excited' ? '0 0 8px rgba(255, 255, 0, 0.6)' : 'none'
              }}>
                {/* 眼睛高光 */}
                <div style={{
                  width: `${3 * scale}px`,
                  height: `${3 * scale}px`,
                  borderRadius: '50%',
                  background: 'white',
                  position: 'absolute',
                  top: `${2 * scale}px`,
                  left: `${2 * scale}px`
                }}></div>
              </div>
            )}
          </div>
          
          {/* 右眼 */}
          <div style={{
            width: `${32 * scale}px`,
            height: options.eyes === 'closed' ? `${3 * scale}px` : `${32 * scale}px`,
            borderRadius: options.eyes === 'closed' ? `${2 * scale}px` : '50%',
            background: options.eyes === 'closed' ? '#000' : 'linear-gradient(135deg, #ffffff, #f0f0f0)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            overflow: 'hidden',
            border: options.eyes !== 'closed' ? `${2 * scale}px solid rgba(0, 0, 0, 0.1)` : 'none',
            boxShadow: options.eyes !== 'closed' ? `inset 0 ${2 * scale}px ${4 * scale}px rgba(255, 255, 255, 0.8)` : 'none'
          }}>
            {options.eyes !== 'closed' && (
              <div style={{
                width: `${14 * scale}px`,
                height: `${14 * scale}px`,
                borderRadius: '50%',
                background: 'radial-gradient(circle at 30% 30%, #333, #000)',
                position: 'relative',
                top: options.eyes === 'sleepy' ? `${6 * scale}px` : 
                     options.eyes === 'angry' ? `${-3 * scale}px` : '0px',
                right: options.eyes === 'excited' ? `${2 * scale}px` : '0px',
                transform: options.eyes === 'excited' ? 'scale(1.2)' : 'scale(1)',
                boxShadow: options.eyes === 'excited' ? '0 0 8px rgba(255, 255, 0, 0.6)' : 'none'
              }}>
                {/* 眼睛高光 */}
                <div style={{
                  width: `${3 * scale}px`,
                  height: `${3 * scale}px`,
                  borderRadius: '50%',
                  background: 'white',
                  position: 'absolute',
                  top: `${2 * scale}px`,
                  left: `${2 * scale}px`
                }}></div>
              </div>
            )}
          </div>
        </div>
        
        {/* 鼻子 - 更立体 */}
        <div style={{
          width: `${18 * scale}px`,
          height: `${18 * scale}px`,
          borderRadius: '50%',
          background: 'radial-gradient(circle at 30% 30%, #ff6b6b, #ff4757)',
          position: 'absolute',
          top: `${100 * scale}px`,
          left: '50%',
          transform: 'translateX(-50%)',
          border: `${2 * scale}px solid rgba(255, 255, 255, 0.3)`,
          boxShadow: `
            inset 0 ${2 * scale}px ${4 * scale}px rgba(255, 255, 255, 0.4),
            0 ${4 * scale}px ${8 * scale}px rgba(0, 0, 0, 0.2)
          `
        }}></div>
        
        {/* 嘴巴 - 更精细 */}
        <div style={{
          width: `${70 * scale}px`,
          height: options.mouth === 'smile' ? `${35 * scale}px` : 
                  options.mouth === 'laugh' ? `${45 * scale}px` : 
                  options.mouth === 'surprised' ? `${22 * scale}px` : 
                  options.mouth === 'sad' ? `${35 * scale}px` : `${3 * scale}px`,
          border: `${2 * scale}px solid #000`,
          borderRadius: options.mouth === 'smile' ? `0 0 ${35 * scale}px ${35 * scale}px` : 
                       options.mouth === 'laugh' ? `0 0 ${45 * scale}px ${45 * scale}px` : 
                       options.mouth === 'surprised' ? '50%' : 
                       options.mouth === 'sad' ? `${35 * scale}px ${35 * scale}px 0 0` : `${2 * scale}px`,
          borderTop: options.mouth === 'smile' || options.mouth === 'laugh' || options.mouth === 'neutral' ? 'none' : 
                    options.mouth === 'surprised' ? `${2 * scale}px solid #000` : 
                    options.mouth === 'sad' ? `${2 * scale}px solid #000` : 'none',
          borderBottom: options.mouth === 'sad' || options.mouth === 'neutral' ? 'none' : `${2 * scale}px solid #000`,
          position: 'absolute',
          top: `${125 * scale}px`,
          left: '50%',
          transform: 'translateX(-50%)',
          background: options.mouth === 'laugh' ? 'rgba(255, 182, 193, 0.3)' : 'transparent',
          boxShadow: options.mouth === 'surprised' ? `inset 0 ${2 * scale}px ${4 * scale}px rgba(0, 0, 0, 0.1)` : 'none'
        }}></div>
        
        {/* 胡须 - 动态效果 */}
        <div style={{
          position: 'absolute',
          top: `${110 * scale}px`,
          width: `${200 * scale}px`,
          left: '50%',
          transform: 'translateX(-50%)'
        }}>
          {[
            { x: 10, y: 0, rotation: 20, side: 'left' },
            { x: 130, y: 0, rotation: -20, side: 'right' },
            { x: 15, y: 10, rotation: 0, side: 'left' },
            { x: 125, y: 10, rotation: 0, side: 'right' },
            { x: 10, y: 20, rotation: -20, side: 'left' },
            { x: 130, y: 20, rotation: 20, side: 'right' }
          ].map((whisker, index) => (
            <div key={index} style={{
              width: `${50 * scale}px`,
              height: `${2 * scale}px`,
              background: 'linear-gradient(90deg, #000, #333)',
              position: 'absolute',
              left: `${whisker.x * scale}px`,
              top: `${whisker.y * scale}px`,
              transform: `rotate(${whisker.rotation}deg)`,
              transformOrigin: whisker.side === 'left' ? 'right center' : 'left center',
              borderRadius: `${1 * scale}px`,
              boxShadow: `0 ${1 * scale}px ${2 * scale}px rgba(0, 0, 0, 0.2)`
            }}></div>
          ))}
        </div>
        
        {/* 项链/铃铛 - 增强版 */}
        {options.necklace !== 'none' && (
          <div style={{
            position: 'absolute',
            bottom: `${50 * scale}px`,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 2
          }}>
            {options.necklace === 'bell' && (
              <>
                {/* 项圈 */}
                <div style={{
                  width: `${80 * scale}px`,
                  height: `${8 * scale}px`,
                  background: 'linear-gradient(135deg, #ff4757, #c44569)',
                  borderRadius: `${4 * scale}px`,
                  border: `${2 * scale}px solid rgba(255, 255, 255, 0.3)`,
                  boxShadow: `0 ${2 * scale}px ${4 * scale}px rgba(0, 0, 0, 0.2)`,
                  margin: '0 auto'
                }}></div>
                {/* 铃铛 */}
                <div style={{
                  width: `${24 * scale}px`,
                  height: `${24 * scale}px`,
                  background: 'radial-gradient(circle at 30% 30%, #ffd700, #ffb700)',
                  borderRadius: '50%',
                  position: 'relative',
                  top: `${4 * scale}px`,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  border: `${2 * scale}px solid rgba(255, 255, 255, 0.4)`,
                  boxShadow: `
                    inset 0 ${2 * scale}px ${4 * scale}px rgba(255, 255, 255, 0.5),
                    0 ${4 * scale}px ${8 * scale}px rgba(0, 0, 0, 0.2)
                  `
                }}>
                  {/* 铃铛中心 */}
                  <div style={{
                    width: `${5 * scale}px`,
                    height: `${5 * scale}px`,
                    background: '#333',
                    borderRadius: '50%',
                    position: 'absolute',
                    top: `${11 * scale}px`,
                    left: `${9.5 * scale}px`
                  }}></div>
                  {/* 铃铛底部线条 */}
                  <div style={{
                    width: `${16 * scale}px`,
                    height: `${2 * scale}px`,
                    background: '#333',
                    position: 'absolute',
                    top: `${18 * scale}px`,
                    left: `${4 * scale}px`,
                    borderRadius: `${1 * scale}px`
                  }}></div>
                </div>
              </>
            )}
            {/* 其他项链选项的增强版本... */}
          </div>
        )}
        
        {/* 头饰 - 增强版 */}
        {options.headgear !== 'default' && (
          <div style={{
            position: 'absolute',
            top: options.headgear === 'hat' ? `${-40 * scale}px` : 
                 options.headgear === 'crown' ? `${-30 * scale}px` : `${10 * scale}px`,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 3
          }}>
            {options.headgear === 'hat' && (
              <div style={{
                width: `${120 * scale}px`,
                height: `${65 * scale}px`,
                background: 'linear-gradient(135deg, #2c3e50, #34495e)',
                borderRadius: '50% 50% 0 0',
                position: 'relative',
                border: `${3 * scale}px solid rgba(255, 255, 255, 0.2)`,
                boxShadow: `
                  inset 0 ${4 * scale}px ${8 * scale}px rgba(255, 255, 255, 0.1),
                  0 ${6 * scale}px ${12 * scale}px rgba(0, 0, 0, 0.3)
                `
              }}>
                {/* 帽檐 */}
                <div style={{
                  width: `${140 * scale}px`,
                  height: `${12 * scale}px`,
                  background: 'linear-gradient(135deg, #2c3e50, #34495e)',
                  position: 'absolute',
                  bottom: `${-6 * scale}px`,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  borderRadius: `${6 * scale}px`,
                  border: `${2 * scale}px solid rgba(255, 255, 255, 0.2)`
                }}></div>
              </div>
            )}
            {options.headgear === 'crown' && (
              <div style={{
                width: `${110 * scale}px`,
                height: `${35 * scale}px`,
                background: 'linear-gradient(135deg, #ffd700, #ffb700)',
                position: 'relative',
                borderRadius: `0 0 ${8 * scale}px ${8 * scale}px`,
                border: `${2 * scale}px solid rgba(255, 255, 255, 0.4)`,
                boxShadow: `
                  inset 0 ${2 * scale}px ${4 * scale}px rgba(255, 255, 255, 0.5),
                  0 ${4 * scale}px ${8 * scale}px rgba(0, 0, 0, 0.3)
                `
              }}>
                {/* 王冠尖齿 */}
                {[20, 45, 70].map((left, index) => (
                  <div key={index} style={{
                    width: `${10 * scale}px`,
                    height: `${20 * scale}px`,
                    background: 'linear-gradient(135deg, #ffd700, #ffb700)',
                    position: 'absolute',
                    top: `${-18 * scale}px`,
                    left: `${left * scale}px`,
                    clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
                    border: `${1 * scale}px solid rgba(255, 255, 255, 0.3)`
                  }}></div>
                ))}
                {/* 宝石 */}
                <div style={{
                  width: `${14 * scale}px`,
                  height: `${14 * scale}px`,
                  background: 'radial-gradient(circle, #e74c3c, #c0392b)',
                  borderRadius: '50%',
                  position: 'absolute',
                  top: `${10 * scale}px`,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  border: `${2 * scale}px solid rgba(255, 255, 255, 0.5)`,
                  boxShadow: `inset 0 ${2 * scale}px ${4 * scale}px rgba(255, 255, 255, 0.3)`
                }}></div>
              </div>
            )}
            {/* 其他头饰的增强版本... */}
          </div>
        )}
      </div>

      {/* CSS动画样式 */}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 0.8; }
          50% { transform: scale(1.05); opacity: 0.6; }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
      `}</style>
    </div>
  );
};

// 游戏化的配置选项卡片
const CustomizationCard = ({ 
  option, 
  isSelected, 
  onClick, 
  colors, 
  theme,
  category 
}: { 
  option: any, 
  isSelected: boolean, 
  onClick: () => void, 
  colors: any, 
  theme: string,
  category: string 
}) => {
  const rarityColor = getRarityColor(option.rarity, theme);
  
  return (
        <div
      onClick={onClick}
      style={{
        position: 'relative',
        padding: '0.2rem',
        backgroundColor: isSelected ? colors.primary : colors.bgElevated,
        borderRadius: '6px',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        border: `2px solid ${isSelected ? colors.primary : 'transparent'}`,
        boxShadow: isSelected 
          ? `0 4px 12px ${colors.primary}40`
          : '0 2px 6px rgba(0, 0, 0, 0.1)',
        transform: isSelected ? 'translateY(-1px)' : 'translateY(0)',
        overflow: 'hidden',
        aspectRatio: '1',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      onMouseEnter={(e) => {
        if (!isSelected) {
          e.currentTarget.style.transform = 'translateY(-2px) scale(1.01)';
          e.currentTarget.style.boxShadow = '0 6px 18px rgba(0, 0, 0, 0.15)';
        }
      }}
      onMouseLeave={(e) => {
        if (!isSelected) {
          e.currentTarget.style.transform = 'translateY(0) scale(1)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
        }
      }}
    >
      {/* 稀有度光环 */}
      <div style={{
        position: 'absolute',
        top: '-2px',
        right: '-2px',
        bottom: '-2px',
        left: '-2px',
        background: `linear-gradient(45deg, ${rarityColor}40, transparent)`,
        borderRadius: '14px',
        zIndex: -1,
        opacity: option.rarity === 'legendary' ? 0.8 : 0.4
      }}></div>
      
      
      
            {/* 只显示Emoji图标 */}
      <div style={{
        fontSize: '1.1rem',
        filter: isSelected ? 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))' : 'none',
        transition: 'all 0.3s ease'
      }}>
        {option.emoji}
      </div>
      
            {/* 选中指示器 */}
      {isSelected && (
        <div style={{
          position: 'absolute',
          top: '2px',
          right: '2px',
          width: '10px',
          height: '10px',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '7px',
          animation: 'pulse 2s infinite'
        }}>
          ✓
        </div>
      )}
    </div>
  );
};

export default function MyDPalPage({ theme, colors, onClose, initialAvatar }: MyDPalPageProps) {
  const [activeTab, setActiveTab] = useState<'avatar' | 'profile' | 'preferences' | 'memory'>('avatar');
  const [name, setName] = useState('David Chen');
  const [avatar, setAvatar] = useState('/avatar.png');
  const [role, setRole] = useState('Data Analyst');
  const [email, setEmail] = useState('<EMAIL>');
  const [department, setDepartment] = useState('Analytics');

  // Doraemon角色自定义选项
  const [avatarOptions, setAvatarOptions] = useState(initialAvatar || {
    headgear: 'default',
    eyes: 'normal',
    mouth: 'smile',
    necklace: 'bell',
    pattern: 'classic',
    color: 'blue',
    experience: 75
  });

  // 用户等级和经验系统
  const userLevel = 10;
  const currentExp = 750;
  const expToNextLevel = 1000;
  const expProgress = (currentExp / expToNextLevel) * 100;

  // 模拟经验获取记录
  const expGains = [
    { id: 1, task: 'Completed code review', exp: 25, time: '2 hours ago' },
    { id: 2, task: 'Fixed critical bug', exp: 50, time: '4 hours ago' },
    { id: 3, task: 'Optimized performance', exp: 30, time: '1 day ago' },
    { id: 4, task: 'Helped team member', exp: 15, time: '2 days ago' }
  ];

  // D-Pal服务风格选项
  const [serviceStyle, setServiceStyle] = useState<'mentor' | 'secretary'>('mentor');

  // 模拟偏好设置
  const [preferences, setPreferences] = useState({
    theme: theme,
    notifications: true,
    desktopNotifications: true,
    // 客户端设置
    textSelectionTranslation: true,
    translateTool: true,
    explainTool: true,
    summaryTool: true
  });

  // 快捷键设置
  const [shortcuts, setShortcuts] = useState({
    screenshot: 'Control+Shift+A'
  });

  // 快捷键编辑状态
  const [editingShortcut, setEditingShortcut] = useState<string | null>(null);
  const [tempShortcut, setTempShortcut] = useState('');

  // 统一工具列表（默认工具 + 自定义工具）
  const [allTools, setAllTools] = useState([
    { id: 'translate', name: 'Translate', description: 'Translate selected text to target language', icon: '🌐', type: 'default', enabled: true, instruction: 'Translate this text: "{selectedText}".' },
    { id: 'explain', name: 'Explain', description: 'Explain and clarify selected content', icon: '💡', type: 'default', enabled: true, instruction: 'Explain this text: "{selectedText}".' },
    { id: 'summary', name: 'Summary', description: 'Summarize selected text content', icon: '📝', type: 'default', enabled: true, instruction: 'Summarize this text: "{selectedText}".' },
    { id: 'grammar', name: 'Grammar Check', description: 'Check and correct grammar issues', icon: '✏️', type: 'custom', enabled: true, instruction: 'Check grammar for this text: "{selectedText}".' }
  ]);

  // 弹窗状态
  const [showAddToolModal, setShowAddToolModal] = useState(false);
  const [newToolName, setNewToolName] = useState('');
  const [newToolInstruction, setNewToolInstruction] = useState('');
  
  // 编辑工具状态
  const [editingTool, setEditingTool] = useState<string | null>(null);
  const [editToolName, setEditToolName] = useState('');
  const [editToolInstruction, setEditToolInstruction] = useState('');

  // AI记录的用户习惯和工作倾向
  const [memoryEntries, setMemoryEntries] = useState([
    {
      id: 1,
      content: "User prefers concise, bullet-point style responses for technical questions",
      category: "Communication Style",
      timestamp: new Date('2024-01-15')
    },
    {
      id: 2,
      content: "Frequently asks for code optimization and performance improvements",
      category: "Work Focus",
      timestamp: new Date('2024-01-16')
    },
    {
      id: 3,
      content: "Prefers dark theme and high contrast UI elements",
      category: "UI Preferences",
      timestamp: new Date('2024-01-17')
    },
    {
      id: 4,
      content: "Works primarily with React/TypeScript, values type safety",
      category: "Technical Skills",
      timestamp: new Date('2024-01-18')
    },
    {
      id: 5,
      content: "Often requests enterprise-grade solutions with proper error handling",
      category: "Code Quality",
      timestamp: new Date('2024-01-19')
    }
  ]);

  const handleTogglePreference = (key: string) => {
    setPreferences({
      ...preferences,
      [key]: !preferences[key as keyof typeof preferences]
    });
  };

  // 处理工具切换
  const handleToggleTool = (toolId: string) => {
    setAllTools(tools => 
      tools.map(tool => 
        tool.id === toolId ? { ...tool, enabled: !tool.enabled } : tool
      )
    );
  };

  // 添加自定义工具
  const handleAddCustomTool = () => {
    if (!newToolName.trim() || !newToolInstruction.trim()) {
      alert('请填写工具名称和指令');
      return;
    }

    const newTool = {
      id: Date.now().toString(),
      name: newToolName.trim(),
      description: 'Custom user tool',
      icon: '⚙️',
      type: 'custom' as const,
      enabled: true,
      instruction: newToolInstruction.trim()
    };

    setAllTools(tools => [...tools, newTool]);
    setNewToolName('');
    setNewToolInstruction('');
    setShowAddToolModal(false);
  };

  // 删除自定义工具
  const handleDeleteTool = (toolId: string) => {
    setAllTools(tools => tools.filter(tool => tool.id !== toolId));
  };

  // 编辑工具
  const handleEditTool = (toolId: string) => {
    const tool = allTools.find(t => t.id === toolId);
    if (tool && tool.type === 'custom') {
      setEditingTool(toolId);
      setEditToolName(tool.name);
      setEditToolInstruction(tool.instruction);
    }
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (!editToolName.trim() || !editToolInstruction.trim()) {
      alert('请填写工具名称和指令');
      return;
    }

    setAllTools(tools => 
      tools.map(tool => 
        tool.id === editingTool 
          ? { ...tool, name: editToolName.trim(), instruction: editToolInstruction.trim() }
          : tool
      )
    );

    setEditingTool(null);
    setEditToolName('');
    setEditToolInstruction('');
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingTool(null);
    setEditToolName('');
    setEditToolInstruction('');
  };

  // 拖拽重新排序
  const handleDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('text/plain', index.toString());
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    const dragIndex = parseInt(e.dataTransfer.getData('text/plain'));
    
    if (dragIndex === dropIndex) return;
    
    const newTools = [...allTools];
    const draggedTool = newTools[dragIndex];
    newTools.splice(dragIndex, 1);
    newTools.splice(dropIndex, 0, draggedTool);
    
    setAllTools(newTools);
  };



  // 快捷键处理函数
  const handleEditShortcut = (shortcutKey: string) => {
    setEditingShortcut(shortcutKey);
    setTempShortcut(shortcuts[shortcutKey as keyof typeof shortcuts]);
  };

  const handleSaveShortcut = (shortcutKey: string) => {
    if (tempShortcut.trim()) {
      setShortcuts({
        ...shortcuts,
        [shortcutKey]: tempShortcut.trim()
      });
    }
    setEditingShortcut(null);
    setTempShortcut('');
  };

  const handleCancelEditShortcut = () => {
    setEditingShortcut(null);
    setTempShortcut('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (editingShortcut) {
      e.preventDefault();
      const keys = [];
      if (e.ctrlKey) keys.push('Control');
      if (e.shiftKey) keys.push('Shift');
      if (e.altKey) keys.push('Alt');
      if (e.metaKey) keys.push('Meta');
      
      const key = e.key;
      if (key !== 'Control' && key !== 'Shift' && key !== 'Alt' && key !== 'Meta') {
        keys.push(key.toUpperCase());
      }
      
      if (keys.length > 0) {
        setTempShortcut(keys.join('+'));
      }
    }
  };

  // Memory 管理函数
  const deleteMemoryEntry = (id: number) => {
    setMemoryEntries(memoryEntries.filter(entry => entry.id !== id));
  };

  const clearAllMemories = () => {
    if (window.confirm('Are you sure you want to clear all memory entries? This action cannot be undone.')) {
      setMemoryEntries([]);
    }
  };

  // 更新Doraemon角色配置
  const updateAvatarOption = (option: string, value: string | number) => {
    setAvatarOptions({
      ...avatarOptions,
      [option]: value
    });
  };

  // 随机生成角色
  const randomizeAvatar = () => {
    const getRandomOption = (options: any[]) => 
      options[Math.floor(Math.random() * options.length)].id;
    
    setAvatarOptions(prev => ({
      headgear: getRandomOption(customizationOptions.headgear),
      eyes: getRandomOption(customizationOptions.eyes),
      mouth: getRandomOption(customizationOptions.mouth),
      necklace: getRandomOption(customizationOptions.necklace),
      pattern: getRandomOption(customizationOptions.pattern),
      color: getRandomOption(customizationOptions.color),
      experience: prev.experience // 保持原有经验值
    }));
  };

  // 还原默认角色
  const resetAvatar = () => {
    setAvatarOptions(prev => ({
      headgear: 'default',
      eyes: 'normal',
      mouth: 'smile',
      necklace: 'bell',
      pattern: 'classic',
      color: 'blue',
      experience: prev.experience // 保持原有经验值
    }));
  };

  // 处理头像上传
  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      alert('Please select a valid image file (JPG, PNG, GIF)');
      return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setAvatar(result);
    };
    reader.onerror = () => {
      alert('Error reading file. Please try again.');
    };
    reader.readAsDataURL(file);

    // 清空input值，允许重新选择相同文件
    event.target.value = '';
  };

  return (
    <div style={{
      backgroundColor: colors.bgMain,
      minHeight: '100vh',
      width: '100%',
      padding: '2rem',
      color: colors.textPrimary
    }}>
      {/* 顶部导航栏 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
          <h1 style={{ 
            fontSize: '1.8rem', 
            fontWeight: 'bold', 
            margin: 0,
            background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            🤖 My D-Pal Studio
          </h1>
          <button
            onClick={() => window.open('/d-system/onboarding', '_blank')}
            style={{
              backgroundColor: colors.primary,
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '0.5rem 1rem',
              fontSize: '0.85rem',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transition: 'all 0.2s ease',
              fontWeight: '500'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-1px)';
              e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0px)';
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            🎯 Onboarding演示
          </button>
        </div>
        <button
          onClick={() => onClose(avatarOptions)}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            color: colors.textPrimary,
            fontSize: '1.5rem',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            transition: 'all 0.2s'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = colors.bgSubtle;
            e.currentTarget.style.transform = 'rotate(90deg)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.transform = 'rotate(0deg)';
          }}
        >
          ×
        </button>
      </div>

      {/* 内容区域 */}
      <div style={{
        display: 'flex',
        gap: '2rem',
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* 侧边导航 */}
        <div style={{
          width: '240px',
          flexShrink: 0
        }}>
          <div style={{
            backgroundColor: colors.bgCard,
            borderRadius: '16px',
            overflow: 'hidden',
            boxShadow: theme === 'light' ? '0 8px 32px rgba(0, 0, 0, 0.1)' : '0 8px 32px rgba(0, 0, 0, 0.3)',
            border: `1px solid ${colors.borderSubtle}`
          }}>
            {[
              { id: 'avatar', name: 'Avatar Studio', emoji: '🎨' },
              { id: 'profile', name: 'Profile', emoji: '👤' },
              { id: 'preferences', name: 'Settings', emoji: '⚙️' },
              { id: 'memory', name: 'Memory', emoji: '🧠' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                style={{
                  width: '100%',
                  padding: '1.2rem',
                  textAlign: 'left',
                  backgroundColor: activeTab === tab.id ? colors.primary : 'transparent',
                  border: 'none',
                  borderBottom: `1px solid ${colors.borderSubtle}`,
                  color: activeTab === tab.id ? 'white' : colors.textPrimary,
                  fontWeight: activeTab === tab.id ? '600' : 'normal',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  fontSize: '0.95rem'
                }}
                onMouseEnter={(e) => {
                  if (activeTab !== tab.id) {
                    e.currentTarget.style.backgroundColor = colors.bgSubtle;
                    e.currentTarget.style.transform = 'translateX(4px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (activeTab !== tab.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.transform = 'translateX(0px)';
                  }
                }}
              >
                <span style={{ fontSize: '1.2rem' }}>{tab.emoji}</span>
                {tab.name}
              </button>
            ))}
          </div>
        </div>

        {/* 主内容区 */}
        <div style={{
          flex: 1,
          backgroundColor: colors.bgCard,
          borderRadius: '16px',
          padding: '1.5rem 2.5rem 2.5rem 2.5rem',
          boxShadow: theme === 'light' ? '0 8px 32px rgba(0, 0, 0, 0.1)' : '0 8px 32px rgba(0, 0, 0, 0.3)',
          border: `1px solid ${colors.borderSubtle}`
        }}>
          {/* Avatar Studio */}
          {activeTab === 'avatar' && (
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1rem'
              }}>
                <h2 style={{ 
                  fontSize: '1.3rem', 
                  margin: 0,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.4rem'
                }}>
                  🎨 Avatar Studio
                </h2>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={resetAvatar}
                    title="Reset to default"
                    style={{
                      backgroundColor: theme === 'dark' ? '#4B5563' : colors.textSecondary,
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1rem',
                      width: '2.5rem',
                      height: '2.5rem'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 6px 18px rgba(0, 0, 0, 0.2)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0px)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                    }}
                  >
                    ↺
                  </button>
                  <button
                    onClick={randomizeAvatar}
                    title="Randomize avatar"
                    style={{
                      backgroundColor: theme === 'dark' ? '#7C3AED' : colors.accent,
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '0.5rem',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1rem',
                      width: '2.5rem',
                      height: '2.5rem'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 6px 18px rgba(0, 0, 0, 0.2)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0px)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                    }}
                  >
                    🎲
                  </button>
                </div>
              </div>
              
                            {/* 左右布局：头像预览 + 定制选项 */}
              <div style={{
                display: 'flex',
                gap: '1.5rem',
                marginBottom: '1.5rem',
                alignItems: 'flex-start'
              }}>
                {/* 左侧：头像预览区域 */}
                <div style={{ 
                  width: '280px',
                  flexShrink: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  background: `linear-gradient(135deg, ${colors.bgElevated}, ${colors.bgSubtle})`,
                  borderRadius: '16px',
                  padding: '1.5rem',
                  border: `2px solid ${colors.borderSubtle}`,
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  {/* 背景装饰 */}
                  <div style={{
                    position: 'absolute',
                    top: '-50%',
                    left: '-50%',
                    width: '200%',
                    height: '200%',
                    background: `radial-gradient(circle, ${colors.primary}10 0%, transparent 50%)`,
                    animation: 'float 6s ease-in-out infinite'
                  }}></div>
                  
                  {/* Doraemon 角色展示 */}
                  <DoraemonAvatar options={avatarOptions} size={160} />
                  
                  {/* 经验条 */}
                  <div style={{
                    width: '100%',
                    maxWidth: '220px',
                    marginTop: '1rem'
                  }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '0.5rem'
                    }}>
                      <span style={{ fontSize: '0.9rem', fontWeight: '600', color: colors.textSecondary }}>
                        🌟 Level {userLevel}
                      </span>
                      <span style={{ fontSize: '1rem', fontWeight: 'bold', color: colors.primary }}>
                        {currentExp}/{expToNextLevel} XP
                      </span>
                    </div>
                    <div style={{
                      width: '100%',
                      height: '12px',
                      backgroundColor: colors.bgMain,
                      borderRadius: '6px',
                      overflow: 'hidden',
                      border: `2px solid ${colors.borderSubtle}`,
                      position: 'relative'
                    }}>
                      <div style={{
                        width: `${expProgress}%`,
                        height: '100%',
                        background: `linear-gradient(90deg, ${colors.primary}, ${colors.accent})`,
                        transition: 'width 0.5s ease',
                        borderRadius: '4px',
                        boxShadow: `0 0 10px ${colors.primary}40`
                      }}></div>
                      {/* 经验条光效 */}
                      <div style={{
                        position: 'absolute',
                        top: '0',
                        left: '0',
                        width: `${expProgress}%`,
                        height: '100%',
                        background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%)',
                        animation: 'shimmer 2s infinite'
                      }}></div>
                    </div>
                  </div>

                  {/* D-Pal服务风格选择 */}
                  <div style={{
                    marginTop: '1rem',
                    width: '100%'
                  }}>
                    <h4 style={{ 
                      fontSize: '1rem', 
                      marginBottom: '0.8rem', 
                      color: colors.textPrimary,
                      fontWeight: '600',
                      textAlign: 'center'
                    }}>
                      🤖 Service Style
                    </h4>
                    <div style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '0.5rem'
                    }}>
                                              <div
                        onClick={() => setServiceStyle('mentor')}
                        style={{
                          flex: 1,
                          padding: '0.5rem',
                          backgroundColor: serviceStyle === 'mentor' ? colors.primary : colors.bgElevated,
                          borderRadius: '6px',
                          cursor: 'pointer',
                          transition: 'all 0.3s ease',
                          border: `2px solid ${serviceStyle === 'mentor' ? colors.primary : 'transparent'}`,
                          textAlign: 'center'
                        }}
                      >
                        <div style={{ fontSize: '1rem', marginBottom: '0.1rem' }}>🎓</div>
                        <div style={{ 
                          fontWeight: 'bold', 
                          fontSize: '0.7rem',
                          color: serviceStyle === 'mentor' ? 'white' : colors.textPrimary
                        }}>
                          Mentor
                  </div>
                </div>
                      
                      <div
                        onClick={() => setServiceStyle('secretary')}
                        style={{
                          flex: 1,
                          padding: '0.5rem',
                          backgroundColor: serviceStyle === 'secretary' ? colors.primary : colors.bgElevated,
                          borderRadius: '6px',
                          cursor: 'pointer',
                          transition: 'all 0.3s ease',
                          border: `2px solid ${serviceStyle === 'secretary' ? colors.primary : 'transparent'}`,
                          textAlign: 'center'
                        }}
                      >
                        <div style={{ fontSize: '1rem', marginBottom: '0.1rem' }}>💼</div>
                        <div style={{ 
                          fontWeight: 'bold', 
                          fontSize: '0.7rem',
                          color: serviceStyle === 'secretary' ? 'white' : colors.textPrimary
                        }}>
                          Secretary
                        </div>
                      </div>
                    </div>
                  </div>


                </div>
                
                                <div style={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '0.8rem'
                }}>
                  {Object.entries(customizationOptions).map(([category, options]) => (
                    <div key={category}>
                      <h4 style={{ 
                        fontSize: '0.8rem', 
                        marginBottom: '0.5rem', 
                        color: colors.textPrimary,
                        fontWeight: '600',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.4rem'
                      }}>
                        {category === 'headgear' && '👑'} 
                        {category === 'eyes' && '👀'} 
                        {category === 'mouth' && '😊'} 
                        {category === 'necklace' && '💎'} 
                        {category === 'pattern' && '🎨'} 
                        {category === 'color' && '🌈'} 
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                        <span style={{
                          fontSize: '0.7rem',
                          backgroundColor: colors.bgSubtle,
                          color: colors.textTertiary,
                          padding: '2px 6px',
                          borderRadius: '8px'
                        }}>
                          {options.length}
                        </span>
                      </h4>
                      <div style={{ 
                        display: 'grid', 
                        gridTemplateColumns: 'repeat(auto-fill, 30px)',
                        gap: '0.2rem'
                      }}>
                        {options.map((option) => (
                          <CustomizationCard
                            key={option.id}
                            option={option}
                            isSelected={avatarOptions[category as keyof typeof avatarOptions] === option.id}
                            onClick={() => updateAvatarOption(category, option.id)}
                            colors={colors}
                            theme={theme}
                            category={category}
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
                            {/* 保存按钮 */}
                <div style={{ 
                  display: 'flex', 
                justifyContent: 'center', 
                marginTop: '1.5rem' 
                }}>
                <button
                  onClick={() => onClose(avatarOptions)}
                  style={{
                    backgroundColor: colors.primary,
                    color: 'white',
                    border: 'none',
                    borderRadius: '16px',
                    padding: '1rem 2.5rem',
                    fontWeight: '600',
                    fontSize: '1.1rem',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    boxShadow: `0 6px 20px ${colors.primary}40`,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-3px)';
                    e.currentTarget.style.boxShadow = `0 8px 25px ${colors.primary}60`;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0px)';
                    e.currentTarget.style.boxShadow = `0 6px 20px ${colors.primary}40`;
                  }}
                >
                                    💾 Save My D-Pal
                  </button>
              </div>

              {/* 经验获取记录 - 独立板块 */}
              <div style={{
                backgroundColor: colors.bgCard,
                borderRadius: '16px',
                padding: '1.5rem',
                marginTop: '1.5rem',
                boxShadow: theme === 'light' ? '0 4px 16px rgba(0, 0, 0, 0.1)' : '0 4px 16px rgba(0, 0, 0, 0.3)',
                border: `1px solid ${colors.borderSubtle}`
              }}>
                <h3 style={{ 
                  fontSize: '1.1rem', 
                  marginBottom: '1rem', 
                  color: colors.textPrimary,
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  📈 Recent Experience Gains
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '0.75rem'
                }}>
                  {expGains.map((gain) => (
                    <div
                      key={gain.id}
                      style={{
                        backgroundColor: colors.bgElevated,
                        borderRadius: '8px',
                        padding: '0.75rem',
                        border: `1px solid ${colors.borderSubtle}`,
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-1px)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0px)';
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    >
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '0.5rem'
                      }}>
                        <span style={{ 
                          fontWeight: '700', 
                          color: colors.primary,
                          fontSize: '0.9rem'
                        }}>
                          +{gain.exp} XP
                        </span>
                        <span style={{ 
                          color: colors.textTertiary, 
                          fontSize: '0.75rem'
                        }}>
                          {gain.time}
                        </span>
                      </div>
                      <div style={{ 
                        color: colors.textPrimary,
                        fontSize: '0.85rem',
                        lineHeight: '1.3',
                        fontWeight: '500'
                      }}>
                        {gain.task}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* 其他标签页内容保持不变... */}
          {activeTab === 'profile' && (
            <div>
              <h2 style={{ fontSize: '1.25rem', marginBottom: '1.5rem' }}>Personal Information</h2>
              
              {/* 头像区域 */}
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                marginBottom: '2rem' 
              }}>
                <div style={{ 
                  width: '80px', 
                  height: '80px', 
                  borderRadius: '50%', 
                  backgroundColor: colors.primary,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#fff',
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  marginRight: '1.5rem',
                  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                  transition: 'background-color 0.3s ease',
                  overflow: 'hidden',
                  backgroundImage: avatar !== '/avatar.png' ? `url(${avatar})` : 'none',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}>
                  {avatar === '/avatar.png' ? 'D' : ''}
                </div>
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarUpload}
                    style={{ display: 'none' }}
                    id="avatar-upload"
                  />
                  <button 
                    onClick={() => document.getElementById('avatar-upload')?.click()}
                    style={{
                      backgroundColor: 'transparent',
                      border: `1px solid ${colors.borderStrong}`,
                      borderRadius: '6px',
                      padding: '0.5rem 1rem',
                      color: colors.textPrimary,
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      marginBottom: '0.5rem',
                      display: 'block'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgSubtle;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    📷 Change avatar
                  </button>
                  <div style={{
                    fontSize: '0.75rem',
                    color: colors.textTertiary,
                    lineHeight: '1.3'
                  }}>
                    Upload JPG, PNG or GIF<br />
                    Max size: 5MB
                  </div>
                </div>
              </div>
              
              {/* 表单字段 */}
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>
                {[
                  { label: 'Name', value: name },
                  { label: 'Email', value: email },
                  { label: 'Position', value: role }
                ].map((field, index) => (
                  <div key={index} style={{ marginBottom: '1.5rem' }}>
                    <label style={{ 
                      display: 'block', 
                      fontSize: '0.875rem', 
                      color: colors.textSecondary,
                      marginBottom: '0.5rem' 
                    }}>
                      {field.label}
                    </label>
                    <input
                      type="text"
                      value={field.value}
                      readOnly
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        borderRadius: '6px',
                        border: `1px solid ${colors.borderSubtle}`,
                        backgroundColor: colors.bgSubtle,
                        color: colors.textSecondary,
                        fontSize: '0.875rem',
                        cursor: 'not-allowed'
                      }}
                    />
                  </div>
                ))}
              </div>
              
              <div style={{
                backgroundColor: colors.bgElevated,
                padding: '1rem',
                borderRadius: '8px',
                border: `1px solid ${colors.borderSubtle}`,
                marginTop: '1.5rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: colors.textSecondary,
                  fontSize: '0.875rem'
                }}>
                  <span>ℹ️</span>
                  <span>Profile information is managed by the HR system and cannot be modified here.</span>
                </div>
              </div>
            </div>
          )}
          
          {/* 偏好设置 */}
          {activeTab === 'preferences' && (
            <div>
              <h2 style={{ fontSize: '1.25rem', marginBottom: '1.5rem' }}>Preferences</h2>
              
              {/* 通知设置卡片 */}
              <div style={{
                backgroundColor: colors.bgCard,
                border: `1px solid ${colors.borderSubtle}`,
                borderRadius: '12px',
                padding: '1.5rem',
                marginBottom: '2rem'
              }}>
                <h3 style={{ fontSize: '1rem', marginBottom: '1rem', color: colors.textSecondary, display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  🔔 Notifications
                </h3>
                {[
                  { id: 'notifications', label: 'Allow Notifications', description: 'Receive notifications from Doraemon' },
                  { id: 'desktopNotifications', label: 'Desktop Notifications', description: 'Show desktop notifications' }
                ].map((setting) => (
                  <div key={setting.id} style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    padding: '0.75rem 0',
                    borderBottom: `1px solid ${colors.borderSubtle}`
                  }}>
                    <div>
                      <div style={{ fontWeight: '500' }}>{setting.label}</div>
                      <div style={{ fontSize: '0.875rem', color: colors.textTertiary }}>{setting.description}</div>
                    </div>
                    <div 
                      onClick={() => handleTogglePreference(setting.id)}
                      style={{
                        width: '48px',
                        height: '24px',
                        backgroundColor: preferences[setting.id as keyof typeof preferences] ? colors.primary : colors.bgSubtle,
                        borderRadius: '12px',
                        position: 'relative',
                        cursor: 'pointer',
                        transition: 'background-color 0.2s'
                      }}
                    >
                      <div style={{
                        width: '20px',
                        height: '20px',
                        backgroundColor: 'white',
                        borderRadius: '50%',
                        position: 'absolute',
                        top: '2px',
                        left: preferences[setting.id as keyof typeof preferences] ? '26px' : '2px',
                        transition: 'left 0.2s',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                      }}></div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 客户端设置卡片 */}
              <div style={{
                backgroundColor: colors.bgCard,
                border: `1px solid ${colors.borderSubtle}`,
                borderRadius: '12px',
                padding: '1.5rem',
                marginBottom: '2rem'
              }}>
                <h3 style={{ fontSize: '1rem', marginBottom: '1rem', color: colors.textSecondary, display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  💻 Client Settings
                </h3>

                {/* 划词翻译开关 */}
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'space-between',
                  padding: '0.75rem 0',
                  borderBottom: `1px solid ${colors.borderSubtle}`
                }}>
                  <div>
                    <div style={{ fontWeight: '500' }}>Text Selection Translation</div>
                    <div style={{ fontSize: '0.875rem', color: colors.textTertiary }}>Enable translation tools for selected text</div>
                  </div>
                  <div 
                    onClick={() => handleTogglePreference('textSelectionTranslation')}
                    style={{
                      width: '48px',
                      height: '24px',
                      backgroundColor: preferences.textSelectionTranslation ? colors.primary : colors.bgSubtle,
                      borderRadius: '12px',
                      position: 'relative',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s'
                    }}
                  >
                    <div style={{
                      width: '20px',
                      height: '20px',
                      backgroundColor: 'white',
                      borderRadius: '50%',
                      position: 'absolute',
                      top: '2px',
                      left: preferences.textSelectionTranslation ? '26px' : '2px',
                      transition: 'left 0.2s',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                    }}></div>
                  </div>
                </div>

                {/* 工具列表 */}
                <div style={{ margin: '1.5rem 0' }}>
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    marginBottom: '1rem'
                  }}>
                    <h4 style={{ fontSize: '0.9rem', fontWeight: '600', margin: 0, color: colors.textPrimary }}>Available Tools</h4>
                    <button
                      onClick={() => setShowAddToolModal(true)}
                      style={{
                        backgroundColor: colors.primary,
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '0.5rem 0.75rem',
                        fontSize: '0.8rem',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-1px)';
                        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0px)';
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    >
                      ➕ Add Feature
                    </button>
                  </div>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    {allTools.map((tool, index) => (
                      <div key={tool.id}>
                        <div 
                          draggable
                          onDragStart={(e) => handleDragStart(e, index)}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e, index)}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            padding: '0.75rem',
                            backgroundColor: colors.bgElevated,
                            borderRadius: '8px',
                            border: `1px solid ${colors.borderSubtle}`,
                            cursor: 'move',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = colors.bgSubtle;
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = colors.bgElevated;
                          }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', flex: 1 }}>
                            {/* 拖拽手柄 */}
                            <div style={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: '2px',
                              cursor: 'grab',
                              color: colors.textTertiary,
                              padding: '0.25rem'
                            }}>
                              <div style={{ width: '3px', height: '3px', backgroundColor: 'currentColor', borderRadius: '50%' }}></div>
                              <div style={{ width: '3px', height: '3px', backgroundColor: 'currentColor', borderRadius: '50%' }}></div>
                              <div style={{ width: '3px', height: '3px', backgroundColor: 'currentColor', borderRadius: '50%' }}></div>
                              <div style={{ width: '3px', height: '3px', backgroundColor: 'currentColor', borderRadius: '50%' }}></div>
                              <div style={{ width: '3px', height: '3px', backgroundColor: 'currentColor', borderRadius: '50%' }}></div>
                              <div style={{ width: '3px', height: '3px', backgroundColor: 'currentColor', borderRadius: '50%' }}></div>
                            </div>

                            <span style={{ fontSize: '1.2rem' }}>{tool.icon}</span>
                            <div style={{ flex: 1 }}>
                              <div style={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                gap: '0.5rem',
                                fontWeight: '500', 
                                fontSize: '0.9rem' 
                              }}>
                                {tool.name}
                                <span style={{
                                  fontSize: '0.7rem',
                                  padding: '2px 6px',
                                  borderRadius: '4px',
                                  backgroundColor: tool.type === 'default' ? colors.primary + '20' : colors.accent + '20',
                                  color: tool.type === 'default' ? colors.primary : colors.accent,
                                  fontWeight: '500'
                                }}>
                                  {tool.type === 'default' ? 'DEFAULT' : 'USER'}
                                </span>
                              </div>
                              <div style={{ fontSize: '0.8rem', color: colors.textTertiary }}>
                                {tool.description}
                              </div>
                            </div>
                          </div>
                          
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                            <div 
                              onClick={() => handleToggleTool(tool.id)}
                              style={{
                                width: '40px',
                                height: '20px',
                                backgroundColor: tool.enabled ? colors.primary : colors.bgSubtle,
                                borderRadius: '10px',
                                position: 'relative',
                                cursor: 'pointer',
                                transition: 'background-color 0.2s'
                              }}
                            >
                              <div style={{
                                width: '16px',
                                height: '16px',
                                backgroundColor: 'white',
                                borderRadius: '50%',
                                position: 'absolute',
                                top: '2px',
                                left: tool.enabled ? '22px' : '2px',
                                transition: 'left 0.2s',
                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                              }}></div>
                            </div>
                            {tool.type === 'custom' && (
                              <>
                                <button
                                  onClick={() => handleEditTool(tool.id)}
                                  style={{
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    color: colors.textTertiary,
                                    cursor: 'pointer',
                                    padding: '0.25rem',
                                    borderRadius: '4px',
                                    fontSize: '1rem',
                                    transition: 'all 0.2s ease'
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.backgroundColor = colors.primary;
                                    e.currentTarget.style.color = 'white';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.backgroundColor = 'transparent';
                                    e.currentTarget.style.color = colors.textTertiary;
                                  }}
                                  title="Edit this tool"
                                >
                                  ✏️
                                </button>
                                <button
                                  onClick={() => handleDeleteTool(tool.id)}
                                  style={{
                                    backgroundColor: 'transparent',
                                    border: 'none',
                                    color: colors.textTertiary,
                                    cursor: 'pointer',
                                    padding: '0.25rem',
                                    borderRadius: '4px',
                                    fontSize: '1rem',
                                    transition: 'all 0.2s ease'
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.backgroundColor = colors.error;
                                    e.currentTarget.style.color = 'white';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.backgroundColor = 'transparent';
                                    e.currentTarget.style.color = colors.textTertiary;
                                  }}
                                  title="Delete this tool"
                                >
                                  🗑️
                                </button>
                              </>
                            )}
                          </div>
                        </div>

                        {/* 分割线：前三个工具下方显示分割线 */}
                        {index === 2 && (
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            margin: '1rem 0',
                            gap: '1rem'
                          }}>
                            <div style={{
                              flex: 1,
                              height: '1px',
                              backgroundColor: colors.borderSubtle
                            }}></div>
                            <div style={{
                              fontSize: '0.75rem',
                              color: colors.textTertiary,
                              backgroundColor: colors.bgCard,
                              padding: '0.25rem 0.75rem',
                              borderRadius: '12px',
                              border: `1px solid ${colors.borderSubtle}`,
                              whiteSpace: 'nowrap'
                            }}>
                              ↑ Quick Access ↓ More Tools
                            </div>
                            <div style={{
                              flex: 1,
                              height: '1px',
                              backgroundColor: colors.borderSubtle
                            }}></div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 快捷键设置卡片 */}
              <div style={{
                backgroundColor: colors.bgCard,
                border: `1px solid ${colors.borderSubtle}`,
                borderRadius: '12px',
                padding: '1.5rem',
                marginBottom: '2rem'
              }}>
                <h3 style={{ fontSize: '1rem', marginBottom: '1rem', color: colors.textSecondary, display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  ⌨️ Keyboard Shortcuts
                </h3>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {[
                    { key: 'screenshot', label: 'Screenshot', description: 'Capture screenshot of selected area', icon: '📷' }
                  ].map((shortcut) => (
                    <div key={shortcut.key} style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '1rem',
                      backgroundColor: colors.bgElevated,
                      borderRadius: '8px',
                      border: `1px solid ${colors.borderSubtle}`
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                        <span style={{ fontSize: '1.2rem' }}>{shortcut.icon}</span>
                        <div>
                          <div style={{ fontWeight: '500', fontSize: '0.9rem' }}>{shortcut.label}</div>
                          <div style={{ fontSize: '0.8rem', color: colors.textTertiary }}>{shortcut.description}</div>
                        </div>
                      </div>
                      
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                        {editingShortcut === shortcut.key ? (
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                            <input
                              type="text"
                              value={tempShortcut}
                              onChange={(e) => setTempShortcut(e.target.value)}
                              onKeyDown={handleKeyDown}
                              placeholder="Press keys..."
                              style={{
                                padding: '0.5rem',
                                borderRadius: '6px',
                                border: `1px solid ${colors.primary}`,
                                backgroundColor: colors.bgCard,
                                color: colors.textPrimary,
                                fontSize: '0.85rem',
                                outline: 'none',
                                minWidth: '120px',
                                textAlign: 'center'
                              }}
                              autoFocus
                            />
                            <button
                              onClick={() => handleSaveShortcut(shortcut.key)}
                              style={{
                                backgroundColor: colors.primary,
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                padding: '0.25rem 0.5rem',
                                fontSize: '0.75rem',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease'
                              }}
                            >
                              ✓
                            </button>
                            <button
                              onClick={handleCancelEditShortcut}
                              style={{
                                backgroundColor: colors.bgSubtle,
                                color: colors.textSecondary,
                                border: 'none',
                                borderRadius: '4px',
                                padding: '0.25rem 0.5rem',
                                fontSize: '0.75rem',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease'
                              }}
                            >
                              ✕
                            </button>
                          </div>
                        ) : (
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                            <div style={{
                              padding: '0.5rem 0.75rem',
                              backgroundColor: colors.bgSubtle,
                              borderRadius: '6px',
                              border: `1px solid ${colors.borderSubtle}`,
                              fontSize: '0.85rem',
                              fontFamily: 'monospace',
                              color: colors.textPrimary,
                              minWidth: '120px',
                              textAlign: 'center'
                            }}>
                              {shortcuts[shortcut.key as keyof typeof shortcuts]}
                            </div>
                            <button
                              onClick={() => handleEditShortcut(shortcut.key)}
                              style={{
                                backgroundColor: 'transparent',
                                border: `1px solid ${colors.borderSubtle}`,
                                borderRadius: '6px',
                                padding: '0.5rem 0.75rem',
                                fontSize: '0.8rem',
                                cursor: 'pointer',
                                color: colors.textSecondary,
                                transition: 'all 0.2s ease'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = colors.primary;
                                e.currentTarget.style.color = 'white';
                                e.currentTarget.style.borderColor = colors.primary;
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                                e.currentTarget.style.color = colors.textSecondary;
                                e.currentTarget.style.borderColor = colors.borderSubtle;
                              }}
                            >
                              Edit
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <div style={{
                  marginTop: '1rem',
                  padding: '0.75rem',
                  backgroundColor: colors.bgElevated,
                  borderRadius: '6px',
                  border: `1px solid ${colors.borderSubtle}`
                }}>
                  <div style={{
                    fontSize: '0.8rem',
                    color: colors.textTertiary,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    <span>💡</span>
                    <span>Click &ldquo;Edit&rdquo; to modify shortcuts. Press the key combination you want to use, then click ✓ to save.</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Memory 标签页 */}
          {activeTab === 'memory' && (
            <div>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1.5rem'
              }}>
                <h2 style={{ fontSize: '1.25rem', margin: 0 }}>AI Memory</h2>
                <button
                  onClick={clearAllMemories}
                  disabled={memoryEntries.length === 0}
                  style={{
                    backgroundColor: colors.error,
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    cursor: memoryEntries.length > 0 ? 'pointer' : 'not-allowed',
                    opacity: memoryEntries.length > 0 ? 1 : 0.5,
                    transition: 'all 0.2s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                  onMouseEnter={(e) => {
                    if (memoryEntries.length > 0) {
                      e.currentTarget.style.transform = 'translateY(-1px)';
                      e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0px)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  🗑️ Clear All
                </button>
              </div>

              <div style={{
                backgroundColor: colors.bgElevated,
                padding: '1rem',
                borderRadius: '8px',
                border: `1px solid ${colors.borderSubtle}`,
                marginBottom: '1.5rem'
              }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: colors.textSecondary,
                  fontSize: '0.875rem'
                }}>
                  <span>💡</span>
                  <span>AI learns from your interactions to provide better assistance. These memories help personalize responses to your work style and preferences.</span>
                </div>
              </div>

              {memoryEntries.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '3rem 1rem',
                  color: colors.textTertiary
                }}>
                  <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🧠</div>
                  <div style={{ fontSize: '1.1rem', fontWeight: '500', marginBottom: '0.5rem' }}>No memories recorded yet</div>
                  <div style={{ fontSize: '0.875rem' }}>AI will learn from your interactions and record useful insights here.</div>
                </div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                  {memoryEntries.map((entry) => (
                    <div
                      key={entry.id}
                      style={{
                        backgroundColor: colors.bgCard,
                        border: `1px solid ${colors.borderSubtle}`,
                        borderRadius: '8px',
                        padding: '1rem',
                        transition: 'all 0.2s ease',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                        e.currentTarget.style.transform = 'translateY(-1px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.boxShadow = 'none';
                        e.currentTarget.style.transform = 'translateY(0px)';
                      }}
                    >
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        gap: '1rem'
                      }}>
                        <div style={{ flex: 1 }}>
                          <div style={{
                            fontSize: '0.875rem',
                            lineHeight: '1.4',
                            color: colors.textPrimary,
                            marginBottom: '0.5rem'
                          }}>
                            {entry.content}
                          </div>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '1rem',
                            fontSize: '0.75rem',
                            color: colors.textTertiary
                          }}>
                                                         <span style={{
                               backgroundColor: theme === 'light' ? colors.primaryLight : colors.primary + '20',
                               color: theme === 'light' ? colors.primary : colors.accent,
                               padding: '2px 6px',
                               borderRadius: '4px',
                               fontWeight: '500'
                             }}>
                               {entry.category}
                             </span>
                            <span>{entry.timestamp.toLocaleDateString()}</span>
                          </div>
                        </div>
                        <button
                          onClick={() => deleteMemoryEntry(entry.id)}
                          style={{
                            backgroundColor: 'transparent',
                            border: 'none',
                            color: colors.textTertiary,
                            cursor: 'pointer',
                            padding: '0.25rem',
                            borderRadius: '4px',
                            fontSize: '1rem',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = colors.error;
                            e.currentTarget.style.color = 'white';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                            e.currentTarget.style.color = colors.textTertiary;
                          }}
                          title="Delete this memory"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
          
          
        </div>
      </div>

      {/* 添加工具弹窗 */}
      {showAddToolModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: colors.bgCard,
            borderRadius: '12px',
            padding: '2rem',
            width: '90%',
            maxWidth: '500px',
            border: `1px solid ${colors.borderSubtle}`,
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h3 style={{ fontSize: '1.2rem', margin: 0, color: colors.textPrimary }}>Add Custom Tool</h3>
              <button
                onClick={() => {
                  setShowAddToolModal(false);
                  setNewToolName('');
                  setNewToolInstruction('');
                }}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: colors.textSecondary,
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  padding: '0.25rem',
                  borderRadius: '4px',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                ×
              </button>
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{ 
                display: 'block',
                fontSize: '0.9rem',
                fontWeight: '500',
                marginBottom: '0.5rem',
                color: colors.textPrimary
              }}>
                Tool Name
              </label>
              <input
                type="text"
                value={newToolName}
                onChange={(e) => setNewToolName(e.target.value)}
                placeholder="e.g., Code Review, Grammar Check"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgElevated,
                  color: colors.textPrimary,
                  fontSize: '0.9rem',
                  outline: 'none',
                  transition: 'border-color 0.2s ease'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = colors.primary;
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = colors.borderSubtle;
                }}
              />
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <div style={{ 
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '0.5rem'
              }}>
                <label style={{ 
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  color: colors.textPrimary
                }}>
                  Instruction
                </label>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                  <span style={{ 
                    fontSize: '0.75rem', 
                    color: colors.textTertiary,
                    marginRight: '0.25rem'
                  }}>
                    Examples:
                  </span>
                  <button
                    onClick={() => {
                      const template = 'Translate this text: &ldquo;{selectedText}&rdquo;.';
                      setNewToolInstruction(template);
                    }}
                    style={{
                      backgroundColor: colors.bgSubtle,
                      border: `1px solid ${colors.borderSubtle}`,
                      borderRadius: '4px',
                      padding: '0.25rem 0.5rem',
                      fontSize: '0.75rem',
                      cursor: 'pointer',
                      color: colors.textSecondary,
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.primary;
                      e.currentTarget.style.color = 'white';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgSubtle;
                      e.currentTarget.style.color = colors.textSecondary;
                    }}
                  >
                    Translate
                  </button>
                  <button
                    onClick={() => {
                      const template = 'Summarize this text: &ldquo;{selectedText}&rdquo;.';
                      setNewToolInstruction(template);
                    }}
                    style={{
                      backgroundColor: colors.bgSubtle,
                      border: `1px solid ${colors.borderSubtle}`,
                      borderRadius: '4px',
                      padding: '0.25rem 0.5rem',
                      fontSize: '0.75rem',
                      cursor: 'pointer',
                      color: colors.textSecondary,
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.primary;
                      e.currentTarget.style.color = 'white';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = colors.bgSubtle;
                      e.currentTarget.style.color = colors.textSecondary;
                    }}
                  >
                    Summary
                  </button>
                </div>
              </div>
              <textarea
                value={newToolInstruction}
                onChange={(e) => setNewToolInstruction(e.target.value)}
                placeholder='e.g., Translate this text: &ldquo;{selectedText}&rdquo;.'
                rows={4}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgElevated,
                  color: colors.textPrimary,
                  fontSize: '0.9rem',
                  outline: 'none',
                  transition: 'border-color 0.2s ease',
                  resize: 'vertical',
                  minHeight: '100px'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = colors.primary;
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = colors.borderSubtle;
                }}
              />
              <div style={{
                fontSize: '0.75rem',
                color: colors.textTertiary,
                marginTop: '0.5rem'
              }}>
                Use {'{selectedText}'} to reference the selected text in your instruction.
              </div>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '0.75rem'
            }}>
              <button
                onClick={() => {
                  setShowAddToolModal(false);
                  setNewToolName('');
                  setNewToolInstruction('');
                }}
                style={{
                  backgroundColor: 'transparent',
                  border: `1px solid ${colors.borderSubtle}`,
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  color: colors.textSecondary,
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleAddCustomTool}
                style={{
                  backgroundColor: colors.primary,
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  color: 'white',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0px)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                Add Tool
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑工具弹窗 */}
      {editingTool && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: colors.bgCard,
            borderRadius: '12px',
            padding: '2rem',
            width: '90%',
            maxWidth: '500px',
            border: `1px solid ${colors.borderSubtle}`,
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h3 style={{ fontSize: '1.2rem', margin: 0, color: colors.textPrimary }}>Edit Custom Tool</h3>
              <button
                onClick={handleCancelEdit}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: colors.textSecondary,
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  padding: '0.25rem',
                  borderRadius: '4px',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                ×
              </button>
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{ 
                display: 'block',
                fontSize: '0.9rem',
                fontWeight: '500',
                marginBottom: '0.5rem',
                color: colors.textPrimary
              }}>
                Tool Name
              </label>
              <input
                type="text"
                value={editToolName}
                onChange={(e) => setEditToolName(e.target.value)}
                placeholder="e.g., Code Review, Grammar Check"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgElevated,
                  color: colors.textPrimary,
                  fontSize: '0.9rem',
                  outline: 'none',
                  transition: 'border-color 0.2s ease'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = colors.primary;
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = colors.borderSubtle;
                }}
              />
            </div>

            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{ 
                fontSize: '0.9rem',
                fontWeight: '500',
                color: colors.textPrimary,
                display: 'block',
                marginBottom: '0.5rem'
              }}>
                Instruction
              </label>
              <textarea
                value={editToolInstruction}
                onChange={(e) => setEditToolInstruction(e.target.value)}
                placeholder='e.g., Translate this text: "{selectedText}".'
                rows={4}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: `1px solid ${colors.borderSubtle}`,
                  backgroundColor: colors.bgElevated,
                  color: colors.textPrimary,
                  fontSize: '0.9rem',
                  outline: 'none',
                  transition: 'border-color 0.2s ease',
                  resize: 'vertical',
                  minHeight: '100px'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = colors.primary;
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = colors.borderSubtle;
                }}
              />
              <div style={{
                fontSize: '0.75rem',
                color: colors.textTertiary,
                marginTop: '0.5rem'
              }}>
                Use {'{selectedText}'} to reference the selected text in your instruction.
              </div>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '0.75rem'
            }}>
              <button
                onClick={handleCancelEdit}
                style={{
                  backgroundColor: 'transparent',
                  border: `1px solid ${colors.borderSubtle}`,
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  color: colors.textSecondary,
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.bgSubtle;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleSaveEdit}
                style={{
                  backgroundColor: colors.primary,
                  border: 'none',
                  borderRadius: '8px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.9rem',
                  cursor: 'pointer',
                  color: 'white',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0px)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS动画 */}
      <style jsx>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        @keyframes float {
          0%, 100% { transform: rotate(0deg) scale(1); }
          50% { transform: rotate(180deg) scale(1.1); }
        }
      `}</style>
    </div>
  );
} 