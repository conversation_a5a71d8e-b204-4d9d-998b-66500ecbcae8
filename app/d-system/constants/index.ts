import React from 'react';
import { ChatHistoryGroup } from '../types';

/**
 * 页面基础样式常量
 * 提供页面主要布局的CSS样式对象
 */
export const PAGE_STYLES = {
  container: {
    display: 'flex',
    minHeight: '100vh',
    backgroundColor: '#181c20',
    color: '#ffffff',
  } as React.CSSProperties,
  sidebar: {
    width: '16rem',
    backgroundColor: '#181c20',
    borderRight: '1px solid #23272e',
    display: 'flex',
    flexDirection: 'column' as 'column',
  } as React.CSSProperties,
  content: {
    flex: 1,
  } as React.CSSProperties,
};

/**
 * 默认聊天历史分组数据
 * 按时间维度分组的聊天记录结构
 */
export const DEFAULT_CHAT_HISTORY: ChatHistoryGroup[] = [
  {
    label: 'Today',
    chats: [
      { id: 'recent1', title: 'AI Assistant Help', hasNewMessage: false },
      { id: 'c1', title: 'Data Analysis Project', hasNewMessage: true }
    ]
  },
  {
    label: 'Yesterday',
    chats: [
      { id: 'recent2', title: 'Marketing Campaign Ideas', hasNewMessage: false },
      { id: 'c2', title: 'Project Planning', hasNewMessage: false }
    ]
  },
  {
    label: 'This Week',
    chats: [
      { id: 'recent3', title: 'Budget Review', hasNewMessage: false },
      { id: 'older1', title: 'Team Performance Review', hasNewMessage: false }
    ]
  },
  {
    label: 'Earlier',
    chats: [
      { id: 'older2', title: 'IT System Upgrade Plan', hasNewMessage: false },
      { id: 'older3', title: 'Marketing Strategy Draft', hasNewMessage: false }
    ]
  }
];

/**
 * 主题颜色生成函数
 * 根据主题模式返回对应的颜色配置
 * @param theme 主题模式 ('light' | 'dark')
 * @returns 颜色配置对象
 */
export const getThemeColors = (theme: 'light' | 'dark') => ({
  bgMain: theme === 'light' ? '#F9FAFB' : '#0F172A',
  bgCard: theme === 'light' ? '#FFFFFF' : '#1E293B',
  bgElevated: theme === 'light' ? '#F1F5F9' : '#334155',
  bgSubtle: theme === 'light' ? '#F3F4F6' : '#1F2937',
  textPrimary: theme === 'light' ? '#111827' : '#F9FAFB',
  textSecondary: theme === 'light' ? '#4B5563' : '#E5E7EB',
  textTertiary: theme === 'light' ? '#9CA3AF' : '#9CA3AF',
  borderSubtle: theme === 'light' ? '#E5E7EB' : '#334155',
  borderStrong: theme === 'light' ? '#D1D5DB' : '#475569',
  primary: theme === 'light' ? '#2563EB' : '#3B82F6',
  primaryLight: '#60A5FA',
  accent: '#8B5CF6',
  success: '#10B981',
  error: '#EF4444'
});

/**
 * Doraemon头像默认配置
 * 定义头像的默认外观设置
 */
export const DEFAULT_AVATAR_CONFIG = {
  headgear: 'default',
  eyes: 'normal',
  mouth: 'smile',
  necklace: 'bell',
  pattern: 'classic',
  color: 'blue',
  experience: 0
};

/**
 * 响应式断点常量
 * 定义移动端和桌面端的断点像素值
 */
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1200
} as const;

/**
 * 动画持续时间常量
 * 定义UI动画的标准持续时间（毫秒）
 */
export const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
  MESSAGE_DELAY: 1500
} as const;

/**
 * 菜单位置常量
 * 定义下拉菜单和弹窗的初始位置
 */
export const MENU_POSITION = {
  DEFAULT: { top: 0, left: 0 }
} as const; 