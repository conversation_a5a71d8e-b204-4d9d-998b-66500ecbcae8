'use client'
import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Button, Dropdown, Switch, Divider, message, Modal, Select, Spin, Card, Input } from 'antd'
import { useChat, useCompletion } from 'ai/react'
import type { MenuProps } from 'antd'
import { 
  TranslationOutlined, 
  QuestionCircleOutlined, 
  FileTextOutlined, 
  CopyOutlined,
  MoreOutlined,
  SettingOutlined,
  EyeInvisibleOutlined,
  BookOutlined,
  StarOutlined,
  ShareAltOutlined,
  HighlightOutlined,
  FontSizeOutlined,
  FormatPainterOutlined,
  CloseOutlined,
  SoundOutlined,
  SwapOutlined,
  PushpinOutlined,
  HolderOutlined
} from '@ant-design/icons'

interface SelectionPosition {
  x: number
  y: number
  width: number
  height: number
  endX?: number
  endY?: number
  isBackward?: boolean
  dragX?: number
  dragY?: number
}

export default function TextSelectionDemo() {
  const [selectedText, setSelectedText] = useState('')
  const [showMenu, setShowMenu] = useState(false)
  const [menuPosition, setMenuPosition] = useState<SelectionPosition>({ x: 0, y: 0, width: 0, height: 0 })
  const [isMiniMode, setIsMiniMode] = useState(false)
  const [showMiniDot, setShowMiniDot] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [showTransitionBalls, setShowTransitionBalls] = useState(false)
  const [balls, setBalls] = useState<Array<{
    id: number
    x: number
    y: number
    color: string
    scale: number
    opacity: number
    controlPoints?: Array<{x: number, y: number}>
  }>>([])
  const [showFusionEffect, setShowFusionEffect] = useState(false)
  const [showTranslateModal, setShowTranslateModal] = useState(false)
  const [isTranslatePinned, setIsTranslatePinned] = useState(false)
  const [translatePosition, setTranslatePosition] = useState({ x: 0, y: 0 })
  const [isDraggingTranslate, setIsDraggingTranslate] = useState(false)
  const [translateDragOffset, setTranslateDragOffset] = useState({ x: 0, y: 0 })
  
  // 解释功能相关状态
  const [showExplainModal, setShowExplainModal] = useState(false)
  const [isExplainPinned, setIsExplainPinned] = useState(false)
  const [explainPosition, setExplainPosition] = useState({ x: 0, y: 0 })
  const [isDraggingExplain, setIsDraggingExplain] = useState(false)
  const [explainDragOffset, setExplainDragOffset] = useState({ x: 0, y: 0 })
  
  // 总结功能相关状态
  const [showSummaryModal, setShowSummaryModal] = useState(false)
  const [isSummaryPinned, setIsSummaryPinned] = useState(false)
  const [summaryPosition, setSummaryPosition] = useState({ x: 0, y: 0 })
  const [isDraggingSummary, setIsDraggingSummary] = useState(false)
  const [summaryDragOffset, setSummaryDragOffset] = useState({ x: 0, y: 0 })
  
  // 快捷键相关状态
  const [isAltPressed, setIsAltPressed] = useState(false)
  const [keyLabel, setKeyLabel] = useState('Alt') // 默认显示 Alt，避免水合错误
  
  // 客户端检测操作系统
  useEffect(() => {
    const isMac = /Mac|iPhone|iPad|iPod/.test(navigator.platform)
    setKeyLabel(isMac ? 'Opt' : 'Alt')
  }, [])

  const textRef = useRef<HTMLDivElement>(null)
  const selectedTextRef = useRef(selectedText)
  const handleActionRef = useRef<(action: string) => void>()
  
  // 更新 ref
  useEffect(() => {
    selectedTextRef.current = selectedText
  }, [selectedText])

  const demoText = `
    This is a demonstration of text selection functionality. You can select any portion of this text to see the context menu in action. 
    
    The system provides various options including translation, explanation, summarization, and copying capabilities. 
    
    When mini mode is enabled, the interface becomes more streamlined with a small dot indicator that expands on hover to show all available options.
    
    This feature is designed for enterprise-level applications where efficient text processing and user interaction are crucial for productivity.
  `

  const handleTextSelection = () => {
    // 添加小延迟确保选择完成
    setTimeout(() => {
      const selection = window.getSelection()
      if (selection && selection.toString().trim()) {
        const selectedContent = selection.toString().trim()
        setSelectedText(selectedContent)
        
              const range = selection.getRangeAt(0)
      const rect = range.getBoundingClientRect()
      
      // 获取选择范围的所有矩形
      const rects = range.getClientRects()
      const firstRect = rects[0] || rect
      const lastRect = rects[rects.length - 1] || rect
      
      // 检测划词方向：比较selection的anchor和focus位置
      const isBackward = selection.anchorNode === selection.focusNode 
        ? selection.anchorOffset > selection.focusOffset
        : range.collapsed ? false : !!(selection.anchorNode && selection.focusNode && 
          (selection.anchorNode.compareDocumentPosition(selection.focusNode) & Node.DOCUMENT_POSITION_PRECEDING))
      
      setMenuPosition({
        x: rect.left + rect.width / 2,
        y: rect.top - 10,
        width: rect.width,
        height: rect.height,
        // 根据方向决定使用开始还是结束位置
        endX: isBackward ? firstRect.left : lastRect.right,
        endY: isBackward ? firstRect.top + firstRect.height / 2 : lastRect.top + lastRect.height / 2,
        isBackward: isBackward,
        dragX: rect.left + rect.width / 2,
        dragY: rect.top - 10
      })
        
        if (isMiniMode) {
          setShowMiniDot(true)
          setShowMenu(false)
        } else {
          setShowMenu(true)
          setShowMiniDot(false)
        }
      } else {
        setShowMenu(false)
        setShowMiniDot(false)
        setSelectedText('')
      }
    }, 10)
  }

  const handleAction = useCallback((action: string) => {
    switch (action) {
      case 'translate':
        if (selectedText) {
          setSavedSelectedText(selectedText) // 保存选中的文本
          setMessages([]) // 清空历史聊天消息
          setShowTranslateModal(true)
          setIsTranslatePinned(false)
          // 设置初始位置（屏幕中央）
          setTranslatePosition({
            x: window.innerWidth / 2 - 300,
            y: window.innerHeight / 2 - 200
          })
          // 自动开始翻译
          translateText(`Translate the following text to zh-CN: ${selectedText}`)
        }
        break
      case 'explain':
        if (selectedText) {
          setSavedSelectedText(selectedText) // 保存选中的文本
          setExplainMessages([]) // 清空历史聊天消息
          setShowExplainModal(true)
          setIsExplainPinned(false)
          // 设置初始位置（屏幕中央，稍微偏移避免与翻译弹窗重叠）
          setExplainPosition({
            x: window.innerWidth / 2 - 250,
            y: window.innerHeight / 2 - 150
          })
          // 延迟自动发送解释请求消息
          setTimeout(() => {
            autoExplainText(selectedText)
          }, 200)
        }
        break
      case 'summarize':
        if (selectedText) {
          setSavedSelectedText(selectedText) // 保存选中的文本
          setSummaryMessages([]) // 清空历史聊天消息
          setShowSummaryModal(true)
          setIsSummaryPinned(false)
          // 设置初始位置（屏幕中央，稍微偏移避免与其他弹窗重叠）
          setSummaryPosition({
            x: window.innerWidth / 2 - 200,
            y: window.innerHeight / 2 - 100
          })
          // 延迟自动发送总结请求消息
          setTimeout(() => {
            autoSummarizeText(selectedText)
          }, 200)
        }
        break
      case 'copy':
        navigator.clipboard.writeText(selectedText)
        message.success('Text copied to clipboard')
        break
      case 'settings':
        message.info('Opening settings...')
        break
      case 'hide-current':
        message.warning('Hiding current application')
        return // 不关闭弹窗
      case 'hide-global':
        message.warning('Global hide activated')
        return // 不关闭弹窗
      case 'hide-webpage':
        message.warning('Current webpage hidden')
        return // 不关闭弹窗
      case 'bookmark':
        message.success(`Bookmarked: &quot;${selectedText}&quot;`)
        break
      case 'share':
        message.success(`Sharing: &quot;${selectedText}&quot;`)
        break
      case 'highlight':
        message.success(`Highlighted: &quot;${selectedText}&quot;`)
        break
      case 'style':
        message.info('Text styling options')
        break
      case 'dictionary':
        message.info(`Looking up in dictionary: &quot;${selectedText}&quot;`)
        break
    }
    setShowMenu(false)
    setShowMiniDot(false)
  }, [selectedText])
  
  // 更新 handleAction ref
  useEffect(() => {
    handleActionRef.current = handleAction
  }, [handleAction])

  const moreMenuItems: MenuProps['items'] = [
    {
      key: 'mini-mode',
      label: (
        <div className="flex items-center justify-between w-full">
          <span>Mini Mode</span>
          <Switch 
            size="small" 
            checked={isMiniMode} 
            onChange={(checked) => {
              if (checked && selectedText) {
                // 从普通模式切换到mini模式，播放动画
                playTransitionAnimation()
              } else {
                setIsMiniMode(checked)
                // 切换模式时立刻显示对应界面
                if (selectedText) {
                  if (checked) {
                    setShowMenu(false)
                    setShowMiniDot(true)
                  } else {
                    setShowMiniDot(false)
                    setShowMenu(true)
                  }
                }
              }
            }}
            onClick={(checked, e) => e.stopPropagation()}
          />
        </div>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'settings',
      label: 'Settings',
      icon: <SettingOutlined />,
      onClick: () => handleAction('settings')
    },
    {
      key: 'hide',
      label: 'Hide Options',
      icon: <EyeInvisibleOutlined />,
      children: [
        {
          key: 'hide-current',
          label: 'Hide Current App',
          onClick: () => handleAction('hide-current')
        },
        {
          key: 'hide-webpage',
          label: 'Hide Current Webpage',
          onClick: () => handleAction('hide-webpage')
        },
        {
          key: 'hide-global',
          label: 'Global Hide',
          onClick: () => handleAction('hide-global')
        }
      ]
    }
  ]

  // 键盘事件监听
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 检测 Alt/Option 键
      if (event.altKey && !isAltPressed) {
        setIsAltPressed(true)
      }
      
      // 快捷键处理
      if (event.altKey && (showMenu || showMiniDot) && selectedTextRef.current) {
        const currentText = selectedTextRef.current
        switch (event.key) {
          case '1':
            event.preventDefault()
            // 通过 ref 调用 handleAction
            handleActionRef.current?.('translate')
            break
          case '2':
            event.preventDefault()
            // 通过 ref 调用 handleAction
            handleActionRef.current?.('explain')
            break
          case '3':
            event.preventDefault()
            // 通过 ref 调用 handleAction
            handleActionRef.current?.('summarize')
            break
        }
      }
    }

    const handleKeyUp = (event: KeyboardEvent) => {
      // Alt 键释放
      if (!event.altKey && isAltPressed) {
        setIsAltPressed(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('keyup', handleKeyUp)
    }
  }, [isAltPressed, showMenu, showMiniDot, selectedText])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      // 检查点击是否在文本区域外，且不是菜单相关元素
      if (textRef.current && !textRef.current.contains(target) && 
          !target.closest('.ant-dropdown') && 
          !target.closest('.ant-dropdown-trigger') &&
          !target.closest('[role="menu"]') &&
          !target.closest('[data-menu-id]')) {
        setShowMenu(false)
        setShowMiniDot(false)
        setSelectedText('')
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [])

  // 拖动相关事件处理
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    const rect = e.currentTarget.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setMenuPosition(prev => ({
        ...prev,
        dragX: e.clientX - dragOffset.x,
        dragY: e.clientY - dragOffset.y
      }))
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, dragOffset])

  // 翻译弹窗拖动相关事件处理
  const handleTranslateMouseDown = (e: React.MouseEvent) => {
    setIsDraggingTranslate(true)
    // 由于拖动条在窗口顶部中央，调整偏移计算
    setTranslateDragOffset({
      x: e.clientX - translatePosition.x,
      y: e.clientY - translatePosition.y
    })
  }

  const handleTranslateMouseMove = (e: MouseEvent) => {
    if (isDraggingTranslate) {
      setTranslatePosition({
        x: e.clientX - translateDragOffset.x,
        y: e.clientY - translateDragOffset.y
      })
    }
  }

  const handleTranslateMouseUp = () => {
    setIsDraggingTranslate(false)
  }

  useEffect(() => {
    if (isDraggingTranslate) {
      document.addEventListener('mousemove', handleTranslateMouseMove)
      document.addEventListener('mouseup', handleTranslateMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleTranslateMouseMove)
        document.removeEventListener('mouseup', handleTranslateMouseUp)
      }
    }
  }, [isDraggingTranslate, translateDragOffset])

  // 解释弹窗拖拽处理
  const handleExplainMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDraggingExplain(true)
    setExplainDragOffset({
      x: e.clientX - explainPosition.x,
      y: e.clientY - explainPosition.y
    })
  }

  const handleExplainMouseMove = (e: MouseEvent) => {
    if (isDraggingExplain) {
      setExplainPosition({
        x: e.clientX - explainDragOffset.x,
        y: e.clientY - explainDragOffset.y
      })
    }
  }

  const handleExplainMouseUp = () => {
    setIsDraggingExplain(false)
  }

  useEffect(() => {
    if (isDraggingExplain) {
      document.addEventListener('mousemove', handleExplainMouseMove)
      document.addEventListener('mouseup', handleExplainMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleExplainMouseMove)
        document.removeEventListener('mouseup', handleExplainMouseUp)
      }
    }
  }, [isDraggingExplain, explainDragOffset])

  // 点击外部关闭翻译弹窗
  useEffect(() => {
    const handleClickOutsideTranslate = (event: MouseEvent) => {
      if (showTranslateModal && !isTranslatePinned) {
        const target = event.target as Element
        if (!target.closest('.translate-floating-modal')) {
          setShowTranslateModal(false)
        }
      }
    }

    if (showTranslateModal) {
      document.addEventListener('click', handleClickOutsideTranslate)
      return () => document.removeEventListener('click', handleClickOutsideTranslate)
    }
  }, [showTranslateModal, isTranslatePinned])

  // 总结弹窗拖拽处理
  const handleSummaryMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDraggingSummary(true)
    setSummaryDragOffset({
      x: e.clientX - summaryPosition.x,
      y: e.clientY - summaryPosition.y
    })
  }

  const handleSummaryMouseMove = (e: MouseEvent) => {
    if (isDraggingSummary) {
      setSummaryPosition({
        x: e.clientX - summaryDragOffset.x,
        y: e.clientY - summaryDragOffset.y
      })
    }
  }

  const handleSummaryMouseUp = () => {
    setIsDraggingSummary(false)
  }

  useEffect(() => {
    if (isDraggingSummary) {
      document.addEventListener('mousemove', handleSummaryMouseMove)
      document.addEventListener('mouseup', handleSummaryMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleSummaryMouseMove)
        document.removeEventListener('mouseup', handleSummaryMouseUp)
      }
    }
  }, [isDraggingSummary, summaryDragOffset])

  // 点击外部关闭解释弹窗
  useEffect(() => {
    const handleClickOutsideExplain = (event: MouseEvent) => {
      if (showExplainModal && !isExplainPinned) {
        const target = event.target as Element
        if (!target.closest('.explain-floating-modal')) {
          setShowExplainModal(false)
        }
      }
    }

    if (showExplainModal) {
      document.addEventListener('click', handleClickOutsideExplain)
      return () => document.removeEventListener('click', handleClickOutsideExplain)
    }
  }, [showExplainModal, isExplainPinned])

  // 点击外部关闭总结弹窗
  useEffect(() => {
    const handleClickOutsideSummary = (event: MouseEvent) => {
      if (showSummaryModal && !isSummaryPinned) {
        const target = event.target as Element
        if (!target.closest('.summary-floating-modal')) {
          setShowSummaryModal(false)
        }
      }
    }

    if (showSummaryModal) {
      document.addEventListener('click', handleClickOutsideSummary)
      return () => document.removeEventListener('click', handleClickOutsideSummary)
    }
  }, [showSummaryModal, isSummaryPinned])

  const [translateData, setTranslateData] = useState({
    targetLanguage: 'zh-CN'
  })
  const [savedSelectedText, setSavedSelectedText] = useState('')

  // AI Chat with useChat hook
  const { messages, input, handleInputChange, handleSubmit, isLoading, setMessages } = useChat({
    api: '/api/text-chat',
    body: {
      selectedText: savedSelectedText
    },
    initialMessages: []
  })

  // 解释功能的AI Chat hook
  const { 
    messages: explainMessages, 
    input: explainInput, 
    handleInputChange: handleExplainInputChange, 
    handleSubmit: handleExplainSubmit, 
    isLoading: isExplainLoading, 
    setMessages: setExplainMessages,
    append: appendExplainMessage
  } = useChat({
    api: '/api/text-chat',
    body: {
      selectedText: savedSelectedText
    },
    initialMessages: []
  })

  // 总结功能的AI Chat hook
  const { 
    messages: summaryMessages, 
    input: summaryInput, 
    handleInputChange: handleSummaryInputChange, 
    handleSubmit: handleSummarySubmit, 
    isLoading: isSummaryLoading, 
    setMessages: setSummaryMessages,
    append: appendSummaryMessage
  } = useChat({
    api: '/api/text-chat',
    body: {
      selectedText: savedSelectedText
    },
    initialMessages: []
  })

  // Translation with useCompletion hook
  const { 
    completion: translationResult, 
    complete: translateText, 
    isLoading: isTranslating 
  } = useCompletion({
    api: '/api/translate'
  })



  const messagesEndRef = useRef<HTMLDivElement>(null)
  const explainMessagesEndRef = useRef<HTMLDivElement>(null)
  const summaryMessagesEndRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const scrollExplainToBottom = () => {
    explainMessagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const scrollSummaryToBottom = () => {
    summaryMessagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, translationResult, isLoading, isTranslating])

  useEffect(() => {
    scrollExplainToBottom()
  }, [explainMessages, isExplainLoading])

  useEffect(() => {
    scrollSummaryToBottom()
  }, [summaryMessages, isSummaryLoading])

  // 执行翻译
  const performTranslation = (targetLang: string) => {
    if (selectedText) {
      setTranslateData({ targetLanguage: targetLang })
      translateText(`Translate the following text to ${targetLang}: ${selectedText}`)
    }
  }

  // 自动执行解释 - 使用append方法但标记为自动消息
  const autoExplainText = async (text: string) => {
    try {
      // 使用append方法自动发送解释请求，添加特殊标记
      await appendExplainMessage({
        role: 'user',
        content: `Please explain the following text in detail: "${text}"`,
        // 添加特殊属性标记这是自动消息
        data: { isAutoMessage: true }
      })
    } catch (error) {
      console.error('Auto explain error:', error)
      message.error('Failed to get explanation from AI')
    }
  }

  // 自动执行总结 - 使用append方法但标记为自动消息
  const autoSummarizeText = async (text: string) => {
    try {
      // 使用append方法自动发送总结请求，添加特殊标记
      await appendSummaryMessage({
        role: 'user',
        content: `Please provide a concise summary of the following text: "${text}"`,
        // 添加特殊属性标记这是自动消息
        data: { isAutoMessage: true }
      })
    } catch (error) {
      console.error('Auto summarize error:', error)
      message.error('Failed to get summary from AI')
    }
  }

  // 语言选项
  const languageOptions = [
    { value: 'zh-CN', label: '简体中文' },
    { value: 'en', label: 'English' },
    { value: 'ja', label: '日本語' },
    { value: 'ko', label: '한국어' },
    { value: 'fr', label: 'Français' },
    { value: 'de', label: 'Deutsch' },
    { value: 'es', label: 'Español' },
  ]

  // 播放多球融合动画
  const playTransitionAnimation = () => {
    const startX = menuPosition.dragX !== undefined ? menuPosition.dragX : menuPosition.x
    const startY = menuPosition.dragY !== undefined ? menuPosition.dragY : menuPosition.y - 60
    const endX = menuPosition.endX ? 
      (menuPosition.isBackward ? menuPosition.endX - 16 : menuPosition.endX + 4) : 
      menuPosition.x + menuPosition.width + 2
    const endY = menuPosition.endY ? 
      (menuPosition.isBackward ? menuPosition.endY - 18 : menuPosition.endY + 16) : 
      menuPosition.y + menuPosition.height - 8

    // 隐藏普通菜单
    setShowMenu(false)
    setShowTransitionBalls(true)
    
    // 创建多个彩色小球
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff']
    const ballCount = 6
    const initialBalls = Array.from({ length: ballCount }, (_, i) => ({
      id: i,
      x: startX + (Math.random() - 0.5) * 120,
      y: startY + (Math.random() - 0.5) * 80,
      color: colors[i % colors.length],
      scale: 0.2 + Math.random() * 0.3, // 开始更小
      opacity: 1,
      // 为每个球预生成飘逸的控制点
      controlPoints: [
        {
          x: startX + (Math.random() - 0.5) * 300,
          y: startY + (Math.random() - 0.5) * 200 - 100
        },
        {
          x: endX + (Math.random() - 0.5) * 200,
          y: endY + (Math.random() - 0.5) * 150 - 75
        },
        {
          x: endX + (Math.random() - 0.5) * 80,
          y: endY + (Math.random() - 0.5) * 60 - 30
        }
      ]
    }))
    
    setBalls(initialBalls)
    
    // 动画参数
    const duration = 1500 // 稍微延长动画时间
    const startTime = Date.now()
    
    // 缓动函数：先慢后快
    const easeInCubic = (t: number) => t * t * t
    
    const animate = () => {
      const elapsed = Date.now() - startTime
      const linearProgress = Math.min(elapsed / duration, 1)
      
      // 更新每个球的位置
      setBalls(prevBalls => 
        prevBalls.map((ball, index) => {
          // 为每个球添加时间偏移，创造波浪效果
          const ballLinearProgress = Math.min(linearProgress + index * 0.08, 1)
          
          // 应用缓动函数，创造逐渐加速效果
          const ballProgress = easeInCubic(ballLinearProgress)
          
          // 使用预生成的控制点创建4阶贝塞尔曲线（更飘逸）
          const t = ballProgress
          const controlPoints = ball.controlPoints || []
          
          // 4点贝塞尔曲线：起点 -> 控制点1 -> 控制点2 -> 控制点3 -> 终点
          const points = [
            { x: ball.x, y: ball.y }, // 起点（球的初始位置）
            controlPoints[0] || { x: ball.x, y: ball.y },         // 第一个控制点
            controlPoints[1] || { x: endX, y: endY },         // 第二个控制点
            controlPoints[2] || { x: endX, y: endY },         // 第三个控制点
            { x: endX, y: endY }     // 终点
          ]
          
          // 5点贝塞尔曲线计算
          const x = Math.pow(1-t, 4) * points[0].x +
                    4 * Math.pow(1-t, 3) * t * points[1].x +
                    6 * Math.pow(1-t, 2) * Math.pow(t, 2) * points[2].x +
                    4 * (1-t) * Math.pow(t, 3) * points[3].x +
                    Math.pow(t, 4) * points[4].x
                    
          const y = Math.pow(1-t, 4) * points[0].y +
                    4 * Math.pow(1-t, 3) * t * points[1].y +
                    6 * Math.pow(1-t, 2) * Math.pow(t, 2) * points[2].y +
                    4 * (1-t) * Math.pow(t, 3) * points[3].y +
                    Math.pow(t, 4) * points[4].y
          
          // 逐渐变大效果
          const initialScale = 0.2 + (index % 3) * 0.1
          const maxScale = 1.2 + (index % 3) * 0.2
          const newScale = initialScale + (maxScale - initialScale) * ballProgress
          
          // 最后阶段的融合效果
          const finalPhase = ballProgress > 0.85
          const fadeProgress = finalPhase ? (ballProgress - 0.85) / 0.15 : 0
          
          return {
            ...ball,
            x,
            y,
            scale: finalPhase ? newScale * (1 - fadeProgress * 0.7) : newScale,
            opacity: finalPhase ? Math.max(0.3, 1 - fadeProgress * 0.7) : 1,
            controlPoints // 保持控制点
          }
        })
      )
      
      if (linearProgress < 1) {
        requestAnimationFrame(animate)
      } else {
        // 显示融合效果
        setShowFusionEffect(true)
        setTimeout(() => {
          setShowTransitionBalls(false)
          setShowFusionEffect(false)
          setIsMiniMode(true)
          setShowMiniDot(true)
        }, 300)
      }
    }
    
    requestAnimationFrame(animate)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">
          Text Selection Demo
        </h1>
        
        <div className="mb-8 p-4 bg-blue-50 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">Instructions:</h2>
          <ul className="text-blue-700 space-y-1">
            <li>• Select any text below to see the context menu</li>
            <li>• Click &quot;More&quot; button to access advanced tools and settings</li>
            <li>• Toggle Mini Mode to show only a small dot after selection</li>
            <li>• In Mini Mode, hover over the dot to see options</li>
            <li>• <strong>Hold {keyLabel} key</strong> to show keyboard shortcuts ({keyLabel}+1/2/3 for Translate/Explain/Summarize)</li>
            <li>• Advanced Tools include bookmarking, sharing, highlighting, and dictionary lookup</li>
          </ul>
        </div>

        <div 
          ref={textRef}
          className="relative text-lg leading-relaxed text-gray-700 select-text"
          onMouseUp={handleTextSelection}
          onMouseDown={() => {
            // 清除之前的菜单状态
            setShowMenu(false)
            setShowMiniDot(false)
          }}
        >
          {demoText.split('\n').map((paragraph, index) => (
            <p key={index} className="mb-4">
              {paragraph.trim()}
            </p>
          ))}
        </div>

        {/* Regular Menu */}
        {showMenu && !isMiniMode && (
          <div 
            className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden"
            style={{
              left: `${menuPosition.dragX !== undefined ? menuPosition.dragX : menuPosition.x}px`,
              top: `${menuPosition.dragY !== undefined ? menuPosition.dragY : menuPosition.y - 60}px`,
              transform: menuPosition.dragX !== undefined ? 'none' : 'translateX(-50%)'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center">
              {/* 拖动条 */}
              <div 
                className="w-1 h-12 bg-gray-300 hover:bg-blue-500 cursor-move transition-colors duration-200 flex-shrink-0"
                onMouseDown={handleMouseDown}
                title="Drag to move"
              />
              
              <div className="flex items-center space-x-2 p-2">
              <Button 
                type="text" 
                size="small" 
                className="text-blue-600 hover:bg-blue-50"
                onClick={() => handleAction('translate')}
              >
                <div className="w-6 h-6 bg-blue-600 text-white rounded flex items-center justify-center text-xs font-bold">
                  D
                </div>
              </Button>
              
              <Button 
                type="text" 
                icon={<TranslationOutlined />} 
                size="small"
                onClick={() => handleAction('translate')}
              >
                <span>Translate</span>
                {isAltPressed && <span className="ml-2 text-xs text-gray-500">{keyLabel}+1</span>}
              </Button>
              
              <Button 
                type="text" 
                icon={<QuestionCircleOutlined />} 
                size="small"
                onClick={() => handleAction('explain')}
              >
                <span>Explain</span>
                {isAltPressed && <span className="ml-2 text-xs text-gray-500">{keyLabel}+2</span>}
              </Button>
              
              <Button 
                type="text" 
                icon={<FileTextOutlined />} 
                size="small"
                onClick={() => handleAction('summarize')}
              >
                <span>Summarize</span>
                {isAltPressed && <span className="ml-2 text-xs text-gray-500">{keyLabel}+3</span>}
              </Button>
              
              <Button 
                type="text" 
                icon={<CopyOutlined />} 
                size="small"
                onClick={() => handleAction('copy')}
              >
                Copy
              </Button>
              
              <Dropdown 
                menu={{ items: moreMenuItems }} 
                trigger={['click']}
                placement="bottomRight"
                getPopupContainer={() => document.body}
              >
                <Button 
                  type="text" 
                  icon={<MoreOutlined />} 
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation() // 阻止事件冒泡
                  }}
                />
              </Dropdown>
              </div>
            </div>
          </div>
        )}

        {/* Mini Mode Dot */}
        {showMiniDot && isMiniMode && (
          <div 
            className="fixed z-50 group"
            style={{
              left: `${menuPosition.endX ? 
                (menuPosition.isBackward ? menuPosition.endX - 16 : menuPosition.endX + 4) : 
                menuPosition.x + menuPosition.width + 2}px`,
              top: `${menuPosition.endY ? 
                (menuPosition.isBackward ? menuPosition.endY - 18 : menuPosition.endY + 16) : 
                menuPosition.y + menuPosition.height - 8}px`,
            }}
          >
            <div className="w-3 h-3 bg-blue-600 rounded-full cursor-pointer hover:bg-blue-700 transition-colors"></div>
            
            {/* Hover Menu */}
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white rounded-lg shadow-lg border border-gray-200 p-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
              <div className="flex flex-col space-y-1">
                <Button 
                  type="text" 
                  size="small" 
                  className="text-blue-600 hover:bg-blue-50 justify-start"
                  onClick={() => handleAction('translate')}
                >
                  <div className="w-4 h-4 bg-blue-600 text-white rounded flex items-center justify-center text-xs font-bold mr-2">
                    D
                  </div>
                  D System
                </Button>
                
                <Button 
                  type="text" 
                  icon={<TranslationOutlined />} 
                  size="small"
                  className="justify-start"
                  onClick={() => handleAction('translate')}
                >
                  <span className="flex items-center w-full">
                    <span>Translate</span>
                    {isAltPressed && <span className="ml-auto text-xs text-gray-400">{keyLabel}+1</span>}
                  </span>
                </Button>
                
                <Button 
                  type="text" 
                  icon={<QuestionCircleOutlined />} 
                  size="small"
                  className="justify-start"
                  onClick={() => handleAction('explain')}
                >
                  <span className="flex items-center w-full">
                    <span>Explain</span>
                    {isAltPressed && <span className="ml-auto text-xs text-gray-400">{keyLabel}+2</span>}
                  </span>
                </Button>
                
                <Button 
                  type="text" 
                  icon={<FileTextOutlined />} 
                  size="small"
                  className="justify-start"
                  onClick={() => handleAction('summarize')}
                >
                  <span className="flex items-center w-full">
                    <span>Summarize</span>
                    {isAltPressed && <span className="ml-auto text-xs text-gray-400">{keyLabel}+3</span>}
                  </span>
                </Button>
                
                <Button 
                  type="text" 
                  icon={<CopyOutlined />} 
                  size="small"
                  className="justify-start"
                  onClick={() => handleAction('copy')}
                >
                  Copy
                </Button>
                
                <Divider className="my-1" />
                
                {/* Mini Mode Switch */}
                <div className="flex items-center justify-between px-3 py-1 hover:bg-gray-50 rounded">
                  <span className="text-sm">Mini Mode</span>
                  <Switch 
                    size="small" 
                    checked={isMiniMode} 
                    onChange={(checked) => {
                      if (checked && selectedText) {
                        // 从普通模式切换到mini模式，播放动画
                        playTransitionAnimation()
                      } else {
                        setIsMiniMode(checked)
                        // 切换模式时立刻显示对应界面
                        if (selectedText) {
                          if (checked) {
                            setShowMenu(false)
                            setShowMiniDot(true)
                          } else {
                            setShowMiniDot(false)
                            setShowMenu(true)
                          }
                        }
                      }
                    }}
                  />
                </div>
                
                <Divider className="my-1" />
                
                {/* Settings */}
                <Button 
                  type="text" 
                  icon={<SettingOutlined />} 
                  size="small"
                  className="justify-start"
                  onClick={() => handleAction('settings')}
                >
                  Settings
                </Button>
                
                {/* Hide Options - 使用Menu组件的子菜单 */}
                <div className="relative group/hide">
                  <Button 
                    type="text" 
                    icon={<EyeInvisibleOutlined />} 
                    size="small"
                    className="justify-start w-full"
                  >
                    Hide Options
                  </Button>
                  
                  {/* 子菜单 */}
                  <div className="absolute left-full top-0 ml-1 bg-white rounded-lg shadow-lg border border-gray-200 p-1 opacity-0 invisible group-hover/hide:opacity-100 group-hover/hide:visible transition-all duration-200 whitespace-nowrap z-50 flex flex-col">
                    <Button 
                      type="text" 
                      size="small"
                      className="justify-start w-full text-left px-3 mb-0"
                      onClick={() => handleAction('hide-current')}
                    >
                      Hide Current App
                    </Button>
                    <Button 
                      type="text" 
                      size="small"
                      className="justify-start w-full text-left px-3 mb-0"
                      onClick={() => handleAction('hide-webpage')}
                    >
                      Hide Current Webpage
                    </Button>
                    <Button 
                      type="text" 
                      size="small"
                      className="justify-start w-full text-left px-3 mb-0"
                      onClick={() => handleAction('hide-global')}
                    >
                      Global Hide
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 多彩小球动画 */}
        {showTransitionBalls && balls.map(ball => (
          <div 
            key={ball.id}
            className="fixed z-50 pointer-events-none"
            style={{
              left: `${ball.x}px`,
              top: `${ball.y}px`,
              transform: 'translate(-50%, -50%)'
            }}
          >
            <div 
              className="rounded-full"
              style={{
                width: `${6 + ball.scale * 8}px`,
                height: `${6 + ball.scale * 8}px`,
                backgroundColor: ball.color,
                opacity: ball.opacity,
                transform: `scale(${ball.scale})`,
                boxShadow: `0 0 ${ball.scale * 10}px ${ball.color}40`,
                transition: 'all 0.1s ease-out'
              }}
            />
          </div>
        ))}

        {/* 融合效果 */}
        {showFusionEffect && (
          <div 
            className="fixed z-50 pointer-events-none"
            style={{
              left: `${menuPosition.endX ? 
                (menuPosition.isBackward ? menuPosition.endX - 16 : menuPosition.endX + 4) : 
                menuPosition.x + menuPosition.width + 2}px`,
              top: `${menuPosition.endY ? 
                (menuPosition.isBackward ? menuPosition.endY - 18 : menuPosition.endY + 16) : 
                menuPosition.y + menuPosition.height - 8}px`,
              transform: 'translate(-50%, -50%)'
            }}
          >
            <div 
              className="bg-blue-600 rounded-full"
              style={{
                width: '12px',
                height: '12px',
                animation: 'fusionPulse 0.3s ease-out forwards',
                boxShadow: '0 0 20px #1677ff80'
              }}
            />
          </div>
        )}

        <style jsx>{`
          @keyframes fusionPulse {
            0% {
              transform: scale(2);
              opacity: 0.3;
            }
            50% {
              transform: scale(1.5);
              opacity: 0.8;
            }
            100% {
              transform: scale(1);
              opacity: 1;
            }
          }
          
          @keyframes pinPulse {
            0%, 100% {
              transform: rotate(45deg) scale(1);
            }
            50% {
              transform: rotate(45deg) scale(1.1);
            }
          }
          
          .pin-active {
            animation: pinPulse 2s ease-in-out infinite;
          }
        `}</style>

        {/* 翻译浮动弹窗 */}
        {showTranslateModal && (
          <div 
            className="fixed z-50 bg-white rounded-lg shadow-2xl border border-gray-200 translate-floating-modal"
            style={{
              left: `${translatePosition.x}px`,
              top: `${translatePosition.y}px`,
              width: '600px',
              maxHeight: '600px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 拖动条 - 顶部中央 */}
            <div className="flex justify-center relative">
              <div 
                className="w-16 h-1 bg-gray-300 hover:bg-gray-400 cursor-move transition-colors duration-200 rounded-full mt-2 mb-1"
                onMouseDown={handleTranslateMouseDown}
                title="Drag to move"
              />
            </div>
            
            {/* 标题栏 */}
            <div className="flex items-center bg-gray-50 border-b border-gray-200 px-4 py-2">
              {/* 标题 */}
              <div className="flex-1 flex items-center space-x-2">
                <TranslationOutlined className="text-blue-600" />
                <span className="font-medium">Translation & AI Chat</span>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center space-x-1">
                <Button 
                  type="text" 
                  size="small"
                  icon={<PushpinOutlined className={`${isTranslatePinned ? 'text-blue-600 pin-active' : 'text-gray-500'} transition-colors duration-200`} />}
                  onClick={() => setIsTranslatePinned(!isTranslatePinned)}
                  className={`transition-all duration-200 ${isTranslatePinned ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-100'}`}
                  title={isTranslatePinned ? 'Unpin window' : 'Pin window'}
                />
                <Button 
                  type="text" 
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => setShowTranslateModal(false)}
                  className="text-gray-500 hover:bg-gray-100"
                />
              </div>
            </div>
            {/* 内容区域 */}
            <div className="p-4 h-[480px] flex flex-col">
              {/* AI 对话消息流 */}
              <div className="flex-1 overflow-y-auto bg-gray-50 rounded-lg p-3 space-y-3 min-h-0">
                {/* 原文消息 - 始终显示翻译窗口打开时的选中文本 */}
                <div className="bg-blue-100 text-blue-900 ml-auto max-w-[90%] p-3 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-blue-700">Selected Text</span>
                    <div className="flex space-x-1">
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<SoundOutlined />}
                        className="text-blue-600"
                        title="Play audio"
                      />
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<CopyOutlined />}
                        className="text-blue-600"
                        onClick={() => {
                          if (savedSelectedText) {
                            navigator.clipboard.writeText(savedSelectedText)
                            message.success('Text copied to clipboard')
                          }
                        }}
                        title="Copy text"
                      />
                    </div>
                  </div>
                  <div className="text-sm leading-relaxed">
                    {savedSelectedText || 'No text selected'}
                  </div>
                </div>

                {/* 翻译卡片 - 始终显示 */}
                <div className="bg-white border border-gray-200 p-3 rounded-lg max-w-[90%]">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <TranslationOutlined className="text-blue-600" />
                      <span className="text-xs font-medium text-gray-700">Translation</span>
                    </div>
                    <div className="flex space-x-1">
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<SoundOutlined />}
                        className="text-blue-600"
                        disabled={!translationResult || isTranslating}
                        title="Play translation audio"
                      />
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<CopyOutlined />}
                        className="text-blue-600"
                        disabled={!translationResult || isTranslating}
                        onClick={() => {
                          if (translationResult) {
                            navigator.clipboard.writeText(translationResult)
                            message.success('Translation copied to clipboard')
                          }
                        }}
                        title="Copy translation"
                      />
                    </div>
                  </div>

                  {/* 语言选择 */}
                  <div className="mb-3">
                    <label className="block text-xs text-gray-600 mb-1">Target Language</label>
                    <Select
                      value={translateData.targetLanguage}
                      onChange={(value) => {
                        performTranslation(value)
                      }}
                      className="w-full"
                      size="small"
                      options={languageOptions}
                      showSearch
                      placeholder="Select target language"
                    />
                  </div>
                  
                  {/* 翻译结果 */}
                  {isTranslating ? (
                    <div className="flex items-center space-x-2 py-2 bg-blue-50 rounded border-l-4 border-blue-400 pl-3">
                      <Spin size="small" />
                      <span className="text-sm text-gray-600">Translating...</span>
                    </div>
                  ) : translationResult ? (
                    <div className="text-sm leading-relaxed text-gray-800 bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                      {translationResult}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded border-l-4 border-gray-300">
                      Translation will appear here...
                    </div>
                  )}
                </div>

                {/* AI对话消息 */}
                {messages.map((msg) => (
                  <div 
                    key={msg.id} 
                    className={`p-3 rounded-lg max-w-[90%] ${
                      msg.role === 'user' 
                        ? 'bg-blue-100 text-blue-900 ml-auto' 
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    <div className="text-sm leading-relaxed whitespace-pre-wrap">
                      {msg.content}
                    </div>
                  </div>
                ))}
                
                {isLoading && messages.length === 0 && (
                  <div className="bg-white border border-gray-200 p-3 rounded-lg max-w-[90%]">
                    <div className="flex items-center space-x-2">
                      <Spin size="small" />
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                )}
                
                {isLoading && messages.length > 0 && messages[messages.length - 1].role === 'user' && (
                  <div className="bg-white border border-gray-200 p-3 rounded-lg max-w-[90%]">
                    <div className="flex items-center space-x-2">
                      <Spin size="small" />
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                )}

                {/* 空状态提示 */}
                {!selectedText && messages.length === 0 && !translationResult && !isTranslating && (
                  <div className="text-center text-gray-500 py-8">
                    <p className="text-sm">Select text to start translation and AI chat</p>
                    <p className="text-xs text-gray-400 mt-1">Highlight any text on the page to begin</p>
                  </div>
                )}
                
                {/* 滚动锚点 */}
                <div ref={messagesEndRef} />
              </div>



              {/* 输入框固定在底部 */}
              <form onSubmit={handleSubmit} className="flex items-center space-x-2 mt-4 pt-4 border-t border-gray-200">
                <input
                  type="text"
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask AI about the selected text..."
                  className="flex-1 h-8 px-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isLoading}
                />
                <Button 
                  type="primary" 
                  size="small"
                  loading={isLoading}
                  disabled={!input.trim()}
                  htmlType="submit"
                  className="px-4 h-8"
                >
                  Send
                </Button>
              </form>
            </div>
          </div>
        )}

        {/* 解释浮动弹窗 */}
        {showExplainModal && (
          <div 
            className="fixed z-50 bg-white rounded-lg shadow-2xl border border-gray-200 explain-floating-modal"
            style={{
              left: `${explainPosition.x}px`,
              top: `${explainPosition.y}px`,
              width: '500px',
              maxHeight: '600px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 拖动条 - 顶部中央 */}
            <div className="flex justify-center relative">
              <div 
                className="w-16 h-1 bg-gray-300 hover:bg-gray-400 cursor-move transition-colors duration-200 rounded-full mt-2 mb-1"
                onMouseDown={handleExplainMouseDown}
                title="Drag to move"
              />
            </div>
            
            {/* 标题栏 */}
            <div className="flex items-center bg-gray-50 border-b border-gray-200 px-4 py-2">
              {/* 标题 */}
              <div className="flex-1 flex items-center space-x-2">
                <QuestionCircleOutlined className="text-green-600" />
                <span className="font-medium">Text Explanation & AI Chat</span>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center space-x-1">
                <Button 
                  type="text" 
                  size="small"
                  icon={<PushpinOutlined className={`${isExplainPinned ? 'text-green-600 pin-active' : 'text-gray-500'} transition-colors duration-200`} />}
                  onClick={() => setIsExplainPinned(!isExplainPinned)}
                  className={`transition-all duration-200 ${isExplainPinned ? 'bg-green-50 hover:bg-green-100' : 'hover:bg-gray-100'}`}
                  title={isExplainPinned ? 'Unpin window' : 'Pin window'}
                />
                <Button 
                  type="text" 
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => setShowExplainModal(false)}
                  className="text-gray-500 hover:bg-gray-100"
                />
              </div>
            </div>
            
            {/* 内容区域 */}
            <div className="p-4 h-[480px] flex flex-col">
              {/* AI 对话消息流 */}
              <div className="flex-1 overflow-y-auto bg-gray-50 rounded-lg p-3 space-y-3 min-h-0">
                {/* 原文消息 - 始终显示解释窗口打开时的选中文本 */}
                <div className="bg-green-100 text-green-900 ml-auto max-w-[90%] p-3 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-green-700">Selected Text</span>
                    <div className="flex space-x-1">
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<SoundOutlined />}
                        className="text-green-600"
                        title="Play audio"
                      />
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<CopyOutlined />}
                        className="text-green-600"
                        onClick={() => {
                          if (savedSelectedText) {
                            navigator.clipboard.writeText(savedSelectedText)
                            message.success('Text copied to clipboard')
                          }
                        }}
                        title="Copy text"
                      />
                    </div>
                  </div>
                  <div className="text-sm leading-relaxed">
                    {savedSelectedText || 'No text selected'}
                  </div>
                </div>



                {/* AI对话消息 */}
                {explainMessages
                  .filter((msg) => !(msg.role === 'user' && (msg as any).data?.isAutoMessage))
                  .map((msg) => (
                  <div 
                    key={msg.id} 
                    className={`p-3 rounded-lg max-w-[90%] ${
                      msg.role === 'user' 
                        ? 'bg-green-100 text-green-900 ml-auto' 
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    <div className="text-sm leading-relaxed whitespace-pre-wrap">
                      {msg.content}
                    </div>
                  </div>
                ))}
                
                {isExplainLoading && explainMessages.length === 0 && (
                  <div className="bg-white border border-gray-200 p-3 rounded-lg max-w-[90%]">
                    <div className="flex items-center space-x-2">
                      <Spin size="small" />
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                )}
                
                {isExplainLoading && explainMessages.length > 0 && explainMessages[explainMessages.length - 1].role === 'user' && (
                  <div className="bg-white border border-gray-200 p-3 rounded-lg max-w-[90%]">
                    <div className="flex items-center space-x-2">
                      <Spin size="small" />
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                )}

                {/* 空状态提示 */}
                {!savedSelectedText && explainMessages.length === 0 && !isExplainLoading && (
                  <div className="text-center text-gray-500 py-8">
                    <p className="text-sm">Select text to start explanation and AI chat</p>
                    <p className="text-xs text-gray-400 mt-1">Highlight any text on the page to begin</p>
                  </div>
                )}
                
                {/* 滚动锚点 */}
                <div ref={explainMessagesEndRef} />
              </div>

              {/* 输入框固定在底部 */}
              <form onSubmit={handleExplainSubmit} className="flex items-center space-x-2 mt-4 pt-4 border-t border-gray-200">
                <input
                  type="text"
                  value={explainInput}
                  onChange={handleExplainInputChange}
                  placeholder="Ask AI more about the selected text..."
                  className="flex-1 h-8 px-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  disabled={isExplainLoading}
                />
                <Button 
                  type="primary" 
                  size="small"
                  loading={isExplainLoading}
                  disabled={!explainInput.trim()}
                  htmlType="submit"
                  className="px-4 h-8 bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700"
                >
                  Send
                </Button>
              </form>
            </div>
          </div>
        )}

        {/* 总结浮动弹窗 */}
        {showSummaryModal && (
          <div 
            className="fixed z-50 bg-white rounded-lg shadow-2xl border border-gray-200 summary-floating-modal"
            style={{
              left: `${summaryPosition.x}px`,
              top: `${summaryPosition.y}px`,
              width: '500px',
              maxHeight: '600px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 拖动条 - 顶部中央 */}
            <div className="flex justify-center relative">
              <div 
                className="w-16 h-1 bg-gray-300 hover:bg-gray-400 cursor-move transition-colors duration-200 rounded-full mt-2 mb-1"
                onMouseDown={handleSummaryMouseDown}
                title="Drag to move"
              />
            </div>
            
            {/* 标题栏 */}
            <div className="flex items-center bg-gray-50 border-b border-gray-200 px-4 py-2">
              {/* 标题 */}
              <div className="flex-1 flex items-center space-x-2">
                <FileTextOutlined className="text-purple-600" />
                <span className="font-medium">Text Summary & AI Chat</span>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center space-x-1">
                <Button 
                  type="text" 
                  size="small"
                  icon={<PushpinOutlined className={`${isSummaryPinned ? 'text-purple-600 pin-active' : 'text-gray-500'} transition-colors duration-200`} />}
                  onClick={() => setIsSummaryPinned(!isSummaryPinned)}
                  className={`transition-all duration-200 ${isSummaryPinned ? 'bg-purple-50 hover:bg-purple-100' : 'hover:bg-gray-100'}`}
                  title={isSummaryPinned ? 'Unpin window' : 'Pin window'}
                />
                <Button 
                  type="text" 
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => setShowSummaryModal(false)}
                  className="text-gray-500 hover:bg-gray-100"
                />
              </div>
            </div>
            
            {/* 内容区域 */}
            <div className="p-4 h-[480px] flex flex-col">
              {/* AI 对话消息流 */}
              <div className="flex-1 overflow-y-auto bg-gray-50 rounded-lg p-3 space-y-3 min-h-0">
                {/* 原文消息 - 始终显示总结窗口打开时的选中文本 */}
                <div className="bg-purple-100 text-purple-900 ml-auto max-w-[90%] p-3 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs font-medium text-purple-700">Selected Text</span>
                    <div className="flex space-x-1">
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<SoundOutlined />}
                        className="text-purple-600"
                        title="Play audio"
                      />
                      <Button 
                        type="text" 
                        size="small" 
                        icon={<CopyOutlined />}
                        className="text-purple-600"
                        onClick={() => {
                          if (savedSelectedText) {
                            navigator.clipboard.writeText(savedSelectedText)
                            message.success('Text copied to clipboard')
                          }
                        }}
                        title="Copy text"
                      />
                    </div>
                  </div>
                  <div className="text-sm leading-relaxed">
                    {savedSelectedText || 'No text selected'}
                  </div>
                </div>

                {/* AI对话消息 */}
                {summaryMessages
                  .filter((msg) => !(msg.role === 'user' && (msg as any).data?.isAutoMessage))
                  .map((msg) => (
                  <div 
                    key={msg.id} 
                    className={`p-3 rounded-lg max-w-[90%] ${
                      msg.role === 'user' 
                        ? 'bg-purple-100 text-purple-900 ml-auto' 
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    <div className="text-sm leading-relaxed whitespace-pre-wrap">
                      {msg.content}
                    </div>
                  </div>
                ))}
                
                {isSummaryLoading && summaryMessages.length === 0 && (
                  <div className="bg-white border border-gray-200 p-3 rounded-lg max-w-[90%]">
                    <div className="flex items-center space-x-2">
                      <Spin size="small" />
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                )}
                
                {isSummaryLoading && summaryMessages.length > 0 && summaryMessages[summaryMessages.length - 1].role === 'user' && (
                  <div className="bg-white border border-gray-200 p-3 rounded-lg max-w-[90%]">
                    <div className="flex items-center space-x-2">
                      <Spin size="small" />
                      <span className="text-sm text-gray-600">AI is thinking...</span>
                    </div>
                  </div>
                )}

                {/* 空状态提示 */}
                {!savedSelectedText && summaryMessages.length === 0 && !isSummaryLoading && (
                  <div className="text-center text-gray-500 py-8">
                    <p className="text-sm">Select text to start summary and AI chat</p>
                    <p className="text-xs text-gray-400 mt-1">Highlight any text on the page to begin</p>
                  </div>
                )}
                
                {/* 滚动锚点 */}
                <div ref={summaryMessagesEndRef} />
              </div>

              {/* 输入框固定在底部 */}
              <form onSubmit={handleSummarySubmit} className="flex items-center space-x-2 mt-4 pt-4 border-t border-gray-200">
                <input
                  type="text"
                  value={summaryInput}
                  onChange={handleSummaryInputChange}
                  placeholder="Ask AI more about the summary..."
                  className="flex-1 h-8 px-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  disabled={isSummaryLoading}
                />
                <Button 
                  type="primary" 
                  size="small"
                  loading={isSummaryLoading}
                  disabled={!summaryInput.trim()}
                  htmlType="submit"
                  className="px-4 h-8 bg-purple-600 hover:bg-purple-700 border-purple-600 hover:border-purple-700"
                >
                  Send
                </Button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 