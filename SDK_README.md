# CTW SDK Integration Demo

## 功能介绍

SDK页面是一个演示页面，展示了如何在CTW官网基础上集成AI聊天功能。

## 主要特性

### 1. 网站嵌入
- 通过iframe嵌入CTW官网 (https://ctw.inc/?lang=en)
- 全屏显示，完整保留原网站功能

### 2. AI聊天浮球
- 粉色圆形浮球，位于页面右下角
- 悬停效果和点击动画
- 点击打开/关闭聊天窗口

### 3. AI聊天功能
- 使用AI SDK和OpenRouter API
- 接入Claude-3.5-sonnet模型
- 日式萌妹风格的CTW行政专员角色
- 流式响应，实时显示对话
- **多语言支持**：支持中文、英文、日文三种语言
- **智能语言检测**：自动检测CTW官网的语言设置并同步

## 设置说明

### 1. 环境变量配置

在项目根目录创建 `.env.local` 文件：

```
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### 2. 获取API密钥

1. 访问 [OpenRouter](https://openrouter.ai/keys)
2. 注册账户并创建API密钥
3. 将密钥添加到环境变量中

### 3. 访问页面

启动开发服务器后，访问：
- 导航菜单中的"SDK"选项
- 或直接访问 `/sdk` 路径

## AI角色设定

CTW行政专员拥有以下特征：

### 性格特点
- 使用日式萌妹语气
- 表情丰富，大量使用emoji
- 亲切友好，充满活力

### 知识范围
- CTW公司信息
- G123游戏平台
- 采用情况和业务咨询
- 一般行政事务

### 对话样例

**日文模式：**
```
こんにちは〜！💕 CTWの行政専員です♪ 
お気軽に何でもお聞きくださいね〜✨ 
どんなことでお手伝いできますか？(^o^)/
```

**中文模式：**
```
你好~！💕 我是CTW的行政专员♪ 
有什么都可以问我哦~✨ 
需要什么帮助呢？(^o^)/
```

**英文模式：**
```
Hello~! 💕 I'm CTW's administrative assistant♪ 
Feel free to ask me anything~✨ 
What can I help you with? (^o^)/
```

## 技术实现

### 前端技术栈
- Next.js 14 (App Router)
- React 18
- TypeScript
- AI SDK (@ai-sdk/openai)

### API接口
- OpenRouter API
- Claude-3.5-sonnet模型
- 流式响应支持

### 样式设计
- 粉色主题色 (#FF6B9D)
- 圆角卡片设计
- 响应式布局
- 平滑动画效果

## 使用方法

1. 点击右下角粉色浮球
2. 在聊天窗口中输入消息
3. AI会以CTW行政专员的身份回复
4. 支持多轮对话和上下文理解
5. **语言切换**：
   - 系统会自动检测CTW官网的语言设置
   - 也可以手动使用聊天窗口右上角的语言选择器
   - 切换语言后，AI会立即使用新语言进行回复

## 注意事项

- 需要有效的OpenRouter API密钥
- 网络连接需要能访问OpenRouter服务
- CTW官网需要允许iframe嵌入
- 建议在HTTPS环境下使用

## 故障排除

### 聊天功能不工作
1. 检查API密钥是否正确设置
2. 确认网络连接正常
3. 查看浏览器控制台错误信息

### 网站嵌入失败
1. 检查CTW官网是否可访问
2. 确认没有CORS限制
3. 尝试刷新页面 